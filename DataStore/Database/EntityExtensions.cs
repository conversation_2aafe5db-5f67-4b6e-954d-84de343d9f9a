﻿// <copyright file="EntityExtensions.cs" company="OpenECX">
// Copyright (c) OpenECX. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.EntityFrameworkCore;

namespace DataStore.Database
{
    public static class EntityExtensions
    {
        public static void AddUpdateDeleteCollection<T>(
            this DataStoreContext context,
            List<T> updateItems,
            List<T> oldItems,
            Func<T, T, bool> compair,
            Func<T, List<T>, bool> remove)
        {
            AddUpdateCollection(context, updateItems, oldItems, compair);

            var itemsToRemove = oldItems.Where(o => remove(o, updateItems)).ToList();

            foreach (var item in itemsToRemove)
            {
                context.Entry(item).State = EntityState.Deleted;
            }
        }

        public static void AddUpdateCollection<T>(
            this DataStoreContext context,
            List<T> updateItems,
            List<T> oldItems,
            Func<T, T, bool> compair)
        {
            foreach (var item in updateItems)
            {
                if (oldItems.Any(t => compair(t, item)))
                {
                    context.Entry(item).State = EntityState.Modified;
                }
                else
                {
                    context.Entry(item).State = EntityState.Added;
                }
            }
        }
    }
}
