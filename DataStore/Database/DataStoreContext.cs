﻿// <copyright file="DataStoreContext.cs" company="OpenECX">
// Copyright (c) OpenECX. All rights reserved.
// </copyright>

using System;
using System.Linq;
using DataStore.Database.Tables;
using ETrading.Database.Tables.Customer;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.Extensions.Logging;

namespace DataStore.Database
{
    /// <summary>
    /// Needs to keep this fr migrations, but will rmeove once the database is reseeded.
    /// </summary>
    [Obsolete]
    public class DataStoreContext : DbContext
    {
        // use for migration onlly.
        public DataStoreContext()
            : this("Server=tcp:openecx.database.windows.net,1433;Initial Catalog=eTradingPreProduction;Persist Security Info=False;User ID=openecx;Password=************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;")
        {
        }

        public DataStoreContext(string connectionString)
            : this(new DbContextOptionsBuilder<DataStoreContext>()
                  .UseLoggerFactory(LoggerFactory.Create(builder => builder.AddDebug()))
                  .UseSqlServer(connectionString, x => x.MigrationsHistoryTable("_MigrationHistory", "DataStore")
                  .EnableRetryOnFailure()).Options)
        {
            this.ChangeTracker.LazyLoadingEnabled = false;
        }

        public DataStoreContext(DbContextOptions<DataStoreContext> options)
            : base(options)
        {
            this.ChangeTracker.LazyLoadingEnabled = false;
        }

        public DataStoreContext(string connectionString, bool migration)
            : this(new DbContextOptionsBuilder<DataStoreContext>().UseSqlServer(connectionString,
                opts => opts
                    .CommandTimeout((int)TimeSpan.FromMinutes(60).TotalSeconds)
                    .MigrationsHistoryTable("_MigrationHistory", "DataStore")).Options)
        {
            this.ChangeTracker.LazyLoadingEnabled = false;
        }

        public DbSet<CustomerDetail> CustomerDetail { get; set; }

        public DbSet<TransactionHeader> TransactionHeaders { get; set; }

        public DbSet<TransactionLine> TransactionLines { get; set; }

        public DbSet<TransactionFile> TransactionFiles { get; set; }

        public DbSet<TransactionContactDetail> TransactionContactDetails { get; set; }

        public DbSet<TransactionAddress> TransactionAddresses { get; set; }

        public DbSet<TransactionCustomHeader> TransactionCustomHeaders { get; set; }

        public DbSet<TransactionCustomLine> TransactionCustomLines { get; set; }

        public DbSet<Note> Notes { get; set; }

        public DbSet<TransactionNoteLink> TransactionNoteLinks { get; set; }

        public DbSet<TransactionGroup> TransactionGroups { get; set; }

        public DbSet<TransactionGroupCustomer> TransactionGroupCustomers { get; set; }

        public DbSet<TransactionMatchLink> TransactionLinks { get; set; }

        public DbSet<TransactionMatchAllocation> TransactionMatchAllocations { get; set; }

        public DbSet<TransactionMatchLink> TransactionMatchLinks { get; set; }

        public DbSet<TransactionMatch> TransactionMatchs { get; set; }

        public DbSet<TransactionMatchData> TransactionMatchDatas { get; set; }

        public void MigrateDatabase()
        {
            this.Database.Migrate();
        }

        public void DetachAllEntities()
        {
            var changedEntriesCopy = this.ChangeTracker.Entries()
                .ToList();

            foreach (var entry in changedEntriesCopy)
            {
                entry.State = EntityState.Detached;
            }
        }

        public int SaveChangesAndClearTracking()
        {
            var result = this.SaveChanges();
            DetachAllEntities();
            return result;
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<TransactionAddress>().Property(u => u.ClusteredId).Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore);
            modelBuilder.Entity<TransactionContactDetail>().Property(u => u.ClusteredId).Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore);
            modelBuilder.Entity<TransactionCustomHeader>().Property(u => u.ClusteredId).Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore);
            modelBuilder.Entity<TransactionCustomLine>().Property(u => u.ClusteredId).Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore);
            modelBuilder.Entity<TransactionFile>().Property(u => u.ClusteredId).Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore);
            modelBuilder.Entity<TransactionGroup>().Property(u => u.ClusteredId).Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore);
            modelBuilder.Entity<TransactionHeader>().Property(u => u.ClusteredId).Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore);
            modelBuilder.Entity<TransactionLine>().Property(u => u.ClusteredId).Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore);
            modelBuilder.Entity<TransactionMatch>().Property(u => u.ClusteredId).Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore);
            modelBuilder.Entity<TransactionMatchAllocation>().Property(u => u.ClusteredId).Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore);
            modelBuilder.Entity<TransactionMatchData>().Property(u => u.ClusteredId).Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore);
            modelBuilder.Entity<TransactionMatchLink>().Property(u => u.ClusteredId).Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore);
            modelBuilder.Entity<Note>().Property(u => u.ClusteredId).Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore);

            modelBuilder.Entity<TransactionHeader>(entity =>
            {
                entity.HasKey(e => e.Id)
                    .IsClustered(false)
                    .HasName("PK_TransactionHeader");
                entity.HasIndex(e => e.ClusteredId)
                    .IsUnique()
                    .IsClustered()
                    .HasName("IX_TransactionHeader");
            });

            modelBuilder.Entity<TransactionLine>(entity =>
            {
                entity.HasKey(e => e.Id)
                    .IsClustered(false)
                    .HasName("PK_TransactionLine");
                entity.HasIndex(e => e.ClusteredId)
                    .IsUnique()
                    .IsClustered()
                    .HasName("IX_TransactionLine");
                entity
                    .Property(e => e.LineNumber)
                    .IsRequired();
            });

            modelBuilder.Entity<TransactionAddress>(entity =>
            {
                entity.HasKey(e => e.Id)
                    .IsClustered(false)
                    .HasName("PK_TransactionAddress");
                entity.HasIndex(e => e.ClusteredId)
                    .IsUnique()
                    .IsClustered()
                    .HasName("IX_TransactionAddress");
            });

            modelBuilder.Entity<TransactionContactDetail>(entity =>
            {
                entity.HasKey(e => e.Id)
                    .IsClustered(false)
                    .HasName("PK_TransactionContactDetail");
                entity.HasIndex(e => e.ClusteredId)
                    .IsUnique()
                    .IsClustered()
                    .HasName("IX_TransactionContactDetail");
            });

            modelBuilder.Entity<Note>(entity =>
            {
                entity.HasKey(e => e.Id)
                    .IsClustered(false)
                    .HasName("PK_TransactionNote");
                entity.HasIndex(e => e.ClusteredId)
                    .IsUnique()
                    .IsClustered()
                    .HasName("IX_TransactionNote");
            });

            modelBuilder.Entity<TransactionCustomLine>(entity =>
            {
                entity.HasKey(e => e.Id)
                    .IsClustered(false)
                    .HasName("PK_TransactionCustomLine");
                entity.HasIndex(e => e.ClusteredId)
                    .IsUnique()
                    .IsClustered()
                    .HasName("IX_TransactionCustomLine");
            });

            modelBuilder.Entity<TransactionCustomHeader>(entity =>
            {
                entity.HasKey(e => e.Id)
                    .IsClustered(false)
                    .HasName("PK_TransactionCustomHeader");
                entity.HasIndex(e => e.ClusteredId)
                    .IsUnique()
                    .IsClustered()
                    .HasName("IX_TransactionCustomHeader");
            });

            modelBuilder.Entity<TransactionFile>(entity =>
            {
                entity.HasKey(e => e.Id)
                    .IsClustered(false)
                    .HasName("PK_TransactionFile");
                entity.HasIndex(e => e.ClusteredId)
                    .IsUnique()
                    .IsClustered()
                    .HasName("IX_TransactionFile");
            });

            modelBuilder.Entity<TransactionGroup>(entity =>
            {
                entity.HasKey(e => e.Id)
                    .IsClustered(false)
                    .HasName("PK_TransactionGroup");
                entity.HasIndex(e => e.ClusteredId)
                    .IsUnique()
                    .IsClustered()
                    .HasName("IX_TransactionGroup");
            });

            modelBuilder.Entity<TransactionMatchAllocation>(entity =>
            {
                entity.HasKey(e => e.Id)
                    .IsClustered(false)
                    .HasName("PK_TransactionMatchAllocation");
                entity.HasIndex(e => e.ClusteredId)
                    .IsUnique()
                    .IsClustered()
                    .HasName("IX_TransactionMatchAllocation");
            });

            modelBuilder.Entity<TransactionMatch>(entity =>
            {
                entity.HasKey(e => e.Id)
                    .IsClustered(false)
                    .HasName("PK_TransactionMatch");
                entity.HasIndex(e => e.ClusteredId)
                    .IsUnique()
                    .IsClustered()
                    .HasName("IX_TransactionMatch");
            });

            modelBuilder.Entity<TransactionMatchLink>(entity =>
            {
                entity.HasKey(e => e.Id)
                    .IsClustered(false)
                    .HasName("PK_TransactionMatchLink");
                entity.HasIndex(e => e.ClusteredId)
                    .IsUnique()
                    .IsClustered()
                    .HasName("IX_TransactionMatchLink");
            });

            modelBuilder.Entity<TransactionMatchData>(entity =>
            {
                entity.HasKey(e => e.Id)
                    .IsClustered(false)
                    .HasName("PK_TransactionMatchData");
                entity.HasIndex(e => e.ClusteredId)
                    .IsUnique()
                    .IsClustered()
                    .HasName("IX_TransactionMatchData");
            });

            foreach (var relationship in modelBuilder.Model.GetEntityTypes().SelectMany(e => e.GetForeignKeys()))
            {
                relationship.DeleteBehavior = DeleteBehavior.Restrict;
            }

            // Add indexes for every property decorated with //[IndexColumn]
            // modelBuilder.BuildIndexesFromAnnotationsForSqlServer();
        }
    }
}
