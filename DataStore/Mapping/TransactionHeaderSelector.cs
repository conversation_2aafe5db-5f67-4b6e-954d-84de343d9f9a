﻿// <copyright file="TransactionHeaderSelector.cs" company="OpenECX">
// Copyright (c) OpenECX. All rights reserved.
// </copyright>

using System;
using System.Threading.Tasks;
using ECXCommon.Result;
using DataStore.Database.Tables;
using ETrading.Database.Tables;
using SelectorResult = ECXCommon.Result.DataResult<DataStore.Database.Tables.TransactionHeader>;
using ETrading.Database;

namespace DataStore.Mapping
{
    public class TransactionHeaderSelector : IDatastoreEntitySelector<TransactionHeader>
    {
        public Task<IDataResult<TransactionHeader>> SelectAsync(
            MainContext dbContext,
            Guid transactionHeaderId,
            ITransactionData dataItemModel)
        {
            return Task.FromResult((IDataResult<TransactionHeader>)SelectorResult.Success(
                    new TransactionHeader
                    {
                        Id = transactionHeaderId,
                    }));
        }
    }
}
