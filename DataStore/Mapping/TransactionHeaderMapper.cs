﻿// <copyright file="TransactionHeaderMapper.cs" company="OpenECX">
// Copyright (c) OpenECX. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using DataStore.Database.Tables;
using DataStore.Models;
using DataStore.ViewModels;
using ETrading.Database.Tables;
using ETradingEF.Enums;

namespace DataStore.Mapping
{
    public class TransactionHeaderMapper : BaseEntityMapperDuplicateFields<TransactionHeader>, IDatastoreEntityMapper<TransactionHeader>
    {
        // Warning: take care not to introduce duplicate field names, or property names
        public static readonly IReadOnlyCollection<IDatastoreFieldMapping<TransactionHeader>> HeaderFieldMappings =
            new IDatastoreFieldMapping<TransactionHeader>[]
            {
                    new DatastoreFieldMapping<TransactionHeader>(
                        "default_vat_rate",
                        (TransactionHeader th, string value) =>
                        {
                            // TODO add dynamic model
                            if (decimal.TryParse(value, out var dfr))
                            {
                                th.DefaultVatRate = dfr;
                            }
                        },
                        (TransactionHeader th) => DataItemValueModel.Create(th.DefaultVatRate),
                        nameof(TransactionHeader.DefaultVatRate),
                        t => t.DefaultVatRate),
                    new DatastoreFieldMapping<TransactionHeader>(
                        "delivery_date",
                        (TransactionHeader th, string value) =>
                        {
                            // TODO add dynamic model
                            if (DateTime.TryParse(value, out var dd))
                            {
                                th.DeliveryDate = dd;
                            }
                        },
                        (TransactionHeader th) => DataItemValueModel.Create(th.DeliveryDate),
                        nameof(TransactionHeader.DeliveryDate),
                        t => t.DeliveryDate),
                    new DatastoreFieldMapping<TransactionHeader>(
                        "due_date",
                        (TransactionHeader th, string value) =>
                        {
                            // TODO add dynamic model
                            if (DateTime.TryParse(value, out var dd))
                            {
                                th.DueDate = dd;
                            }
                        },
                        (TransactionHeader th) => DataItemValueModel.Create(th.DueDate),
                        nameof(TransactionHeader.DueDate),
                        t => t.DueDate),
                    new DatastoreFieldMapping<TransactionHeader>(
                        "original_invoice_date",
                        (TransactionHeader th, string value) =>
                        {
                            // TODO add dynamic model
                            if (DateTime.TryParse(value, out var oid))
                            {
                                th.OriginalInvoiceDate = oid;
                            }
                        },
                        (TransactionHeader th) => DataItemValueModel.Create(th.OriginalInvoiceDate),
                        nameof(TransactionHeader.OriginalInvoiceDate),
                        t => t.OriginalInvoiceDate),
                    new DatastoreFieldMapping<TransactionHeader>(
                        "original_order_date",
                        (TransactionHeader th, string value) =>
                        {
                            // TODO add dynamic model
                            if (DateTime.TryParse(value, out var ood))
                            {
                                th.OriginalOrderDate = ood;
                            }
                        },
                        (TransactionHeader th) => DataItemValueModel.Create(th.OriginalOrderDate),
                        nameof(TransactionHeader.OriginalOrderDate),
                        t => t.OriginalOrderDate),

                    new DatastoreFieldMapping<TransactionHeader>(
                        "tax_point_date",
                        (TransactionHeader th, string value) =>
                        {
                            // TODO add dynamic model
                            if (DateTime.TryParse(value, out var tpd))
                            {
                                th.TaxPointDate = tpd;
                            }
                        },
                        (TransactionHeader th) => DataItemValueModel.Create(th.TaxPointDate),
                        nameof(TransactionHeader.TaxPointDate),
                        t => t.TaxPointDate),
                    new DatastoreFieldMapping<TransactionHeader>(
                        "gross_subtotal_1",
                        (TransactionHeader th, string value) =>
                        {
                            // TODO add dynamic model
                            if (decimal.TryParse(value, out var gs1))
                            {
                                th.GrossSubtotal1 = gs1;
                            }
                        },
                        (TransactionHeader th) => DataItemValueModel.Create(th.GrossSubtotal1),
                        nameof(TransactionHeader.GrossSubtotal1),
                        t => t.GrossSubtotal1),
                    new DatastoreFieldMapping<TransactionHeader>(
                        "gross_subtotal_2",
                        (TransactionHeader th, string value) =>
                        {
                            // TODO add dynamic model
                            if (decimal.TryParse(value, out var gs2))
                            {
                                th.GrossSubtotal2 = gs2;
                            }
                        },
                        (TransactionHeader th) => DataItemValueModel.Create(th.GrossSubtotal2),
                        nameof(TransactionHeader.GrossSubtotal2),
                        t => t.GrossSubtotal2),
                    new DatastoreFieldMapping<TransactionHeader>(
                        "gross_subtotal_3",
                        (TransactionHeader th, string value) =>
                        {
                            // TODO add dynamic model
                            if (decimal.TryParse(value, out var gs3))
                            {
                                th.GrossSubtotal3 = gs3;
                            }
                        },
                        (TransactionHeader th) => DataItemValueModel.Create(th.GrossSubtotal3),
                        nameof(TransactionHeader.GrossSubtotal3),
                        t => t.GrossSubtotal3),
                    new DatastoreFieldMapping<TransactionHeader>(
                        "total_discount",
                        (TransactionHeader th, string value) =>
                        {
                            // TODO add dynamic model
                            if (decimal.TryParse(value, out var td))
                            {
                                th.TotalDiscount = td;
                            }
                        },
                        (TransactionHeader th) => DataItemValueModel.Create(th.TotalDiscount),
                        nameof(TransactionHeader.TotalDiscount),
                        t => t.TotalDiscount),
                    new DatastoreFieldMapping<TransactionHeader>(
                        "total_discount_percent",
                        (TransactionHeader th, string value) =>
                        {
                            // TODO add dynamic model
                            if (decimal.TryParse(value, out var td))
                            {
                                th.TotalPercentDiscount = td;
                            }
                        },
                        (TransactionHeader th) => DataItemValueModel.Create(th.TotalPercentDiscount),
                        nameof(TransactionHeader.TotalPercentDiscount),
                        t => t.TotalPercentDiscount),
                    new DatastoreFieldMapping<TransactionHeader>(
                        "total_invoice",
                        (TransactionHeader th, string value) =>
                        {
                            // TODO add dynamic model
                            if (decimal.TryParse(value, out var td))
                            {
                                th.TotalInvoice = td;
                            }
                        },
                        (TransactionHeader th) => DataItemValueModel.Create(th.TotalInvoice),
                        nameof(TransactionHeader.TotalInvoice),
                        t => t.TotalInvoice),
                    new DatastoreFieldMapping<TransactionHeader>(
                        "total_net",
                        (TransactionHeader th, string value) =>
                        {
                            // TODO add dynamic model
                            if (decimal.TryParse(value, out var tn))
                            {
                                th.TotalNet = tn;
                            }
                        },
                        (TransactionHeader th) => DataItemValueModel.Create(th.TotalNet),
                        nameof(TransactionHeader.TotalNet),
                        t => t.TotalInvoice),
                    new DatastoreFieldMapping<TransactionHeader>(
                        "balance",
                        (TransactionHeader th, string value) =>
                        {
                            // TODO add dynamic model
                            if (decimal.TryParse(value, out var tn))
                            {
                                th.Balance = tn;
                            }
                        },
                        (TransactionHeader th) => DataItemValueModel.Create(th.Balance),
                        nameof(TransactionHeader.Balance),
                        t => t.Balance),
                    new DatastoreFieldMapping<TransactionHeader>(
                        "total_vat",
                        (TransactionHeader th, string value) =>
                        {
                            // TODO add dynamic model
                            if (decimal.TryParse(value, out var tv))
                            {
                                th.TotalVat = tv;
                            }
                        },
                        (TransactionHeader th) => DataItemValueModel.Create(th.TotalVat),
                        nameof(TransactionHeader.TotalVat),
                        t => t.TotalVat),
                    new DatastoreFieldMapping<TransactionHeader>(
                        "total_weight",
                        (TransactionHeader th, string value) =>
                        {
                            // TODO add dynamic model
                            if (decimal.TryParse(value, out var tw))
                            {
                                th.TotalWeight = tw;
                            }
                        },
                        (TransactionHeader th) => DataItemValueModel.Create(th.TotalWeight),
                        nameof(TransactionHeader.TotalWeight),
                        t => t.TotalWeight),
                    new DatastoreFieldMapping<TransactionHeader>(
                        "force_result",
                        (TransactionHeader th, string value) => th.ForceResult = value,
                        (TransactionHeader th) => DataItemValueModel.Create(th.ForceResult),
                        nameof(TransactionHeader.ForceResult),
                        t => t.TotalWeight),
                    new DatastoreFieldMapping<TransactionHeader>(
                        "batch_number",
                        (TransactionHeader th, string value) => th.BatchNumber = value,
                        (TransactionHeader th) => DataItemValueModel.Create(th.BatchNumber),
                        nameof(TransactionHeader.BatchNumber),
                        t => t.BatchNumber),
                    new DatastoreFieldMapping<TransactionHeader>(
                        "buyer_organisation",
                        (TransactionHeader th, string value) => th.BuyerOrganisation = value,
                        (TransactionHeader th) => DataItemValueModel.Create(th.BuyerOrganisation),
                        nameof(TransactionHeader.BuyerOrganisation),
                        t => t.BuyerOrganisation),
                    new DatastoreFieldMapping<TransactionHeader>(
                        "buyer_registration_number",
                        (TransactionHeader th, string value) => th.BuyerRegistrationNumber = value,
                        (TransactionHeader th) => DataItemValueModel.Create(th.BuyerRegistrationNumber),
                        nameof(TransactionHeader.BuyerRegistrationNumber),
                        t => t.BuyerRegistrationNumber),
                    new DatastoreFieldMapping<TransactionHeader>(
                        "buyers_code_for_buyer",
                        (TransactionHeader th, string value) => th.BuyersCodeForBuyer = value,
                        (TransactionHeader th) => DataItemValueModel.Create(th.BuyersCodeForBuyer),
                        nameof(TransactionHeader.BuyersCodeForBuyer),
                        t => t.BuyersCodeForBuyer),
                    new DatastoreFieldMapping<TransactionHeader>(
                        "buyers_code_for_location",
                        (TransactionHeader th, string value) => th.BuyersCodeForLocation = value,
                        (TransactionHeader th) => DataItemValueModel.Create(th.BuyersCodeForLocation),
                        nameof(TransactionHeader.BuyersCodeForLocation),
                        t => t.BuyersCodeForLocation),
                    new DatastoreFieldMapping<TransactionHeader>(
                        "buyers_code_for_supplier",
                        (TransactionHeader th, string value) => th.BuyersCodeForSupplier = value,
                        (TransactionHeader th) => DataItemValueModel.Create(th.BuyersCodeForSupplier),
                        nameof(TransactionHeader.BuyersCodeForSupplier),
                        t => t.BuyersCodeForSupplier),
                    new DatastoreFieldMapping<TransactionHeader>(
                        "contract_order_reference",
                        (TransactionHeader th, string value) => th.ContractOrderReference = value,
                        (TransactionHeader th) => DataItemValueModel.Create(th.ContractOrderReference),
                        nameof(TransactionHeader.ContractOrderReference),
                        t => t.BuyersCodeForSupplier),
                    new DatastoreFieldMapping<TransactionHeader>(
                        "cost_centre",
                        (TransactionHeader th, string value) => th.CostCentre = value,
                        (TransactionHeader th) => DataItemValueModel.Create(th.CostCentre),
                        nameof(TransactionHeader.CostCentre),
                        t => t.CostCentre),
                    new DatastoreFieldMapping<TransactionHeader>(
                        "currency",
                        (TransactionHeader th, string value) => th.Currency = value,
                        (TransactionHeader th) => DataItemValueModel.Create(th.Currency),
                        nameof(TransactionHeader.Currency),
                        t => t.Currency),

                    new DatastoreFieldMapping<TransactionHeader>(
                        "delivery_from_location",
                        (TransactionHeader th, string value) => th.DeliveryFromLocation = value,
                        (TransactionHeader th) => DataItemValueModel.Create(th.DeliveryFromLocation),
                        nameof(TransactionHeader.DeliveryFromLocation),
                        t => t.DeliveryFromLocation),
                    new DatastoreFieldMapping<TransactionHeader>(
                        "delivery_note_number",
                        (TransactionHeader th, string value) => th.DeliveryNoteNumber = value,
                        (TransactionHeader th) => DataItemValueModel.Create(th.DeliveryNoteNumber),
                        nameof(TransactionHeader.DeliveryNoteNumber),
                        t => t.DeliveryNoteNumber),
                    new DatastoreFieldMapping<TransactionHeader>(
                        "delivery_note_reference",
                        (TransactionHeader th, string value) => th.DeliveryNoteReference = value,
                        (TransactionHeader th) => DataItemValueModel.Create(th.DeliveryNoteReference),
                        nameof(TransactionHeader.DeliveryNoteReference),
                        t => t.DeliveryNoteReference),

                    new DatastoreFieldMapping<TransactionHeader>(
                        "invoice_number",
                        (TransactionHeader th, string value) => th.TransactionNumber = value,
                        (TransactionHeader th) => DataItemValueModel.Create(th.TransactionNumber),
                        nameof(TransactionHeader.TransactionNumber),
                        t => t.TransactionNumber),
                    new DatastoreFieldMapping<TransactionHeader>(
                        "invoice_date",
                        (TransactionHeader th, string value) =>
                        {
                            // TODO add dynamic model
                            if (DateTime.TryParse(value, out var tpd))
                            {
                                th.TransactionDate = tpd;
                            }
                        },
                        (TransactionHeader th) => DataItemValueModel.Create(th.TransactionDate),
                        nameof(TransactionHeader.TransactionDate),
                        t => t.TransactionDate),

                    new DatastoreFieldMapping<TransactionHeader>(
                        "narrative",
                        (TransactionHeader th, string value) => th.Narrative = value,
                        (TransactionHeader th) => DataItemValueModel.Create(th.Narrative),
                        nameof(TransactionHeader.Narrative),
                        t => t.Narrative),
                    new DatastoreFieldMapping<TransactionHeader>(
                        "order_number",
                        (TransactionHeader th, string value) => th.OrderNumber = value,
                        (TransactionHeader th) => DataItemValueModel.Create(th.OrderNumber),
                        nameof(TransactionHeader.OrderNumber),
                        t => t.OrderNumber),
                    new DatastoreFieldMapping<TransactionHeader>(
                        "original_invoice_number",
                        (TransactionHeader th, string value) => th.OriginalInvoiceNumber = value,
                        (TransactionHeader th) => DataItemValueModel.Create(th.OriginalInvoiceNumber),
                        nameof(TransactionHeader.OriginalInvoiceNumber),
                        t => t.OriginalInvoiceNumber),

                    new DatastoreFieldMapping<TransactionHeader>(
                        "payment_terms",
                        (TransactionHeader th, string value) => th.PaymentTerms = value,
                        (TransactionHeader th) => DataItemValueModel.Create(th.PaymentTerms),
                        nameof(TransactionHeader.PaymentTerms),
                        t => t.PaymentTerms),
                    new DatastoreFieldMapping<TransactionHeader>(
                        "reason_for_credit",
                        (TransactionHeader th, string value) => th.ReasonForCredit = value,
                        (TransactionHeader th) => DataItemValueModel.Create(th.ReasonForCredit),
                        nameof(TransactionHeader.ReasonForCredit),
                        t => t.ReasonForCredit),
                    new DatastoreFieldMapping<TransactionHeader>(
                        "sender_name",
                        (TransactionHeader th, string value) => th.Sender = value,
                        (TransactionHeader th) => DataItemValueModel.Create(th.Sender),
                        nameof(TransactionHeader.Sender),
                        t => t.ReasonForCredit),
                    new DatastoreFieldMapping<TransactionHeader>(
                        "supplier_order_number",
                        (TransactionHeader th, string value) => th.SupplierOrderNumber = value,
                        (TransactionHeader th) => DataItemValueModel.Create(th.SupplierOrderNumber),
                        nameof(TransactionHeader.SupplierOrderNumber),
                        t => t.SupplierOrderNumber),
                    new DatastoreFieldMapping<TransactionHeader>(
                        "supplier_bank_account_number",
                        (TransactionHeader th, string value) => th.SupplierBankAccountNumber = value,
                        (TransactionHeader th) => DataItemValueModel.Create(th.SupplierBankAccountNumber),
                        nameof(TransactionHeader.SupplierBankAccountNumber),
                        t => t.SupplierBankAccountNumber),
                    new DatastoreFieldMapping<TransactionHeader>(
                        "supplier_display_name",
                        (TransactionHeader th, string value) => th.SupplierDisplayName = value,
                        (TransactionHeader th) => DataItemValueModel.Create(th.SupplierDisplayName),
                        nameof(TransactionHeader.SupplierDisplayName),
                        t => t.SupplierDisplayName),
                    new DatastoreFieldMapping<TransactionHeader>(
                        "supplier_iban",
                        (TransactionHeader th, string value) => th.SupplierIban = value,
                        (TransactionHeader th) => DataItemValueModel.Create(th.SupplierIban),
                        nameof(TransactionHeader.SupplierIban),
                        t => t.SupplierIban),
                    new DatastoreFieldMapping<TransactionHeader>(
                        "supplier_registration_number",
                        (TransactionHeader th, string value) => th.SupplierRegistrationNumber = value,
                        (TransactionHeader th) => DataItemValueModel.Create(th.SupplierRegistrationNumber),
                        nameof(TransactionHeader.SupplierRegistrationNumber),
                        t => t.SupplierRegistrationNumber),
                    new DatastoreFieldMapping<TransactionHeader>(
                        "supplier_vat_number",
                        (TransactionHeader th, string value) => th.SupplierVatNumber = value,
                        (TransactionHeader th) => DataItemValueModel.Create(th.SupplierVatNumber),
                        nameof(TransactionHeader.SupplierVatNumber),
                        t => t.SupplierRegistrationNumber),
                    new DatastoreFieldMapping<TransactionHeader>(
                        "suppliers_code_for_buyer",
                        (TransactionHeader th, string value) => th.SuppliersCodeForBuyer = value,
                        (TransactionHeader th) => DataItemValueModel.Create(th.SuppliersCodeForBuyer),
                        nameof(TransactionHeader.SuppliersCodeForBuyer),
                        t => t.SuppliersCodeForBuyer),

                    new DatastoreFieldMapping<TransactionHeader>(
                        "type_of_supply",
                        (TransactionHeader th, string value) => th.TypeOfSupply = value,
                        (TransactionHeader th) => DataItemValueModel.Create(th.TypeOfSupply),
                        nameof(TransactionHeader.TypeOfSupply),
                        t => t.TypeOfSupply),

                    new DatastoreFieldMapping<TransactionHeader>(
                        "third_party_id",
                        (TransactionHeader th, string value) => th.ThirdPartyId = value,
                        (TransactionHeader th) => DataItemValueModel.Create(th.ThirdPartyId),
                        nameof(TransactionHeader.ThirdPartyId),
                        t => t.ThirdPartyId),

                    new DatastoreFieldMapping<TransactionHeader>(
                        "statement_start_date",
                        (TransactionHeader th, string value) =>
                        {
                            // TODO add dynamic model
                            if (DateTime.TryParse(value, out var dd))
                            {
                                th.StatementStartDate = dd;
                            }
                        },
                        (TransactionHeader th) => DataItemValueModel.Create(th.StatementStartDate),
                        nameof(TransactionHeader.StatementStartDate),
                        t => t.StatementStartDate),

                    new DatastoreFieldMapping<TransactionHeader>(
                        "statement_end_date",
                        (TransactionHeader th, string value) =>
                        {
                            // TODO add dynamic model
                            if (DateTime.TryParse(value, out var dd))
                            {
                                th.StatementEndDate = dd;
                            }
                        },
                        (TransactionHeader th) => DataItemValueModel.Create(th.StatementEndDate),
                        nameof(TransactionHeader.StatementEndDate),
                        t => t.StatementStartDate),

                    new DatastoreFieldMapping<TransactionHeader>(
                        "quote_number",
                        (TransactionHeader th, string value) => th.QuoteNumber = value,
                        (TransactionHeader th) => DataItemValueModel.Create(th.QuoteNumber),
                        nameof(TransactionHeader.QuoteNumber),
                        t => t.QuoteNumber),

                    new DatastoreFieldMapping<TransactionHeader>(
                        "marketplace_receiver",
                        (TransactionHeader th, string value) => th.MarketplaceReceiver = value,
                        (TransactionHeader th) => DataItemValueModel.Create(th.MarketplaceReceiver),
                        nameof(TransactionHeader.MarketplaceReceiver),
                        t => t.MarketplaceReceiver),

                    new DatastoreFieldMapping<TransactionHeader>(
                        "payment_status",
                        (TransactionHeader th, string value) =>
                        {
                            // TODO add dynamic model
                            if (int.TryParse(value, out var dfr))
                            {
                                th.PaymentStatus = (EnumPaymentStatus)dfr;
                            }
                        },
                        (TransactionHeader th) => DataItemValueModel.Create((int)th.PaymentStatus),
                        nameof(TransactionHeader.PaymentStatus),
                        t => t.PaymentStatus),

                    new DatastoreFieldMapping<TransactionHeader>(
                        "payment_date",
                        (TransactionHeader th, string value) =>
                        {
                            // TODO add dynamic model
                            if (DateTime.TryParse(value, out var dd))
                            {
                                th.PaymentDate = dd;
                            }
                        },
                        (TransactionHeader th) => DataItemValueModel.Create(th.PaymentDate),
                        nameof(TransactionHeader.PaymentDate),
                        t => t.PaymentDate),
            };

        public TransactionHeaderMapper(TransactionHeaderSelector transactionHeaderSelector)
            : base(transactionHeaderSelector, HeaderFieldMappings)
        {
        }

        public IFieldMappingResult<TransactionHeader> CreateAndMap(
            ITransaction transaction,
            IReadOnlyCollection<ITransactionData> dataItemModels)
        {
            if (transaction == null)
            {
                throw new ArgumentNullException(nameof(transaction));
            }

            if (transaction.IDocument == null)
            {
                throw new ArgumentException("Document must not be null", nameof(transaction));
            }

            var result = CreateAndMap(dataItemModels);
            result.Entity.DocumentId = transaction.DocumentId.Value;
            result.Entity.CustomerId = transaction.IDocument.CustomerId.Value;
            result.Entity.SupplierId = transaction.SupplierId;
            result.Entity.ReceiverId = transaction.IDocument.ReceiverId;
            result.Entity.TransactionStatus = transaction.Status;
            result.Entity.CorrelationId = transaction.IDocument.CorrelationId;

            result.Entity.DocumentAddedDate = transaction.IDocument.DateAdded.Value;
            result.Entity.SourceDocumentFileName = transaction.IDocument.FileName;
            result.Entity.SourceDocumentFullFilePath = transaction.IDocument.FullFilePath;

            result.Entity.Action = transaction.Action;
            result.Entity.AssignedTransactionGroupId = transaction.AssignedTransactionGroupId;
            result.Entity.ApprovalStatus = transaction.ApprovalStatus;
            result.Entity.DocumentType = transaction.Type;

            return result;
        }
    }
}
