﻿// <copyright file="DeliveryFromAddressSelector.cs" company="OpenECX">
// Copyright (c) OpenECX. All rights reserved.
// </copyright>

using System;
using System.Threading.Tasks;
using ECXCommon.Result;
using DataStore.Database.Tables;
using DataStore.Models;
using ETrading.Database.Tables;
using ETradingEF.Enums;
using ETrading.Database;

namespace DataStore.Mapping
{
    public class DeliveryFromAddressSelector : IDatastoreEntitySelector<TransactionAddress>
    {
        /// <summary>
        /// Returns a <see cref="TransactionAddress"/> that can be attached to a
        /// <see cref="Microsoft.EntityFrameworkCore.DbContext"/> to update
        /// the Datastore with the changes from <see cref="ITransactionData"/>
        /// for delivery-from address data.
        /// </summary>
        /// <param name="dbContext">
        /// The <see cref="Microsoft.EntityFrameworkCore.DbContext"/> to use to find
        /// the <see cref="TransactionAddress"/> to update using the information held in
        /// <paramref name="dataItemModel"/>
        /// </param>
        /// <param name="transactionHeaderId">
        /// The id of <see cref="TransactionHeader"/> of the 
        /// <see cref="TransactionAddress"/> record to update.
        /// </param>
        /// <param name="dataItemModel">
        /// The model to use to find the existing <see cref="TransactionAddress"/> record.
        /// </param>
        /// <returns>
        /// A new instance of <see cref="TransactionAddress"/> with <code>Id</code> set.
        /// </returns>
        public Task<IDataResult<TransactionAddress>> SelectAsync(
            MainContext dbContext,
            Guid transactionHeaderId,
            ITransactionData dataItemModel)
        {
            return AddressSelector.SelectAsync(
                dbContext,
                transactionHeaderId,
                dataItemModel,
                EnumAddressType.DeliveryFrom);
        }
    }
}
