﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace DataStore.Migrations
{
    public partial class WorkbenchQuoteMAtching : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Quote<PERSON><PERSON><PERSON>",
                schema: "Datastore",
                table: "TransactionHeader",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "QuoteN<PERSON><PERSON>",
                schema: "Datastore",
                table: "TransactionHeader");
        }
    }
}
