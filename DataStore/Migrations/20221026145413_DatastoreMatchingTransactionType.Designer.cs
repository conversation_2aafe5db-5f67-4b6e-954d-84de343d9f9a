﻿// <auto-generated />
using System;
using DataStore.Database;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace DataStore.Migrations
{
    [DbContext(typeof(DataStoreContext))]
    [Migration("20221026145413_DatastoreMatchingTransactionType")]
    partial class DatastoreMatchingTransactionType
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "3.1.8")
                .HasAnnotation("Relational:MaxIdentifierLength", 128)
                .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

            modelBuilder.Entity("DataStore.Database.Tables.CustomerDetail", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("DisableEmail")
                        .HasColumnType("bit");

                    b.Property<bool>("DisableLoadValidationFromFileSystem")
                        .HasColumnType("bit");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("ClusteredId")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.HasIndex("TenantId")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.ToTable("CustomerDetail","DataStore");
                });

            modelBuilder.Entity("DataStore.Database.Tables.EnumTableDocumentType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Value")
                        .HasColumnType("int");

                    b.HasKey("Id")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.ToTable("EnumDocumentType","Datastore");
                });

            modelBuilder.Entity("DataStore.Database.Tables.EnumTableTransactionStatus", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Value")
                        .HasColumnType("int");

                    b.HasKey("Id")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.ToTable("EnumTransactionStatus","Datastore");
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionAddress", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AddressLine1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AddressLine2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AddressLine3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AddressLine4")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("City")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("CountryCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CountryText")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("County")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Department")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Location")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Party")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PostCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Street")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("TransactionHeaderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id")
                        .HasName("PK_TransactionAddress")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("ClusteredId")
                        .HasName("IX_TransactionAddress")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.HasIndex("TransactionHeaderId");

                    b.ToTable("TransactionAddress","Datastore");
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionContactDetail", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Contact")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Ddi")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Fax")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("TransactionHeaderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id")
                        .HasName("PK_TransactionContactDetail")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("ClusteredId")
                        .HasName("IX_TransactionContactDetail")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.HasIndex("TransactionHeaderId");

                    b.ToTable("TransactionContactDetail","Datastore");
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionCustomHeader", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("FieldName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("TransactionHeaderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id")
                        .HasName("PK_TransactionCustomHeader")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("ClusteredId")
                        .HasName("IX_TransactionCustomHeader")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.HasIndex("TransactionHeaderId");

                    b.ToTable("TransactionCustomHeader","Datastore");
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionCustomLine", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("FieldName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("LineNumber")
                        .HasColumnType("int");

                    b.Property<Guid>("TransactionHeaderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id")
                        .HasName("PK_TransactionCustomLine")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("ClusteredId")
                        .HasName("IX_TransactionCustomLine")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.HasIndex("TransactionHeaderId");

                    b.ToTable("TransactionCustomLine","Datastore");
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionFile", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ExportGroupId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ExportId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("FileType")
                        .HasColumnType("int");

                    b.Property<string>("FullPathFileName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("TransactionHeaderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("TransactionId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id")
                        .HasName("PK_TransactionFile")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("ClusteredId")
                        .HasName("IX_TransactionFile")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.HasIndex("TransactionHeaderId");

                    b.ToTable("TransactionFile","Datastore");
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionGroup", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("CheckOrder")
                        .HasColumnType("int");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<Guid?>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Expression")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ExpressionField")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("InterventionEmailAddresses")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("SuspenseEmailTemplateId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id")
                        .HasName("PK_TransactionGroup")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("ClusteredId")
                        .HasName("IX_TransactionGroup")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.ToTable("TransactionGroup","Datastore");
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionGroupCustomer", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TransactionGroupId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("TransactionGroupId");

                    b.ToTable("TransactionGroupCustomer","DataStore");
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionHeader", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Action")
                        .HasColumnType("int");

                    b.Property<int>("ApprovalStatus")
                        .HasColumnType("int");

                    b.Property<bool>("Archived")
                        .HasColumnType("bit");

                    b.Property<Guid?>("AssignedToUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("AssignedTransactionGroupId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal?>("Balance")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("BalanceDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("BalanceDrawn")
                        .HasColumnType("bit");

                    b.Property<string>("BatchNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BuyerOrganisation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BuyerRegistrationNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BuyersCodeForBuyer")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BuyersCodeForLocation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BuyersCodeForSupplier")
                        .HasColumnType("nvarchar(450)");

                    b.Property<decimal?>("ClosingBalance")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("ContractOrderReference")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CorrelationId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CostCentre")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Currency")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("CustomerTransactionGroupId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal?>("DefaultVatRate")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("DeliveryDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeliveryFromLocation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DeliveryNoteNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DeliveryNoteReference")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DocumentAddedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("DocumentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("DocumentType")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DueDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("EmailInboxId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("EmailUniqueId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<Guid?>("ExportGroupId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("ExportedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ExternalTransactionAPIId")
                        .HasColumnType("nvarchar(50)")
                        .HasMaxLength(50);

                    b.Property<bool>("FeedData")
                        .HasColumnType("bit");

                    b.Property<string>("ForceResult")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("GrossSubtotal1")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("GrossSubtotal2")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("GrossSubtotal3")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("IsLegacy")
                        .HasColumnType("bit");

                    b.Property<bool>("IsMarketplace")
                        .HasColumnType("bit");

                    b.Property<bool>("Junk")
                        .HasColumnType("bit");

                    b.Property<string>("LegacyBilling")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LegacyFilenamePrefix")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LegacyReceiver")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LegacySender")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("MarketplaceSupplierId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Narrative")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("OnHold")
                        .HasColumnType("bit");

                    b.Property<decimal?>("OpeningBalance")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("OrderNumber")
                        .HasColumnType("nvarchar(850)")
                        .HasMaxLength(850);

                    b.Property<DateTime?>("OriginalInvoiceDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("OriginalInvoiceNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("OriginalOrderDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("PaymentTerms")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("ProcessedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("QuoteNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Reason")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ReasonForCredit")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("ReceiverId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Sender")
                        .HasColumnType("nvarchar(850)")
                        .HasMaxLength(850);

                    b.Property<bool>("ShowInWorkbench")
                        .HasColumnType("bit");

                    b.Property<string>("SourceDocumentFileName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SourceDocumentFullFilePath")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("StatementEndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("StatementStartDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("SupplierBankAccountNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SupplierDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SupplierIban")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("SupplierId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("SupplierOrderNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SupplierRegistrationNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SupplierVatNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SuppliersCodeForBuyer")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Tag")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("TaxPointDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ThirdPartyId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("ThreeWayMatched")
                        .HasColumnType("bit");

                    b.Property<decimal?>("TotalDiscount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TotalInvoice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TotalNet")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TotalPercentDiscount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TotalVat")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TotalWeight")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("TransactionDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("TransactionDateFormat")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransactionNumber")
                        .HasColumnType("nvarchar(850)")
                        .HasMaxLength(850);

                    b.Property<int>("TransactionStatus")
                        .HasColumnType("int");

                    b.Property<string>("TypeOfSupply")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id")
                        .HasName("PK_TransactionHeader")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("Action")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("Archived")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("AssignedTransactionGroupId");

                    b.HasIndex("BuyersCodeForSupplier")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("ClusteredId")
                        .HasName("IX_TransactionHeader")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.HasIndex("CustomerTransactionGroupId");

                    b.HasIndex("DocumentAddedDate")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("DocumentType")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("EmailUniqueId")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("ExportedDate")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("FeedData")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("IsLegacy")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("IsMarketplace")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("MarketplaceSupplierId")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("OrderNumber")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("ProcessedDate")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("Sender")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("ShowInWorkbench")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("SuppliersCodeForBuyer")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("TenantId")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("TransactionDate")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("TransactionNumber")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("TransactionStatus")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.ToTable("TransactionHeader","Datastore");
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionLine", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal?>("AmountDiscount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("BuyersOrderNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ChargePeriod")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ChargePeriodUOMCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ChargePeriodUOMDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("CreditLineIndicator")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("DeliveryDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeliveryNoteNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("FromDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Item")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ItemForBuyer")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("LineNumber")
                        .HasColumnType("int");

                    b.Property<int?>("LineOrderLineNumber")
                        .HasColumnType("int");

                    b.Property<int?>("MatchingTransactionType")
                        .HasColumnType("int");

                    b.Property<decimal?>("NetAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("OriginalOrderDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PackSize")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("PercentDiscount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("PriceUOMCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Quantity")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("QuantityUOMCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ReasonForCredit")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Reference")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ThirdPartyId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("ToDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal?>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid>("TransactionHeaderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal?>("UnitAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("VATAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("VATCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("VATRate")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id")
                        .HasName("PK_TransactionLine")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("ClusteredId")
                        .HasName("IX_TransactionLine")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.HasIndex("TransactionHeaderId");

                    b.ToTable("TransactionLine","Datastore");
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionMatch", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<bool>("ManualMatch")
                        .HasColumnType("bit");

                    b.Property<int>("MatchStatus")
                        .HasColumnType("int");

                    b.Property<string>("Message")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id")
                        .HasName("PK_TransactionMatch")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("ClusteredId")
                        .HasName("IX_TransactionMatch")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.ToTable("TransactionMatch","Datastore");
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionMatchAllocation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<Guid>("GRNTransactionHeaderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("InvoiceTransactionHeaderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Item")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Quantity")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TotalNet")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id")
                        .HasName("PK_TransactionMatchAllocation")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("ClusteredId")
                        .HasName("IX_TransactionMatchAllocation")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.HasIndex("GRNTransactionHeaderId");

                    b.HasIndex("InvoiceTransactionHeaderId");

                    b.ToTable("TransactionMatchAllocation","Datastore");
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionMatchData", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int>("DocumentType")
                        .HasColumnType("int");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FieldName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("HasError")
                        .HasColumnType("bit");

                    b.Property<string>("Item")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("TotalMatched")
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid>("TransactionMatchId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id")
                        .HasName("PK_TransactionMatchData")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("ClusteredId")
                        .HasName("IX_TransactionMatchData")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.HasIndex("TransactionMatchId");

                    b.ToTable("TransactionMatchData","Datastore");
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionMatchLink", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<bool>("SourceTransaction")
                        .HasColumnType("bit");

                    b.Property<Guid?>("TransactionHeaderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("TransactionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TransactionMatchId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id")
                        .HasName("PK_TransactionMatchLink")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("ClusteredId")
                        .HasName("IX_TransactionMatchLink")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.HasIndex("TransactionHeaderId");

                    b.HasIndex("TransactionId")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("TransactionMatchId");

                    b.ToTable("TransactionMatchLink","Datastore");
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionNote", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<DateTime>("DateAdded")
                        .HasColumnType("datetime2");

                    b.Property<string>("Note")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("TransactionHeaderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("UserName")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id")
                        .HasName("PK_TransactionNote")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("ClusteredId")
                        .HasName("IX_TransactionNote")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.HasIndex("TransactionHeaderId");

                    b.ToTable("TransactionNote","Datastore");
                });

            modelBuilder.Entity("DataStore.ViewModels.WorkbenchListItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AccountNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Action")
                        .HasColumnType("int");

                    b.Property<bool?>("Archived")
                        .HasColumnType("bit");

                    b.Property<Guid?>("AssignedUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ClientPONumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CustomerName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DocumentFileName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("DocumentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("DocumentType")
                        .HasColumnType("int");

                    b.Property<string>("ExportQueue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ExportStatus")
                        .HasColumnType("int");

                    b.Property<bool>("Junk")
                        .HasColumnType("bit");

                    b.Property<string>("MemberGroupName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("ShowInWorkbench")
                        .HasColumnType("bit");

                    b.Property<string>("SupplierDocumentNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SupplierName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("TransactionDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("TransactionGroupId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("TransactionGroupName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("TransactionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("TransactionStatus")
                        .HasColumnType("int");

                    b.Property<decimal?>("Value")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.ToTable("WorkBenchList");
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionAddress", b =>
                {
                    b.HasOne("DataStore.Database.Tables.TransactionHeader", "TransactionHeader")
                        .WithMany("TransactionAddresses")
                        .HasForeignKey("TransactionHeaderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionContactDetail", b =>
                {
                    b.HasOne("DataStore.Database.Tables.TransactionHeader", "TransactionHeader")
                        .WithMany("TransactionContactDetails")
                        .HasForeignKey("TransactionHeaderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionCustomHeader", b =>
                {
                    b.HasOne("DataStore.Database.Tables.TransactionHeader", "TransactionHeader")
                        .WithMany("TransactionCustomHeaders")
                        .HasForeignKey("TransactionHeaderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionCustomLine", b =>
                {
                    b.HasOne("DataStore.Database.Tables.TransactionHeader", "TransactionHeader")
                        .WithMany("TransactionCustomLines")
                        .HasForeignKey("TransactionHeaderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionFile", b =>
                {
                    b.HasOne("DataStore.Database.Tables.TransactionHeader", "TransactionHeader")
                        .WithMany("TransactionFiles")
                        .HasForeignKey("TransactionHeaderId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionGroupCustomer", b =>
                {
                    b.HasOne("DataStore.Database.Tables.TransactionGroup", "TransactionGroup")
                        .WithMany()
                        .HasForeignKey("TransactionGroupId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionHeader", b =>
                {
                    b.HasOne("DataStore.Database.Tables.TransactionGroup", "AssignedTransactionGroup")
                        .WithMany()
                        .HasForeignKey("AssignedTransactionGroupId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("DataStore.Database.Tables.TransactionGroup", "CustomerTransactionGroup")
                        .WithMany()
                        .HasForeignKey("CustomerTransactionGroupId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionLine", b =>
                {
                    b.HasOne("DataStore.Database.Tables.TransactionHeader", "TransactionHeader")
                        .WithMany("TransactionLines")
                        .HasForeignKey("TransactionHeaderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionMatchAllocation", b =>
                {
                    b.HasOne("DataStore.Database.Tables.TransactionHeader", "GRNTransactionHeader")
                        .WithMany()
                        .HasForeignKey("GRNTransactionHeaderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("DataStore.Database.Tables.TransactionHeader", "TransactionHeader")
                        .WithMany()
                        .HasForeignKey("InvoiceTransactionHeaderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionMatchData", b =>
                {
                    b.HasOne("DataStore.Database.Tables.TransactionMatch", null)
                        .WithMany("TransactionMatchDatas")
                        .HasForeignKey("TransactionMatchId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionMatchLink", b =>
                {
                    b.HasOne("DataStore.Database.Tables.TransactionHeader", "TransactionHeader")
                        .WithMany("TransactionMatchLinks")
                        .HasForeignKey("TransactionHeaderId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("DataStore.Database.Tables.TransactionMatch", "TransactionMatch")
                        .WithMany("TransactionMatchLinks")
                        .HasForeignKey("TransactionMatchId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionNote", b =>
                {
                    b.HasOne("DataStore.Database.Tables.TransactionHeader", "TransactionHeader")
                        .WithMany()
                        .HasForeignKey("TransactionHeaderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();
                });
#pragma warning restore 612, 618
        }
    }
}
