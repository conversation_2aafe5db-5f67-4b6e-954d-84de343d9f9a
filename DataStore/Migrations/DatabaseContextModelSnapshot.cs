﻿// <auto-generated />
using System;
using DataStore.Database;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace DataStore.Migrations
{
    [DbContext(typeof(DataStoreContext))]
    partial class DatabaseContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "3.1.8")
                .HasAnnotation("Relational:MaxIdentifierLength", 128)
                .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

            modelBuilder.Entity("DataStore.Database.Tables.TransactionAddress", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AddressLine1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AddressLine2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AddressLine3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AddressLine4")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("City")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("CountryCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CountryText")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("County")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Department")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Location")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Party")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PostCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Street")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("TransactionHeaderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id")
                        .HasName("PK_TransactionAddress")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("ClusteredId")
                        .HasName("IX_TransactionAddress")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.HasIndex("TransactionHeaderId");

                    b.ToTable("TransactionAddress","Datastore");
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionContactDetail", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Contact")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Ddi")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Fax")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("TransactionHeaderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id")
                        .HasName("PK_TransactionContactDetail")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("ClusteredId")
                        .HasName("IX_TransactionContactDetail")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.HasIndex("TransactionHeaderId");

                    b.ToTable("TransactionContactDetail","Datastore");
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionCustomHeader", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("FieldName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("TransactionHeaderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id")
                        .HasName("PK_TransactionCustomHeader")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("ClusteredId")
                        .HasName("IX_TransactionCustomHeader")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.HasIndex("TransactionHeaderId");

                    b.ToTable("TransactionCustomHeader","Datastore");
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionCustomLine", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("FieldName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("LineNumber")
                        .HasColumnType("int");

                    b.Property<Guid>("TransactionHeaderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id")
                        .HasName("PK_TransactionCustomLine")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("ClusteredId")
                        .HasName("IX_TransactionCustomLine")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.HasIndex("TransactionHeaderId");

                    b.ToTable("TransactionCustomLine","Datastore");
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionFile", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ExportGroupId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ExportId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("FileType")
                        .HasColumnType("int");

                    b.Property<string>("FullPathFileName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("TransactionHeaderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("TransactionId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id")
                        .HasName("PK_TransactionFile")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("ClusteredId")
                        .HasName("IX_TransactionFile")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.HasIndex("TransactionHeaderId");

                    b.HasIndex("TransactionId");

                    b.ToTable("TransactionFile","Datastore");
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionGroup", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("CheckOrder")
                        .HasColumnType("int");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<Guid?>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Expression")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ExpressionField")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("InterventionEmailAddresses")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("SuspenseEmailTemplateId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id")
                        .HasName("PK_TransactionGroup")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("ClusteredId")
                        .HasName("IX_TransactionGroup")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.ToTable("TransactionGroup","Datastore");
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionGroupCustomer", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TransactionGroupId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("TransactionGroupId");

                    b.ToTable("TransactionGroupCustomer","DataStore");
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionHeader", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Action")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ApprovalDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ApprovalReleaseDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("ApprovalStatus")
                        .HasColumnType("int");

                    b.Property<bool>("Archived")
                        .HasColumnType("bit");

                    b.Property<Guid?>("AssignedToUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("AssignedTransactionGroupId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal?>("Balance")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("BalanceDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("BalanceDrawn")
                        .HasColumnType("bit");

                    b.Property<string>("BatchNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BuyerOrganisation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BuyerRegistrationNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BuyersCodeForBuyer")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BuyersCodeForLocation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BuyersCodeForSupplier")
                        .HasColumnType("nvarchar(450)");

                    b.Property<decimal?>("ClosingBalance")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("ContractOrderReference")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CorrelationId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CostCentre")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Currency")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("CustomerTransactionGroupId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal?>("DefaultVatRate")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("DeliveryDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeliveryFromLocation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DeliveryNoteNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DeliveryNoteReference")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DocumentAddedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("DocumentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("DocumentType")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DueDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("EmailInboxConfigId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("EmailInboxId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("EmailUniqueId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<Guid?>("ExportGroupId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("ExportedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ExternalTransactionAPIId")
                        .HasColumnType("nvarchar(50)")
                        .HasMaxLength(50);

                    b.Property<bool>("FeedData")
                        .HasColumnType("bit");

                    b.Property<string>("ForceResult")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("GrossSubtotal1")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("GrossSubtotal2")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("GrossSubtotal3")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("IsLegacy")
                        .HasColumnType("bit");

                    b.Property<bool>("IsMarketplace")
                        .HasColumnType("bit");

                    b.Property<string>("LegacyBilling")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LegacyFilenamePrefix")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LegacyReceiver")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LegacySender")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MarketplaceReceiver")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("MarketplaceSupplierId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("MatchingDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("MatchingReleaseDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Narrative")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("OnHold")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("OnHoldDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("OnHoldReleaseDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal?>("OpeningBalance")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("OrderNumber")
                        .HasColumnType("nvarchar(850)")
                        .HasMaxLength(850);

                    b.Property<DateTime?>("OriginalInvoiceDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("OriginalInvoiceNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("OriginalOrderDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("PaymentTerms")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("ProcessedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("QuoteNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Reason")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ReasonForCredit")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("ReceiverId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Sender")
                        .HasColumnType("nvarchar(850)")
                        .HasMaxLength(850);

                    b.Property<string>("SenderEmailAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("ShowInWorkbench")
                        .HasColumnType("bit");

                    b.Property<string>("SourceDocumentFileName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SourceDocumentFullFilePath")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("StatementEndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("StatementStartDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("SupplierBankAccountNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SupplierDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SupplierIban")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("SupplierId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("SupplierOrderNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SupplierRegistrationNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SupplierVatNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SuppliersCodeForBuyer")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("SuspnseDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("SuspnseReleaseDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Tag")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("TaxPointDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ThirdPartyId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("ThreeWayMatched")
                        .HasColumnType("bit");

                    b.Property<decimal?>("TotalDiscount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TotalInvoice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TotalNet")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TotalPercentDiscount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TotalVat")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TotalWeight")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("TransactionDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("TransactionDateFormat")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransactionNumber")
                        .HasColumnType("nvarchar(850)")
                        .HasMaxLength(850);

                    b.Property<int>("TransactionStatus")
                        .HasColumnType("int");

                    b.Property<string>("TypeOfSupply")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id")
                        .HasName("PK_TransactionHeader")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("Action")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("ApprovalDate")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("ApprovalReleaseDate")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("Archived")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("AssignedTransactionGroupId");

                    b.HasIndex("BuyersCodeForSupplier")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("ClusteredId")
                        .HasName("IX_TransactionHeader")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.HasIndex("CustomerTransactionGroupId");

                    b.HasIndex("DocumentAddedDate")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("DocumentId");

                    b.HasIndex("DocumentType")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("EmailUniqueId")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("ExportedDate")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("FeedData")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("IsLegacy")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("IsMarketplace")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("MarketplaceSupplierId")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("MatchingDate")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("MatchingReleaseDate")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("OnHoldDate")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("OnHoldReleaseDate")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("OrderNumber")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("ProcessedDate")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("Sender")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("ShowInWorkbench")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("StatementEndDate")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("StatementStartDate")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("SuppliersCodeForBuyer")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("SuspnseDate")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("SuspnseReleaseDate")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("TenantId")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("TransactionDate")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("TransactionNumber")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("TransactionStatus")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.ToTable("TransactionHeader","Datastore");
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionLine", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal?>("AmountDiscount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("BuyersOrderNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ChargePeriod")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ChargePeriodUOMCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ChargePeriodUOMDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("CreditLineIndicator")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("DeliveryDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeliveryNoteNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("FromDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Item")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ItemForBuyer")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("LineNumber")
                        .HasColumnType("int");

                    b.Property<int?>("LineOrderLineNumber")
                        .HasColumnType("int");

                    b.Property<int?>("MatchingTransactionType")
                        .HasColumnType("int");

                    b.Property<decimal?>("NetAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("OriginalOrderDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PackSize")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("PercentDiscount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("PriceUOMCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Quantity")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("QuantityUOMCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ReasonForCredit")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Reference")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ThirdPartyId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("ToDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal?>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid>("TransactionHeaderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal?>("UnitAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("VATAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("VATCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("VATRate")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id")
                        .HasName("PK_TransactionLine")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("ClusteredId")
                        .HasName("IX_TransactionLine")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.HasIndex("TransactionHeaderId");

                    b.ToTable("TransactionLine","Datastore");
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionMatch", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<bool>("ManualMatch")
                        .HasColumnType("bit");

                    b.Property<int>("MatchStatus")
                        .HasColumnType("int");

                    b.Property<string>("Message")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id")
                        .HasName("PK_TransactionMatch")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("ClusteredId")
                        .HasName("IX_TransactionMatch")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.ToTable("TransactionMatch","Datastore");
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionMatchAllocation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<Guid>("GRNTransactionHeaderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("InvoiceTransactionHeaderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Item")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Quantity")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TotalNet")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id")
                        .HasName("PK_TransactionMatchAllocation")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("ClusteredId")
                        .HasName("IX_TransactionMatchAllocation")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.HasIndex("GRNTransactionHeaderId");

                    b.HasIndex("InvoiceTransactionHeaderId");

                    b.ToTable("TransactionMatchAllocation","Datastore");
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionMatchData", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int>("DocumentType")
                        .HasColumnType("int");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FieldName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("HasError")
                        .HasColumnType("bit");

                    b.Property<string>("Item")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("TotalMatched")
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid>("TransactionMatchId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id")
                        .HasName("PK_TransactionMatchData")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("ClusteredId")
                        .HasName("IX_TransactionMatchData")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.HasIndex("TransactionMatchId");

                    b.ToTable("TransactionMatchData","Datastore");
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionMatchLink", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<bool>("SourceTransaction")
                        .HasColumnType("bit");

                    b.Property<Guid?>("TransactionHeaderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("TransactionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TransactionMatchId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id")
                        .HasName("PK_TransactionMatchLink")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("ClusteredId")
                        .HasName("IX_TransactionMatchLink")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.HasIndex("TransactionHeaderId");

                    b.HasIndex("TransactionId")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("TransactionMatchId");

                    b.ToTable("TransactionMatchLink","Datastore");
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionNote", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<DateTime>("DateAdded")
                        .HasColumnType("datetime2");

                    b.Property<string>("Note")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("TransactionHeaderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("UserName")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id")
                        .HasName("PK_TransactionNote")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("ClusteredId")
                        .HasName("IX_TransactionNote")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.HasIndex("TransactionHeaderId");

                    b.ToTable("TransactionNote","Datastore");
                });

            modelBuilder.Entity("ECXBackgroundService.Shared.Database.Tables.Job", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("QueueId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ReportId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("QueueId");

                    b.ToTable("Job","BackgroundService");
                });

            modelBuilder.Entity("ECXBackgroundService.Shared.Database.Tables.JobProcess", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("JobId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ProcessId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ProcessOrder")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("JobId");

                    b.HasIndex("ProcessId");

                    b.ToTable("JobProcess","BackgroundService");
                });

            modelBuilder.Entity("ECXBackgroundService.Shared.Database.Tables.Process", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AssemblyQualifiedName")
                        .HasColumnType("nvarchar(500)")
                        .HasMaxLength(500);

                    b.Property<string>("ClassName")
                        .HasColumnType("nvarchar(500)")
                        .HasMaxLength(500);

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FullName")
                        .HasColumnType("nvarchar(500)")
                        .HasMaxLength(500);

                    b.Property<string>("MethodName")
                        .HasColumnType("nvarchar(500)")
                        .HasMaxLength(500);

                    b.Property<string>("SQLCommand")
                        .HasColumnType("nvarchar(500)")
                        .HasMaxLength(500);

                    b.Property<int>("SQLCommandTimeout")
                        .HasColumnType("int");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("Process","BackgroundService");
                });

            modelBuilder.Entity("ECXBackgroundService.Shared.Database.Tables.ProcessParameter", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("LookUpURL")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("OrderNumber")
                        .HasColumnType("int");

                    b.Property<Guid?>("ProcessId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ProcessTemplateParameterId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("ProcessId");

                    b.HasIndex("ProcessTemplateParameterId");

                    b.ToTable("ProcessParameter","BackgroundService");
                });

            modelBuilder.Entity("ECXBackgroundService.Shared.Database.Tables.ProcessTemplate", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AssemblyQualifiedName")
                        .HasColumnType("nvarchar(500)")
                        .HasMaxLength(500);

                    b.Property<string>("ClassName")
                        .HasColumnType("nvarchar(500)")
                        .HasMaxLength(500);

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FullName")
                        .HasColumnType("nvarchar(500)")
                        .HasMaxLength(500);

                    b.Property<string>("MethodName")
                        .HasColumnType("nvarchar(500)")
                        .HasMaxLength(500);

                    b.HasKey("Id");

                    b.ToTable("ProcessTemplate","BackgroundService");
                });

            modelBuilder.Entity("ECXBackgroundService.Shared.Database.Tables.ProcessTemplateParameter", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("LookUpURL")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("OrderNumber")
                        .HasColumnType("int");

                    b.Property<Guid>("ProcessTemplateId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ProcessTemplateId");

                    b.ToTable("ProcessTemplateParameter","BackgroundService");
                });

            modelBuilder.Entity("ECXBackgroundService.Shared.Database.Tables.Queue", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(20)")
                        .HasMaxLength(20);

                    b.Property<string>("HangfireServerGroup")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ServerDNSName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("Workers")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("Queue","BackgroundService");
                });

            modelBuilder.Entity("ECXBackgroundService.Shared.Database.Tables.Schedule", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Cron")
                        .HasColumnType("nvarchar(500)")
                        .HasMaxLength(500);

                    b.Property<bool>("Enabled")
                        .HasColumnType("bit");

                    b.Property<string>("HumanReadableSchedule")
                        .HasColumnType("nvarchar(500)")
                        .HasMaxLength(500);

                    b.Property<Guid>("JobId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("JobId");

                    b.ToTable("Schedule","BackgroundService");
                });

            modelBuilder.Entity("ECXIO.Core.Database.Tables.Email.EmailTemplate", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Body")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Subject")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.ToTable("EmailTemplate","Email");
                });

            modelBuilder.Entity("ECXIO.Core.Database.Tables.IO.FTP", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("AuthentificationType")
                        .HasColumnType("int");

                    b.Property<string>("BackUpFolder")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CertificateKey")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CertificatePath")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Directory")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Password")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Server")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("UsePassive")
                        .HasColumnType("bit");

                    b.Property<string>("UserName")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("FTP","IO");
                });

            modelBuilder.Entity("ETrading.Database.Tables.Customer.CustomerDetail", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("DeploymentColor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("DisableEmail")
                        .HasColumnType("bit");

                    b.Property<Guid?>("ProcessingQueueId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("ClusteredId")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.HasIndex("ProcessingQueueId");

                    b.HasIndex("TenantId")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.ToTable("CustomerDetail","Customer");
                });

            modelBuilder.Entity("ETrading.Database.Tables.Customer.EmailInboxConfig", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Authentification")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Encryption")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Inbox")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Password")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Server")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Username")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.ToTable("EmailInboxConfig","Customer");
                });

            modelBuilder.Entity("ETrading.Database.Tables.Customer.Field", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("FieldName")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("LineType")
                        .HasColumnType("int");

                    b.HasKey("Id")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("ClusteredId")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.HasIndex("FieldName")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.ToTable("Field","Customer");
                });

            modelBuilder.Entity("ETrading.Database.Tables.Customer.FieldData", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Action")
                        .HasColumnType("int");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Condition")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Configuration")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("DoesNotHaveFlag")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EmailAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EmailErrorMessage")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ExcludeDocumentType")
                        .HasColumnType("int");

                    b.Property<Guid>("FieldId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("FriendlyName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HasFlag")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("IncludeDocumentType")
                        .HasColumnType("int");

                    b.Property<string>("InterventionErrorMessage")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("InterventionTarget")
                        .HasColumnType("int");

                    b.Property<int>("IsMandatory")
                        .HasColumnType("int");

                    b.Property<bool>("IsReadOnly")
                        .HasColumnType("bit");

                    b.Property<int>("LookUpType")
                        .HasColumnType("int");

                    b.Property<string>("MandatoryCustomCheck")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Order")
                        .HasColumnType("int");

                    b.Property<string>("RuleFolder")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("ClusteredId")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.HasIndex("CustomerId");

                    b.HasIndex("FieldId");

                    b.HasIndex("RuleFolder")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.ToTable("FieldData","Customer");
                });

            modelBuilder.Entity("ETrading.Database.Tables.Customer.Receiver", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AzureRulesContainer")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CCEmail")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CSVSeperator")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Cron")
                        .HasColumnType("nvarchar(50)")
                        .HasMaxLength(50);

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("DisablePolling")
                        .HasColumnType("bit");

                    b.Property<Guid?>("EmailInboxCofigId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ExportQueueId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("FTPId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("InputFolder")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("Orders")
                        .HasColumnType("bit");

                    b.Property<Guid?>("PostProcessHangfireJobId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ProcessFileName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("ProcessingQueueId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Processor")
                        .HasColumnType("int");

                    b.Property<Guid>("QueueId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ReturnToSenderEmailTemplateId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("SupplierMode")
                        .HasColumnType("int");

                    b.Property<Guid?>("TransactionGroupId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<Guid?>("ValidationEmailTemplateId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("EmailInboxCofigId");

                    b.HasIndex("ExportQueueId");

                    b.HasIndex("FTPId");

                    b.HasIndex("ProcessingQueueId");

                    b.HasIndex("QueueId");

                    b.HasIndex("ReturnToSenderEmailTemplateId");

                    b.HasIndex("ValidationEmailTemplateId");

                    b.ToTable("Receiver","Customer");
                });

            modelBuilder.Entity("ETrading.Database.Tables.Customer.VATGroup", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("VATGroup","Customer");
                });

            modelBuilder.Entity("ETrading.Database.Tables.Customer.ValidationRule", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Action")
                        .HasColumnType("int");

                    b.Property<int>("AllowIgnore")
                        .HasColumnType("int");

                    b.Property<bool>("AllowIgnoreFromCustomerIntervention")
                        .HasColumnType("bit");

                    b.Property<bool>("AllowIgnoreFromIntervention")
                        .HasColumnType("bit");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Condition")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Configuration")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("DeleteTransactionLine")
                        .HasColumnType("bit");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DoesNotHaveFlag")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EmailAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EmailErrorMessage")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ExcludeDocumentType")
                        .HasColumnType("int");

                    b.Property<string>("HasFlag")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("IncludeDocumentType")
                        .HasColumnType("int");

                    b.Property<string>("InterventionErrorMessage")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("InterventionTarget")
                        .HasColumnType("int");

                    b.Property<string>("QuestionID")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Reason")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RuleFolder")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ScenarioType")
                        .HasColumnType("int");

                    b.Property<string>("ValidationFunction")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.ToTable("ValidationRule","Customer");
                });

            modelBuilder.Entity("ETrading.Database.Tables.Document", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Chain")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("CorrelationId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("DateAdded")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("EmailInboxId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("FileContainer")
                        .HasColumnType("nvarchar(500)")
                        .HasMaxLength(500);

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FullFilePath")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("datetime2");

                    b.Property<int>("Progress")
                        .HasColumnType("int");

                    b.Property<Guid?>("QueueId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ReceiverId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool?>("Recompressed")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("DateAdded")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("EmailInboxId");

                    b.HasIndex("ReceiverId");

                    b.ToTable("Document","ETrading");
                });

            modelBuilder.Entity("ETrading.Database.Tables.ETrading.Flag", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("TransactionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("Value")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.HasIndex("TransactionId");

                    b.ToTable("Flag","ETrading");
                });

            modelBuilder.Entity("ETrading.Database.Tables.ETrading.ValidationMessage", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<Guid?>("FieldDataId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int?>("LineNumber")
                        .HasColumnType("int");

                    b.Property<string>("Message")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("Overide")
                        .HasColumnType("bit");

                    b.Property<bool>("ServerSide")
                        .HasColumnType("bit");

                    b.Property<Guid?>("TransactionHeaderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TransactionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ValidationRuleId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("FieldDataId");

                    b.HasIndex("TransactionId");

                    b.HasIndex("ValidationRuleId");

                    b.ToTable("ValidationMessage","ETrading");
                });

            modelBuilder.Entity("ETrading.Database.Tables.Email.Inbox", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Attachments")
                        .HasColumnType("int");

                    b.Property<int>("AttachmentsForwarded")
                        .HasColumnType("int");

                    b.Property<int>("AttachmentsIgnored")
                        .HasColumnType("int");

                    b.Property<int>("AttachmentsProcessed")
                        .HasColumnType("int");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<int?>("DocumentCountOnError")
                        .HasColumnType("int");

                    b.Property<int>("DocumentsCreated")
                        .HasColumnType("int");

                    b.Property<int>("EmbeddedDocuments")
                        .HasColumnType("int");

                    b.Property<string>("Exception")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("ExecutionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("ExecutionStartDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("ExtensionsNotProcessed")
                        .HasColumnType("int");

                    b.Property<string>("From")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Message")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("PermanentError")
                        .HasColumnType("bit");

                    b.Property<int?>("RetryCount")
                        .HasColumnType("int");

                    b.Property<string>("Subject")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("To")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UniqueId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("UniqueId")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.ToTable("Inbox","Email");
                });

            modelBuilder.Entity("ETrading.Database.Tables.Transaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Action")
                        .HasColumnType("int");

                    b.Property<int>("ApprovalStatus")
                        .HasColumnType("int");

                    b.Property<Guid?>("AssignedTo")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("AssignedTransactionGroupId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ClusteredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<Guid?>("CustomerTransactionGroupId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("DisableReprocess")
                        .HasColumnType("bit");

                    b.Property<Guid?>("DocumentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("EmailAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Engine")
                        .HasColumnType("int");

                    b.Property<int>("PageLineRangeEndIndex")
                        .HasColumnType("int");

                    b.Property<int>("PageLineRangeStartIndex")
                        .HasColumnType("int");

                    b.Property<int>("PageNumberFirst")
                        .HasColumnType("int");

                    b.Property<int>("PageNumberLast")
                        .HasColumnType("int");

                    b.Property<string>("Reason")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("ShowInWorkBench")
                        .HasColumnType("bit");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid?>("SupplierId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("TransactionHeaderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<Guid?>("VATGroupId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ValidationStatus")
                        .HasColumnType("int");

                    b.HasKey("Id")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("AssignedTransactionGroupId");

                    b.HasIndex("ClusteredId")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.HasIndex("CustomerTransactionGroupId");

                    b.HasIndex("DocumentId");

                    b.HasIndex("Status")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("TransactionHeaderId")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("Type")
                        .HasAnnotation("SqlServer:Clustered", false);

                    b.HasIndex("VATGroupId");

                    b.ToTable("Transaction","ETrading");
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionAddress", b =>
                {
                    b.HasOne("DataStore.Database.Tables.TransactionHeader", "TransactionHeader")
                        .WithMany("TransactionAddresses")
                        .HasForeignKey("TransactionHeaderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionContactDetail", b =>
                {
                    b.HasOne("DataStore.Database.Tables.TransactionHeader", "TransactionHeader")
                        .WithMany("TransactionContactDetails")
                        .HasForeignKey("TransactionHeaderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionCustomHeader", b =>
                {
                    b.HasOne("DataStore.Database.Tables.TransactionHeader", "TransactionHeader")
                        .WithMany("TransactionCustomHeaders")
                        .HasForeignKey("TransactionHeaderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionCustomLine", b =>
                {
                    b.HasOne("DataStore.Database.Tables.TransactionHeader", "TransactionHeader")
                        .WithMany("TransactionCustomLines")
                        .HasForeignKey("TransactionHeaderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionFile", b =>
                {
                    b.HasOne("DataStore.Database.Tables.TransactionHeader", "TransactionHeader")
                        .WithMany("TransactionFiles")
                        .HasForeignKey("TransactionHeaderId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ETrading.Database.Tables.Transaction", "Transaction")
                        .WithMany("TransactionFiles")
                        .HasForeignKey("TransactionId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionGroupCustomer", b =>
                {
                    b.HasOne("DataStore.Database.Tables.TransactionGroup", "TransactionGroup")
                        .WithMany()
                        .HasForeignKey("TransactionGroupId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionHeader", b =>
                {
                    b.HasOne("DataStore.Database.Tables.TransactionGroup", "AssignedTransactionGroup")
                        .WithMany()
                        .HasForeignKey("AssignedTransactionGroupId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("DataStore.Database.Tables.TransactionGroup", "CustomerTransactionGroup")
                        .WithMany()
                        .HasForeignKey("CustomerTransactionGroupId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ETrading.Database.Tables.Document", "Document")
                        .WithMany()
                        .HasForeignKey("DocumentId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionLine", b =>
                {
                    b.HasOne("DataStore.Database.Tables.TransactionHeader", "TransactionHeader")
                        .WithMany("TransactionLines")
                        .HasForeignKey("TransactionHeaderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionMatchAllocation", b =>
                {
                    b.HasOne("DataStore.Database.Tables.TransactionHeader", "GRNTransactionHeader")
                        .WithMany()
                        .HasForeignKey("GRNTransactionHeaderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("DataStore.Database.Tables.TransactionHeader", "TransactionHeader")
                        .WithMany()
                        .HasForeignKey("InvoiceTransactionHeaderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionMatchData", b =>
                {
                    b.HasOne("DataStore.Database.Tables.TransactionMatch", null)
                        .WithMany("TransactionMatchDatas")
                        .HasForeignKey("TransactionMatchId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionMatchLink", b =>
                {
                    b.HasOne("DataStore.Database.Tables.TransactionHeader", "TransactionHeader")
                        .WithMany("TransactionMatchLinks")
                        .HasForeignKey("TransactionHeaderId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("DataStore.Database.Tables.TransactionMatch", "TransactionMatch")
                        .WithMany("TransactionMatchLinks")
                        .HasForeignKey("TransactionMatchId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();
                });

            modelBuilder.Entity("DataStore.Database.Tables.TransactionNote", b =>
                {
                    b.HasOne("DataStore.Database.Tables.TransactionHeader", "TransactionHeader")
                        .WithMany()
                        .HasForeignKey("TransactionHeaderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();
                });

            modelBuilder.Entity("ECXBackgroundService.Shared.Database.Tables.Job", b =>
                {
                    b.HasOne("ECXBackgroundService.Shared.Database.Tables.Queue", "Queue")
                        .WithMany("Jobs")
                        .HasForeignKey("QueueId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();
                });

            modelBuilder.Entity("ECXBackgroundService.Shared.Database.Tables.JobProcess", b =>
                {
                    b.HasOne("ECXBackgroundService.Shared.Database.Tables.Job", null)
                        .WithMany("JobProcesses")
                        .HasForeignKey("JobId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ECXBackgroundService.Shared.Database.Tables.Process", "Process")
                        .WithMany()
                        .HasForeignKey("ProcessId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();
                });

            modelBuilder.Entity("ECXBackgroundService.Shared.Database.Tables.ProcessParameter", b =>
                {
                    b.HasOne("ECXBackgroundService.Shared.Database.Tables.Process", "Process")
                        .WithMany("ProcessParameters")
                        .HasForeignKey("ProcessId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ECXBackgroundService.Shared.Database.Tables.ProcessTemplateParameter", "ProcessTemplateParameter")
                        .WithMany()
                        .HasForeignKey("ProcessTemplateParameterId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("ECXBackgroundService.Shared.Database.Tables.ProcessTemplateParameter", b =>
                {
                    b.HasOne("ECXBackgroundService.Shared.Database.Tables.ProcessTemplate", "ProcessTemplate")
                        .WithMany("ProcessTemplateParameters")
                        .HasForeignKey("ProcessTemplateId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();
                });

            modelBuilder.Entity("ECXBackgroundService.Shared.Database.Tables.Schedule", b =>
                {
                    b.HasOne("ECXBackgroundService.Shared.Database.Tables.Job", "Job")
                        .WithMany("Schedules")
                        .HasForeignKey("JobId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();
                });

            modelBuilder.Entity("ETrading.Database.Tables.Customer.CustomerDetail", b =>
                {
                    b.HasOne("ECXBackgroundService.Shared.Database.Tables.Queue", "ProcessingQueue")
                        .WithMany()
                        .HasForeignKey("ProcessingQueueId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("ETrading.Database.Tables.Customer.EmailInboxConfig", b =>
                {
                    b.HasOne("ETrading.Database.Tables.Customer.CustomerDetail", "Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();
                });

            modelBuilder.Entity("ETrading.Database.Tables.Customer.FieldData", b =>
                {
                    b.HasOne("ETrading.Database.Tables.Customer.CustomerDetail", "Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ETrading.Database.Tables.Customer.Field", "Field")
                        .WithMany()
                        .HasForeignKey("FieldId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();
                });

            modelBuilder.Entity("ETrading.Database.Tables.Customer.Receiver", b =>
                {
                    b.HasOne("ETrading.Database.Tables.Customer.CustomerDetail", "Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ETrading.Database.Tables.Customer.EmailInboxConfig", "EmailInboxConfig")
                        .WithMany()
                        .HasForeignKey("EmailInboxCofigId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ECXBackgroundService.Shared.Database.Tables.Queue", "ExportQueue")
                        .WithMany()
                        .HasForeignKey("ExportQueueId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ECXIO.Core.Database.Tables.IO.FTP", "FTP")
                        .WithMany()
                        .HasForeignKey("FTPId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ECXBackgroundService.Shared.Database.Tables.Queue", "ProcessingQueue")
                        .WithMany()
                        .HasForeignKey("ProcessingQueueId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ECXBackgroundService.Shared.Database.Tables.Queue", "Queue")
                        .WithMany()
                        .HasForeignKey("QueueId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ECXIO.Core.Database.Tables.Email.EmailTemplate", "ReturnToSenderEmailTemplate")
                        .WithMany()
                        .HasForeignKey("ReturnToSenderEmailTemplateId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ECXIO.Core.Database.Tables.Email.EmailTemplate", "ValidationEmailTemplate")
                        .WithMany()
                        .HasForeignKey("ValidationEmailTemplateId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("ETrading.Database.Tables.Customer.ValidationRule", b =>
                {
                    b.HasOne("ETrading.Database.Tables.Customer.CustomerDetail", "Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("ETrading.Database.Tables.Document", b =>
                {
                    b.HasOne("ETrading.Database.Tables.Customer.CustomerDetail", "Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ETrading.Database.Tables.Email.Inbox", "EmailInbox")
                        .WithMany()
                        .HasForeignKey("EmailInboxId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ETrading.Database.Tables.Customer.Receiver", "Receiver")
                        .WithMany()
                        .HasForeignKey("ReceiverId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();
                });

            modelBuilder.Entity("ETrading.Database.Tables.ETrading.Flag", b =>
                {
                    b.HasOne("ETrading.Database.Tables.Transaction", "Transaction")
                        .WithMany("Flags")
                        .HasForeignKey("TransactionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();
                });

            modelBuilder.Entity("ETrading.Database.Tables.ETrading.ValidationMessage", b =>
                {
                    b.HasOne("ETrading.Database.Tables.Customer.FieldData", "FieldData")
                        .WithMany()
                        .HasForeignKey("FieldDataId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ETrading.Database.Tables.Transaction", "Transaction")
                        .WithMany("ValidationMessages")
                        .HasForeignKey("TransactionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ETrading.Database.Tables.Customer.ValidationRule", "ValidationRule")
                        .WithMany()
                        .HasForeignKey("ValidationRuleId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("ETrading.Database.Tables.Email.Inbox", b =>
                {
                    b.HasOne("ETrading.Database.Tables.Customer.CustomerDetail", "Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();
                });

            modelBuilder.Entity("ETrading.Database.Tables.Transaction", b =>
                {
                    b.HasOne("DataStore.Database.Tables.TransactionGroup", "AssignedTransactionGroup")
                        .WithMany()
                        .HasForeignKey("AssignedTransactionGroupId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("DataStore.Database.Tables.TransactionGroup", "CustomerTransactionGroup")
                        .WithMany()
                        .HasForeignKey("CustomerTransactionGroupId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ETrading.Database.Tables.Document", "Document")
                        .WithMany()
                        .HasForeignKey("DocumentId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("DataStore.Database.Tables.TransactionHeader", "TransactionHeader")
                        .WithMany()
                        .HasForeignKey("TransactionHeaderId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ETrading.Database.Tables.Customer.VATGroup", "VATGroup")
                        .WithMany()
                        .HasForeignKey("VATGroupId")
                        .OnDelete(DeleteBehavior.Restrict);
                });
#pragma warning restore 612, 618
        }
    }
}
