﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace DataStore.Migrations
{
    public partial class ReconciliationDatesIndexes : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_TransactionHeader_ApprovalDate",
                schema: "Datastore",
                table: "TransactionHeader",
                column: "ApprovalDate")
                .Annotation("SqlServer:Clustered", false);

            migrationBuilder.CreateIndex(
                name: "IX_TransactionHeader_ApprovalReleaseDate",
                schema: "Datastore",
                table: "TransactionHeader",
                column: "ApprovalReleaseDate")
                .Annotation("SqlServer:Clustered", false);

            migrationBuilder.CreateIndex(
                name: "IX_TransactionHeader_MatchingDate",
                schema: "Datastore",
                table: "TransactionHeader",
                column: "MatchingDate")
                .Annotation("SqlServer:Clustered", false);

            migrationBuilder.CreateIndex(
                name: "IX_TransactionHeader_MatchingReleaseDate",
                schema: "Datastore",
                table: "TransactionHeader",
                column: "MatchingReleaseDate")
                .Annotation("SqlServer:Clustered", false);

            migrationBuilder.CreateIndex(
                name: "IX_TransactionHeader_OnHoldDate",
                schema: "Datastore",
                table: "TransactionHeader",
                column: "OnHoldDate")
                .Annotation("SqlServer:Clustered", false);

            migrationBuilder.CreateIndex(
                name: "IX_TransactionHeader_OnHoldReleaseDate",
                schema: "Datastore",
                table: "TransactionHeader",
                column: "OnHoldReleaseDate")
                .Annotation("SqlServer:Clustered", false);

            migrationBuilder.CreateIndex(
                name: "IX_TransactionHeader_StatementEndDate",
                schema: "Datastore",
                table: "TransactionHeader",
                column: "StatementEndDate")
                .Annotation("SqlServer:Clustered", false);

            migrationBuilder.CreateIndex(
                name: "IX_TransactionHeader_StatementStartDate",
                schema: "Datastore",
                table: "TransactionHeader",
                column: "StatementStartDate")
                .Annotation("SqlServer:Clustered", false);

            migrationBuilder.CreateIndex(
                name: "IX_TransactionHeader_SuspnseDate",
                schema: "Datastore",
                table: "TransactionHeader",
                column: "SuspnseDate")
                .Annotation("SqlServer:Clustered", false);

            migrationBuilder.CreateIndex(
                name: "IX_TransactionHeader_SuspnseReleaseDate",
                schema: "Datastore",
                table: "TransactionHeader",
                column: "SuspnseReleaseDate")
                .Annotation("SqlServer:Clustered", false);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_TransactionHeader_ApprovalDate",
                schema: "Datastore",
                table: "TransactionHeader");

            migrationBuilder.DropIndex(
                name: "IX_TransactionHeader_ApprovalReleaseDate",
                schema: "Datastore",
                table: "TransactionHeader");

            migrationBuilder.DropIndex(
                name: "IX_TransactionHeader_MatchingDate",
                schema: "Datastore",
                table: "TransactionHeader");

            migrationBuilder.DropIndex(
                name: "IX_TransactionHeader_MatchingReleaseDate",
                schema: "Datastore",
                table: "TransactionHeader");

            migrationBuilder.DropIndex(
                name: "IX_TransactionHeader_OnHoldDate",
                schema: "Datastore",
                table: "TransactionHeader");

            migrationBuilder.DropIndex(
                name: "IX_TransactionHeader_OnHoldReleaseDate",
                schema: "Datastore",
                table: "TransactionHeader");

            migrationBuilder.DropIndex(
                name: "IX_TransactionHeader_StatementEndDate",
                schema: "Datastore",
                table: "TransactionHeader");

            migrationBuilder.DropIndex(
                name: "IX_TransactionHeader_StatementStartDate",
                schema: "Datastore",
                table: "TransactionHeader");

            migrationBuilder.DropIndex(
                name: "IX_TransactionHeader_SuspnseDate",
                schema: "Datastore",
                table: "TransactionHeader");

            migrationBuilder.DropIndex(
                name: "IX_TransactionHeader_SuspnseReleaseDate",
                schema: "Datastore",
                table: "TransactionHeader");
        }
    }
}
