﻿using System;
using System.Collections.Generic;
using System.Linq.Expressions;

namespace DataStore.DataLayer.Interfaces
{
    public interface IGenericDataLayer<T>
        where T : class
    {
        void AddUpdate(object id, T obj);

        void Add(T obj);

        void AddRange(List<T> objs);

        void Delete(object id);

        void DeleteRange(Expression<Func<T, bool>> predicate);

        T FirstOrDefault(Expression<Func<T, bool>> predicate);

        T FirstOrDefault(List<Expression<Func<T, bool>>> predicates);

        IEnumerable<T> GetAll();

        T GetById(object id);

        T GetByIdWithIncludes(object id, Expression<Func<T, object>>[] includeProperties);

        List<T> GetList(Expression<Func<T, bool>> predicate);

        List<T> GetList(Expression<Func<T, bool>> predicate, Expression<Func<T, object>> includeProperties);

        List<T> GetList(Expression<Func<T, bool>> predicate, Expression<Func<T, object>>[] includeProperties);

        List<T> GetList(List<Expression<Func<T, bool>>> predicates);

        List<T> GetList(List<Expression<Func<T, bool>>> predicates, Expression<Func<T, object>>[] includeProperties);

        List<T> GetListNoTracking(Expression<Func<T, bool>> predicate);

        void SaveChanges();

        void Update(T obj);

        void UpdateRange(List<T> objs);

        void DetachAllEntities();

        void Detach(Guid id);

        void AddUpdateDeleteCollection(
            List<T> updateItems,
            List<T> oldItems,
            Func<T, T, bool> compair,
            Func<T, List<T>, bool> remove);

        void DetachRange(List<T> objs);

        void UnchangedRange(List<T> objs);
    }
}