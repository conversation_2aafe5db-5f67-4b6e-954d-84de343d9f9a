﻿// <copyright file="IDataResult.cs" company="OpenECX">
// Copyright (c) OpenECX. All rights reserved.
// </copyright>

namespace ECXCommon.Result
{
    public interface IData<TData>
    {
        TData Data { get; }
    }

    /// <summary>
    /// Represents the outcome of an operation as success or failure.
    /// Successful operations return data of type {{TData}}.
    /// </summary>
    /// <typeparam name="TData">
    /// The data returned when an operation succeeds.
    /// </typeparam>
    public interface IDataResult<TData> : IData<TData>, IResult
    {
    }
}
