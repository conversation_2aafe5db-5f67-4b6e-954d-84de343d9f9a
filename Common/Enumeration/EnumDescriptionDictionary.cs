﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Enumeration
{
    /// <summary>
    /// A <see cref="Dictionary{string, TEnum}"/> of <see cref="DescriptionAttribute"/>
    /// and Enum values for any <see cref="Enum"/> type.
    /// 
    /// <see cref="https://www.codeproject.com/Articles/761518/A-Dictionary-for-Enum-Descriptions"/>
    /// </summary>
    /// <typeparam name="TEnum">
    /// The <see cref="Enum"/> type for which to build a descriptions Dictionary.</typeparam>
    /// <remarks>
    /// Looks for a <see cref="DescriptionAttribute"/> on each field of the Enum, if found, it 
    /// uses the value provided by that. If not found, or empty, throws an exception.
    /// </remarks>
    public class EnumDescriptionDictionary<TEnum> : Dictionary<string, TEnum>
        where TEnum : struct, IComparable, IConvertible, IFormattable
    {
        private EnumDescriptionDictionary()
        {
        }

        public static Dictionary<string, TEnum> Create()
        {
            return EnumAttributeDictionary<TEnum, DescriptionAttribute, string>.Create(descriptionAttribute => descriptionAttribute.Description);
        }
    }
}
