﻿using System;
using System.Collections.Generic;
using System.Reflection;

namespace Common.Enumeration
{
    /// <summary>
    /// A <see cref="Dictionary{TAttributeProperty, TEnum}"/> of <see cref="TAttributeProperty"/>
    /// and Enum values for any <see cref="Enum"/> type.
    /// </summary>
    /// <typeparam name="TEnum">
    /// The <see cref="Enum"/> type for which to build a descriptions Dictionary.
    /// </typeparam>
    /// <typeparam name="TAttribute">
    /// The type of <see cref="System.Attribute"/> that decorates each <see cref="Enum"/>.
    /// </typeparam>
    /// <typeparam name="TAttributeProperty">
    /// The type of the property, the values of which will form the keys to the <see cref="Dictionary{TAttributeProperty, TEnum}"/>.
    /// </typeparam>
    /// <remarks>
    /// Looks for a {TAttribute} <see cref="System.Attribute"/> on each field of the <see cref="Enum"/>, if found, it 
    /// uses the value of the property extracted using <code>attributePropertySelector</code> as the key.
    /// If an attribute property is not found, or is null, throws an exception. If duplicate values of the {TAttribute}
    /// property exist, an exception is thrown.
    /// </remarks>
    public class EnumAttributeDictionary<TEnum, TAttribute, TAttributeProperty> : Dictionary<TAttributeProperty, TEnum> 
        where TEnum : struct, IComparable, IConvertible, IFormattable
        where TAttribute : System.Attribute
    {
        private EnumAttributeDictionary()
        {
        }

        public static EnumAttributeDictionary<TEnum, TAttribute, TAttributeProperty> Create(Func<TAttribute, TAttributeProperty> attributePropertySelector)
        {
            if (attributePropertySelector == null)
            {
                throw new ArgumentNullException(nameof(attributePropertySelector));
            }

            var type = typeof(TEnum);
            if (!type.IsEnum)
            {
                // There is no BCL exception suitable for invalid generic type parameters.
                throw new NotSupportedException($"Generic parameter {type.FullName} must be of type Enum.");
            }

            var result = new EnumAttributeDictionary<TEnum, TAttribute, TAttributeProperty>();

            // These binding flags exclude the hidden, generated instance field named 'value__'. 
            var fields = typeof(TEnum).GetFields(BindingFlags.Public | BindingFlags.Static);
            foreach (var field in fields)
            {
                var descAtt = field.GetCustomAttribute<TAttribute>();
                if (descAtt != null)
                {
                    var key = attributePropertySelector(descAtt);
                    var attributePropertyType = typeof(TAttributeProperty);
                    if (key == null)
                    {
                        throw new Exception($"Enum value '{field.Name}' of {type.FullName} must have an {typeof(TAttribute).FullName} attribute with a non null value.");
                    }

                    result.Add(
                        key,
                        (TEnum)Enum.Parse(typeof(TEnum), field.Name));
                }
            }

            return result;
        }
    }
}
