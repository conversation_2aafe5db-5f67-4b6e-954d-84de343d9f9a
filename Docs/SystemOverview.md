# eHub System Overview

## Purpose & Scope

This document provides a comprehensive high-level overview of the eHub platform - an enterprise B2B electronic trading and document processing system. It covers the system's business purpose, architectural design, core capabilities, and how the various components work together to process millions of electronic documents annually.

## Prerequisites

- Basic understanding of B2B electronic document exchange
- Familiarity with .NET and microservices architecture
- Understanding of electronic trading concepts (invoices, purchase orders, etc.)

## Core Concepts

### What is eHub?

eHub is an enterprise-grade electronic document processing platform that facilitates Business-to-Business (B2B) trading relationships. The system automates the entire document lifecycle from ingestion through processing, validation, matching, approval, and export to external systems.

### Key Business Value
- **Document Automation**: Eliminates manual document processing
- **Three-Way Matching**: Automatically matches purchase orders, invoices, and receipts
- **Compliance**: Maintains comprehensive audit trails for regulatory requirements
- **Integration**: Seamlessly connects with existing ERP and accounting systems
- **Scalability**: Processes millions of documents with enterprise-grade reliability

## System Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        TP[Trading Portal<br/>Blazor]
        RP[Reporting Portal<br/>Blazor]
        NPA[Native PDF App<br/>WinForms]
    end
    
    subgraph "API Layer"
        TA[Trading API<br/>Main REST]
        EA[ECXIO API<br/>I/O Operations]
        RA[Reporting API]
        SA[Schedule API]
    end
    
    subgraph "Business Logic Layer"
        CBL[Core Business Logic<br/>eHub]
        PE[PDF Engine<br/>PDFNative]
        WE[Workflow Engine<br/>Approval/Validation]
    end
    
    subgraph "Data Layer"
        DS[DataStore<br/>Core Data]
        ADB[Audit DB<br/>ECXAudit]
        AuthDB[Auth DB<br/>ECXAuth]
        CDB[Config DB]
    end
    
    subgraph "Background Services"
        HF[Hangfire Background Processing]
        DP[Document Processing]
        EP[Email Processing]
        EJ[Export & Integration Jobs]
    end
    
    subgraph "Integration Layer"
        FS[File System<br/>Azure]
        AS2[AS2 Protocol]
        FTP[FTP/SFTP Polling]
        EM[Email Inbox]
    end
    
    TP --> TA
    RP --> RA
    NPA --> TA
    
    TA --> CBL
    EA --> CBL
    RA --> CBL
    SA --> HF
    
    CBL --> DS
    CBL --> PE
    CBL --> WE
    
    HF --> DP
    HF --> EP
    HF --> EJ
    
    FS --> CBL
    AS2 --> EA
    FTP --> EA
    EM --> EA
```

### Traditional Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────┐
│                        eHub Platform                            │
├─────────────────────────────────────────────────────────────────┤
│  Frontend Layer                                                 │
│  ┌───────────────┐  ┌─────────────────┐  ┌──────────────────┐   │
│  │ Trading Portal │  │ Reporting Portal │  │ Native PDF App   │   │
│  │ (Blazor)      │  │ (Blazor)        │  │ (WinForms)       │   │
│  └───────────────┘  └─────────────────┘  └──────────────────┘   │
├─────────────────────────────────────────────────────────────────┤
│  API Layer                                                      │
│  ┌─────────────┐  ┌──────────────┐  ┌─────────────┐  ┌────────┐ │
│  │ Trading API │  │ ECXIO API    │  │ Reporting   │  │Schedule│ │
│  │ (Main REST) │  │ (I/O Ops)    │  │ API         │  │ API    │ │
│  └─────────────┘  └──────────────┘  └─────────────┘  └────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  Business Logic Layer                                           │
│  ┌───────────────┐  ┌─────────────┐  ┌─────────────────────────┐ │
│  │ Core Business │  │ PDF Engine  │  │ Workflow Engine         │ │
│  │ Logic (eHub)  │  │ (PDFNative) │  │ (Approval/Validation)   │ │
│  └───────────────┘  └─────────────┘  └─────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  Data Layer                                                     │
│  ┌─────────────┐  ┌──────────────┐  ┌─────────────┐  ┌────────┐ │
│  │ DataStore   │  │ Audit DB     │  │ Auth DB     │  │ Config │ │
│  │ (Core Data) │  │ (ECXAudit)   │  │ (ECXAuth)   │  │ DB     │ │
│  └─────────────┘  └──────────────┘  └─────────────┘  └────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  Background Services                                            │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │ Hangfire Background Processing                              │ │
│  │ ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐ │ │
│  │ │ Document    │ │ Email       │ │ Export & Integration    │ │ │
│  │ │ Processing  │ │ Processing  │ │ Jobs                    │ │ │
│  │ └─────────────┘ └─────────────┘ └─────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  Integration Layer                                              │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌────────┐ │
│  │ File System │  │ AS2         │  │ FTP/SFTP    │  │ Email  │ │
│  │ (Azure)     │  │ Protocol    │  │ Polling     │  │ Inbox  │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### Solution Architecture

The eHub platform is organized into **11 separate solution files**, each serving specific domain areas:

#### Core Business Solutions
- **`eHub.sln`** - Main umbrella solution containing core components
- **`ETrading.sln`** - Core trading functionality and main APIs
- **`DataStore.sln`** - Transaction data management and persistence

#### Specialized Service Solutions  
- **`ECXAuth.sln`** - Authentication, authorization, and OIDC
- **`ECXIO.Core.sln`** - I/O operations, file processing, integrations
- **`Reporting.sln`** - Analytics, reporting, and dashboards
- **`BackgroundServices.sln`** - Hangfire background job processing
- **`Audit.sln`** - Audit trails and compliance logging

#### Supporting Solutions
- **`ECXCommon.sln`** - Shared utilities and common components
- **`ETradingPackages.sln`** - Package management and distribution
- **`ETradingEF.sln`** - Entity Framework data access layer

## Core Business Processes

### Document Processing Pipeline

```
Document Ingestion → Extraction → Validation → Matching → Approval → Export
       ↓                ↓           ↓          ↓          ↓         ↓
   [Multiple      [PDF Parser]  [Business   [3-Way    [Workflow  [Multiple
    Sources]                     Rules]     Matching]  Engine]    Formats]
```

#### 1. **Document Ingestion**
- **Email Processing**: Automated inbox monitoring and attachment extraction
- **FTP/SFTP Polling**: Scheduled file retrieval from trading partners
- **AS2 Protocol**: Secure B2B document exchange
- **Manual Upload**: Web interface for direct document upload
- **API Integration**: REST endpoints for external system integration

#### 2. **Document Extraction**
- **PDF Processing**: Native PDF parser with OCR capabilities
- **Structured Data**: XML, JSON, EDI format processing
- **Spreadsheet Data**: Excel and CSV file processing
- **Field Mapping**: Dynamic field extraction and transformation
- **Document Classification**: Automatic document type identification

#### 3. **Validation & Business Rules**
- **Field Validation**: Data type, format, and range checking
- **Business Rules**: Custom validation logic per customer
- **Duplicate Detection**: Prevents duplicate document processing
- **Completeness Checks**: Ensures required fields are populated
- **Compliance Validation**: Regulatory requirement verification

#### 4. **Matching & Reconciliation**
- **Three-Way Matching**: Purchase Order ↔ Invoice ↔ Goods Receipt
- **Statement Matching**: Bank statement transaction reconciliation
- **Partial Matching**: Handles quantity and amount variances
- **Manual Override**: User intervention for complex scenarios
- **Match Analytics**: Reports on matching success rates

#### 5. **Approval Workflows**
- **Configurable Rules**: Customer-specific approval hierarchies
- **Dollar Thresholds**: Automatic approval for amounts under limits
- **Escalation Logic**: Time-based escalation to higher authorities
- **Bulk Approval**: Batch processing for efficiency
- **Mobile Approval**: Support for mobile approval workflows

#### 6. **Export & Integration**
- **ERP Integration**: Direct integration with major ERP systems
- **Custom Formats**: Configurable export format generation
- **Real-time Sync**: Immediate export upon approval
- **Batch Processing**: Scheduled bulk exports
- **Error Handling**: Comprehensive retry and failure management

## Technology Stack

### Core Technologies
- **Runtime**: .NET 7.0
- **Web Framework**: ASP.NET Core
- **Database**: SQL Server with Entity Framework Core 7.0
- **Cloud Platform**: Microsoft Azure
- **UI Framework**: Blazor Server/WebAssembly

### Key Dependencies
- **Background Jobs**: Hangfire for distributed job processing
- **Dependency Injection**: Castle Windsor container
- **Object Mapping**: AutoMapper for entity transformations
- **PDF Processing**: Adobe PDF Services SDK + Custom parser
- **Email**: MailKit for advanced email processing
- **Authentication**: OpenID Connect (OIDC) with JWT tokens

### Database Architecture
- **Multiple Contexts**: Specialized database contexts for different domains
- **Entity Framework**: Code-first migrations with extensive change tracking
- **Audit Trails**: Comprehensive audit logging for compliance
- **Multi-Tenancy**: Customer data isolation and security
- **Performance**: Optimized queries with Dapper for critical paths

## Key Features & Capabilities

### Document Processing
- **Multi-Format Support**: PDF, XML, JSON, EDI, Excel, CSV
- **OCR Integration**: Text extraction from scanned documents
- **Table Recognition**: Automated line item extraction
- **Font Handling**: Support for complex PDF font encodings
- **Document Splitting**: Automatic separation of multi-document files

### Business Intelligence
- **Real-time Dashboards**: Live processing metrics and KPIs
- **Custom Reports**: User-configurable report generation
- **Exception Management**: Automated flagging of processing issues
- **Performance Analytics**: System performance and bottleneck analysis
- **Compliance Reporting**: Audit-ready compliance reports

### Integration Capabilities
- **REST APIs**: Comprehensive API coverage for all business functions
- **Webhook Support**: Real-time notifications to external systems
- **File-based Integration**: Automated file exchange protocols
- **Message Queuing**: Reliable message processing with retry logic
- **Real-time Sync**: Live data synchronization with external systems

### Security & Compliance
- **Multi-Tenant Architecture**: Complete customer data isolation
- **Role-Based Access**: Granular permission management
- **Audit Logging**: Immutable audit trails for all changes
- **Data Encryption**: Encryption at rest and in transit
- **Compliance Standards**: SOX, GDPR, and industry-specific requirements

## Deployment Architecture

### Environment Strategy
- **Development**: Local development with containerized dependencies
- **Test**: Automated testing environment with CI/CD integration
- **UAT**: User acceptance testing with production-like data
- **PreProduction**: Staging environment mirroring production
- **Production**: High-availability production deployment

### Scalability Features
- **Microservices Design**: Independent scaling of components
- **Background Job Distribution**: Horizontally scalable job processing
- **Database Sharding**: Customer-based data partitioning
- **Cloud-Native**: Azure-optimized deployment with auto-scaling
- **Load Balancing**: Distributed load across multiple instances

## Performance Characteristics

### Processing Capacity
- **Document Volume**: Millions of documents processed annually
- **Concurrent Users**: Hundreds of simultaneous users
- **Peak Processing**: Handles high-volume batch processing
- **Response Times**: Sub-second response for most operations
- **Availability**: 99.9% uptime SLA with redundancy

### Optimization Features
- **Caching**: Multi-level caching strategy for performance
- **Connection Pooling**: Optimized database connection management
- **Async Processing**: Non-blocking operations for user experience
- **Background Jobs**: Offloaded heavy processing to background
- **CDN Integration**: Static asset delivery optimization

## Business Impact

### Operational Efficiency
- **Process Automation**: 90%+ reduction in manual document handling
- **Error Reduction**: Automated validation reduces human errors
- **Cost Savings**: Significant reduction in operational costs
- **Faster Processing**: Documents processed in minutes vs. hours/days
- **Improved Accuracy**: Higher accuracy rates through automation

### Compliance Benefits
- **Audit Readiness**: Complete audit trails for all transactions
- **Regulatory Compliance**: Built-in compliance with industry standards
- **Data Integrity**: Immutable audit logs prevent data tampering
- **Risk Reduction**: Reduced compliance and operational risks
- **Transparency**: Full visibility into document processing workflows

## Related Documents

- **[DeveloperQuickStart.md](./DeveloperQuickStart.md)** - Getting started with development
- **[BuildAndDeployment.md](./BuildAndDeployment.md)** - Build and deployment procedures
- **[DatabaseArchitecture.md](./DatabaseArchitecture.md)** - Detailed database design
- **[APIArchitecture.md](./APIArchitecture.md)** - REST API documentation
- **[SecurityArchitecture.md](./SecurityArchitecture.md)** - Security implementation details

---

*This overview provides the foundation for understanding the eHub platform's architecture and capabilities. Refer to the specialized documentation for detailed implementation guidance.*