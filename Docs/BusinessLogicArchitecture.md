# eHub Business Logic Architecture

## Purpose & Scope

This document provides comprehensive documentation of the eHub business logic architecture, including core business processes, workflow orchestration, document processing pipelines, validation engines, and integration patterns. It serves as the definitive guide for understanding how business requirements are implemented in the eHub platform.

## Prerequisites

- Understanding of business process management and workflow engines
- Knowledge of document processing and electronic trading concepts
- Familiarity with the eHub database and API architecture
- Basic understanding of state machines and business rule engines

## Core Concepts

### Business Logic Philosophy

The eHub business logic layer is built on several key architectural principles:

1. **State-Driven Processing**: All business operations follow a state machine pattern
2. **Rule-Based Decisions**: Business rules are externalized and configurable
3. **Pipeline Architecture**: Documents flow through well-defined processing stages
4. **Event-Driven Operations**: Asynchronous processing with comprehensive event handling
5. **Clean Architecture**: Business logic isolated from infrastructure concerns

### Business Logic Overview

```
eHub Business Logic Architecture
├── Document Processing Engine
│   ├── Ingestion & Classification
│   ├── Extraction & Parsing
│   ├── Validation & Business Rules
│   └── Workflow & Approval
├── Transaction Management
│   ├── State Machine Orchestration
│   ├── Matching & Reconciliation
│   ├── Approval Workflows
│   └── Export & Integration
├── Background Processing
│   ├── Job Orchestration
│   ├── Scheduled Operations
│   ├── Error Handling & Retry
│   └── Performance Monitoring
└── Business Rule Engine
    ├── Validation Rules
    ├── Processing Rules
    ├── Approval Rules
    └── Integration Rules
```

## Document Processing Workflow Engine

### Central Processing Coordinator

**Location**: `eHub/BusinessLogic/ProcessDocument.cs`

The document processing engine implements a sophisticated pipeline that handles the complete document lifecycle:

```csharp
public class ProcessDocument
{
    public async Task<ProcessResult> ProcessDocumentAsync(Guid documentId, ProcessingOptions options)
    {
        // 1. Document Classification
        var classification = await ClassifyDocumentAsync(documentId);
        
        // 2. Extraction Strategy Selection
        var extractor = _extractorFactory.CreateExtractor(classification.DocumentType);
        
        // 3. Data Extraction
        var extractionResult = await extractor.ExtractAsync(documentId);
        
        // 4. Business Rule Validation
        var validationResult = await _validationEngine.ValidateAsync(extractionResult);
        
        // 5. Transaction Creation/Update
        var transaction = await CreateOrUpdateTransactionAsync(extractionResult);
        
        // 6. Workflow Initiation
        await _workflowEngine.InitiateWorkflowAsync(transaction);
        
        return ProcessResult.Success(transaction.Id);
    }
}
```

### Document Processing Pipeline Stages

#### Stage 1: Document Ingestion & Classification

```csharp
public class DocumentClassificationEngine
{
    public async Task<ClassificationResult> ClassifyDocumentAsync(Document document)
    {
        // Multi-strategy classification
        var strategies = new IClassificationStrategy[]
        {
            new FileExtensionClassifier(),
            new ContentAnalysisClassifier(),
            new SenderBasedClassifier(),
            new TemplateMatchingClassifier()
        };
        
        foreach (var strategy in strategies)
        {
            var result = await strategy.ClassifyAsync(document);
            if (result.Confidence > 0.8)
            {
                return result;
            }
        }
        
        return ClassificationResult.Unknown();
    }
}
```

**Classification Types**:
- **Invoice** - Customer invoices requiring payment processing
- **Purchase Order** - Purchase requests requiring matching
- **Credit Note** - Credit adjustments and returns
- **Statement** - Bank/supplier statements for reconciliation
- **GRN** - Goods Received Notes for delivery confirmation
- **Quote** - Price quotations and estimates

#### Stage 2: Data Extraction & Parsing

```csharp
public class ExtractionEngine
{
    public async Task<ExtractionResult> ExtractAsync(Document document)
    {
        var extractor = _extractorFactory.CreateExtractor(document.Type);
        
        return document.Type switch
        {
            DocumentType.PDF => await _pdfExtractor.ExtractAsync(document),
            DocumentType.XML => await _xmlExtractor.ExtractAsync(document),
            DocumentType.JSON => await _jsonExtractor.ExtractAsync(document),
            DocumentType.EDI => await _ediExtractor.ExtractAsync(document),
            DocumentType.Excel => await _excelExtractor.ExtractAsync(document),
            _ => await _genericExtractor.ExtractAsync(document)
        };
    }
}
```

**Extraction Capabilities**:
- **PDF Native Processing** - Custom PDF parser with coordinate-based field extraction
- **Structured Data** - XML, JSON, EDI format parsing
- **Spreadsheet Processing** - Excel and CSV data extraction
- **OCR Integration** - Text recognition for scanned documents
- **Template Matching** - Supplier-specific template processing

#### Stage 3: Business Rule Validation

```csharp
public class ValidationEngine
{
    public async Task<ValidationResult> ValidateAsync(ExtractionResult extraction)
    {
        var validators = await GetValidatorsAsync(extraction.DocumentType, extraction.CustomerId);
        var results = new List<ValidationResult>();
        
        foreach (var validator in validators)
        {
            var result = await validator.ValidateAsync(extraction);
            results.Add(result);
        }
        
        return ValidationResult.Combine(results);
    }
}
```

**Validation Types**:
```csharp
public interface IBusinessValidator
{
    Task<ValidationResult> ValidateAsync(ExtractionResult extraction);
}

// Field-level validation
public class MandatoryFieldValidator : IBusinessValidator
{
    public async Task<ValidationResult> ValidateAsync(ExtractionResult extraction)
    {
        var requiredFields = await GetRequiredFieldsAsync(extraction.DocumentType);
        var missingFields = requiredFields.Where(f => !extraction.HasField(f)).ToList();
        
        return missingFields.Any() 
            ? ValidationResult.Error($"Missing required fields: {string.Join(", ", missingFields)}")
            : ValidationResult.Success();
    }
}

// Business rule validation
public class ArithmeticValidator : IBusinessValidator
{
    public async Task<ValidationResult> ValidateAsync(ExtractionResult extraction)
    {
        var lineTotal = extraction.Lines.Sum(l => l.LineTotal);
        var headerTotal = extraction.Header.Total;
        var tolerance = await GetToleranceAsync(extraction.CustomerId);
        
        if (Math.Abs(lineTotal - headerTotal) > tolerance)
        {
            return ValidationResult.Warning($"Line total ({lineTotal:C}) does not match header total ({headerTotal:C})");
        }
        
        return ValidationResult.Success();
    }
}
```

#### Stage 4: Workflow Initiation

```csharp
public class WorkflowEngine
{
    public async Task InitiateWorkflowAsync(Transaction transaction)
    {
        var workflow = await GetWorkflowAsync(transaction.Type, transaction.CustomerId);
        
        // Initialize state machine
        var stateMachine = new TransactionStateMachine(transaction);
        
        // Determine initial state based on validation results
        var initialState = DetermineInitialState(transaction.ValidationResults);
        
        // Transition to initial state
        await stateMachine.TransitionToAsync(initialState);
        
        // Queue next processing step
        await QueueNextStepAsync(transaction, workflow);
    }
}
```

## Transaction State Machine

### State Management Architecture

```csharp
public class TransactionStateMachine
{
    public enum TransactionState
    {
        // Initial states
        Created,
        ReadyForProcessing,
        FailedExtraction,
        
        // Validation states
        ValidationPending,
        ValidationSuccess,
        ValidationFailed,
        Suspense,
        
        // Matching states
        MatchingPending,
        MatchingSuccess,
        MatchingFailed,
        MatchRejected,
        
        // Approval states
        ApprovalPending,
        ApprovalSuccess,
        ApprovalRejected,
        OnHold,
        
        // Export states
        ExportPending,
        ExportOnHold,
        ExportSuccess,
        ExportFailed,
        SentForEmail,
        
        // Final states
        Completed,
        Cancelled,
        Archived
    }
    
    public async Task<StateTransitionResult> TransitionToAsync(TransactionState newState)
    {
        // Validate transition
        if (!IsValidTransition(_currentState, newState))
        {
            return StateTransitionResult.InvalidTransition(_currentState, newState);
        }
        
        // Execute transition logic
        await ExecuteTransitionLogicAsync(_currentState, newState);
        
        // Update state
        _currentState = newState;
        await SaveStateAsync();
        
        // Trigger events
        await _eventBus.PublishAsync(new StateChangedEvent(_transaction.Id, newState));
        
        return StateTransitionResult.Success(newState);
    }
}
```

### State Transition Rules

```csharp
public class StateTransitionRules
{
    private static readonly Dictionary<TransactionState, List<TransactionState>> ValidTransitions = new()
    {
        {
            TransactionState.Created, new List<TransactionState>
            {
                TransactionState.ReadyForProcessing,
                TransactionState.FailedExtraction,
                TransactionState.Cancelled
            }
        },
        {
            TransactionState.ReadyForProcessing, new List<TransactionState>
            {
                TransactionState.ValidationPending,
                TransactionState.ValidationSuccess,
                TransactionState.Suspense
            }
        },
        {
            TransactionState.ValidationSuccess, new List<TransactionState>
            {
                TransactionState.MatchingPending,
                TransactionState.ApprovalPending,
                TransactionState.ExportPending
            }
        }
        // ... additional transition rules
    };
}
```

## PDF Processing Engine

### Native PDF Parser Architecture

**Location**: `PDFNative/`

The PDF processing engine provides sophisticated document analysis capabilities:

```csharp
public class PDFProcessor
{
    public async Task<PDFProcessingResult> ProcessPDFAsync(byte[] pdfData, ProcessingOptions options)
    {
        // 1. PDF Structure Analysis
        var structure = await AnalyzePDFStructureAsync(pdfData);
        
        // 2. Font and Encoding Analysis
        var fontMap = await BuildFontMappingAsync(structure);
        
        // 3. Text Extraction with Coordinates
        var textBlocks = await ExtractTextWithCoordinatesAsync(structure, fontMap);
        
        // 4. Table Detection and Extraction
        var tables = await DetectAndExtractTablesAsync(textBlocks);
        
        // 5. Field Identification
        var fields = await IdentifyFieldsAsync(textBlocks, tables);
        
        // 6. Business Data Extraction
        var businessData = await ExtractBusinessDataAsync(fields);
        
        return new PDFProcessingResult
        {
            TextBlocks = textBlocks,
            Tables = tables,
            Fields = fields,
            BusinessData = businessData
        };
    }
}
```

### Coordinate-Based Field Extraction

```csharp
public class CoordinateBasedExtractor
{
    public async Task<FieldExtractionResult> ExtractFieldAsync(
        PDFPage page, 
        FieldDefinition fieldDef)
    {
        var searchArea = new Rectangle(
            fieldDef.X, 
            fieldDef.Y, 
            fieldDef.Width, 
            fieldDef.Height);
        
        var textInArea = page.TextBlocks
            .Where(tb => searchArea.Contains(tb.Bounds))
            .OrderBy(tb => tb.Y)
            .ThenBy(tb => tb.X)
            .ToList();
        
        var extractedText = string.Join(" ", textInArea.Select(tb => tb.Text));
        
        // Apply field-specific processing
        var processedValue = await ProcessFieldValueAsync(extractedText, fieldDef.Type);
        
        return new FieldExtractionResult
        {
            FieldName = fieldDef.Name,
            RawValue = extractedText,
            ProcessedValue = processedValue,
            Confidence = CalculateConfidence(extractedText, fieldDef)
        };
    }
}
```

## Business Rule Engine

### Rule Definition and Management

```csharp
public class BusinessRuleEngine
{
    public async Task<RuleExecutionResult> ExecuteRulesAsync(
        RuleContext context, 
        IEnumerable<BusinessRule> rules)
    {
        var results = new List<RuleResult>();
        
        foreach (var rule in rules.OrderBy(r => r.Priority))
        {
            try
            {
                var result = await ExecuteRuleAsync(context, rule);
                results.Add(result);
                
                // Stop on critical failure
                if (result.Severity == RuleSeverity.Critical && !result.Passed)
                {
                    break;
                }
            }
            catch (Exception ex)
            {
                results.Add(RuleResult.Error(rule.Id, $"Rule execution failed: {ex.Message}"));
            }
        }
        
        return new RuleExecutionResult(results);
    }
}
```

### Rule Types and Implementation

```csharp
public abstract class BusinessRule
{
    public string Id { get; set; }
    public string Name { get; set; }
    public RuleSeverity Severity { get; set; }
    public int Priority { get; set; }
    public bool IsActive { get; set; }
    
    public abstract Task<RuleResult> ExecuteAsync(RuleContext context);
}

// Field validation rule
public class MandatoryFieldRule : BusinessRule
{
    public string FieldName { get; set; }
    
    public override async Task<RuleResult> ExecuteAsync(RuleContext context)
    {
        var fieldValue = context.GetFieldValue(FieldName);
        
        if (string.IsNullOrWhiteSpace(fieldValue))
        {
            return RuleResult.Failed(Id, $"Field '{FieldName}' is mandatory but not provided");
        }
        
        return RuleResult.Passed(Id, $"Field '{FieldName}' validation successful");
    }
}

// Business logic rule
public class TotalMatchRule : BusinessRule
{
    public decimal TolerancePercentage { get; set; } = 0.01m; // 1%
    
    public override async Task<RuleResult> ExecuteAsync(RuleContext context)
    {
        var headerTotal = context.GetDecimalField("Total");
        var lineTotal = context.GetDecimalField("LineTotal");
        
        var difference = Math.Abs(headerTotal - lineTotal);
        var tolerance = headerTotal * TolerancePercentage;
        
        if (difference > tolerance)
        {
            return RuleResult.Failed(Id, 
                $"Total mismatch: Header {headerTotal:C}, Lines {lineTotal:C}, Difference {difference:C}");
        }
        
        return RuleResult.Passed(Id, "Total validation successful");
    }
}
```

## Transaction Matching Engine

### Three-Way Matching Logic

```csharp
public class ThreeWayMatchingEngine
{
    public async Task<MatchingResult> PerformThreeWayMatchAsync(
        Guid purchaseOrderId,
        Guid goodsReceiptId,
        Guid invoiceId)
    {
        // Load transactions
        var po = await _repository.GetTransactionAsync(purchaseOrderId);
        var grn = await _repository.GetTransactionAsync(goodsReceiptId);
        var invoice = await _repository.GetTransactionAsync(invoiceId);
        
        // Validate prerequisites
        var prerequisites = await ValidateMatchingPrerequisitesAsync(po, grn, invoice);
        if (!prerequisites.IsValid)
        {
            return MatchingResult.Failed(prerequisites.Errors);
        }
        
        // Perform line-level matching
        var lineMatches = await PerformLineMatchingAsync(po.Lines, grn.Lines, invoice.Lines);
        
        // Calculate tolerance-based matching
        var toleranceMatches = await ApplyToleranceRulesAsync(lineMatches);
        
        // Generate matching recommendations
        var recommendations = await GenerateMatchingRecommendationsAsync(toleranceMatches);
        
        // Create match records
        var matchRecords = await CreateMatchRecordsAsync(recommendations);
        
        return MatchingResult.Success(matchRecords);
    }
}
```

### Tolerance-Based Matching

```csharp
public class ToleranceMatchingEngine
{
    public async Task<ToleranceMatchResult> ApplyToleranceAsync(
        TransactionLine poLine,
        TransactionLine invoiceLine,
        MatchingTolerance tolerance)
    {
        var results = new List<ToleranceResult>();
        
        // Quantity tolerance
        if (tolerance.QuantityTolerance.HasValue)
        {
            var qtyDiff = Math.Abs(poLine.Quantity - invoiceLine.Quantity);
            var qtyTolerance = poLine.Quantity * tolerance.QuantityTolerance.Value;
            
            results.Add(new ToleranceResult
            {
                Type = ToleranceType.Quantity,
                Passed = qtyDiff <= qtyTolerance,
                ActualDifference = qtyDiff,
                AllowedTolerance = qtyTolerance
            });
        }
        
        // Price tolerance
        if (tolerance.PriceTolerance.HasValue)
        {
            var priceDiff = Math.Abs(poLine.UnitPrice - invoiceLine.UnitPrice);
            var priceTolerance = poLine.UnitPrice * tolerance.PriceTolerance.Value;
            
            results.Add(new ToleranceResult
            {
                Type = ToleranceType.Price,
                Passed = priceDiff <= priceTolerance,
                ActualDifference = priceDiff,
                AllowedTolerance = priceTolerance
            });
        }
        
        return new ToleranceMatchResult(results);
    }
}
```

## Approval Workflow Engine

### Workflow Configuration

```csharp
public class ApprovalWorkflowEngine
{
    public async Task<WorkflowResult> InitiateApprovalWorkflowAsync(Transaction transaction)
    {
        // Get customer-specific workflow configuration
        var workflow = await GetWorkflowConfigurationAsync(transaction.CustomerId, transaction.Type);
        
        // Determine approval requirements
        var approvalRequirements = await DetermineApprovalRequirementsAsync(transaction, workflow);
        
        // Create approval tasks
        var approvalTasks = await CreateApprovalTasksAsync(transaction, approvalRequirements);
        
        // Initiate first approval step
        var firstStep = approvalTasks.OrderBy(t => t.Sequence).First();
        await InitiateApprovalStepAsync(firstStep);
        
        return WorkflowResult.Success(approvalTasks.Select(t => t.Id).ToList());
    }
}
```

### Approval Rules Engine

```csharp
public class ApprovalRulesEngine
{
    public async Task<ApprovalRequirement> DetermineRequirementsAsync(Transaction transaction)
    {
        var rules = await GetApprovalRulesAsync(transaction.CustomerId);
        var requirement = new ApprovalRequirement();
        
        foreach (var rule in rules.OrderBy(r => r.Priority))
        {
            if (await rule.AppliesAsync(transaction))
            {
                await rule.ApplyAsync(requirement, transaction);
            }
        }
        
        return requirement;
    }
}

// Example approval rule
public class AmountThresholdRule : IApprovalRule
{
    public decimal ThresholdAmount { get; set; }
    public string RequiredRole { get; set; }
    
    public async Task<bool> AppliesAsync(Transaction transaction)
    {
        return transaction.Total >= ThresholdAmount;
    }
    
    public async Task ApplyAsync(ApprovalRequirement requirement, Transaction transaction)
    {
        requirement.RequiredApprovers.Add(new ApprovalStep
        {
            RequiredRole = RequiredRole,
            Reason = $"Amount {transaction.Total:C} exceeds threshold {ThresholdAmount:C}"
        });
    }
}
```

## Export and Integration Engine

### Export Processing Pipeline

```csharp
public class ExportEngine
{
    public async Task<ExportResult> ExportTransactionAsync(
        Transaction transaction,
        ExportConfiguration config)
    {
        // 1. Data transformation
        var transformedData = await TransformDataAsync(transaction, config.MappingRules);
        
        // 2. Format generation
        var formattedData = await FormatDataAsync(transformedData, config.OutputFormat);
        
        // 3. Transport delivery
        var deliveryResult = await DeliverDataAsync(formattedData, config.Transport);
        
        // 4. Confirmation processing
        await ProcessDeliveryConfirmationAsync(deliveryResult, transaction);
        
        return ExportResult.Success(deliveryResult.TrackingId);
    }
}
```

### Data Transformation Engine

```csharp
public class DataTransformationEngine
{
    public async Task<TransformedData> TransformAsync(
        Transaction source,
        IEnumerable<MappingRule> mappingRules)
    {
        var transformed = new TransformedData();
        
        foreach (var rule in mappingRules)
        {
            var sourceValue = await ExtractSourceValueAsync(source, rule.SourcePath);
            var transformedValue = await ApplyTransformationAsync(sourceValue, rule.Transformation);
            
            transformed.SetValue(rule.TargetPath, transformedValue);
        }
        
        return transformed;
    }
}
```

## Background Job Orchestration

### Job Processing Architecture

```csharp
public class JobOrchestrator
{
    public async Task ScheduleDocumentProcessingAsync(Guid documentId)
    {
        // Immediate validation
        var validationJob = BackgroundJob.Enqueue<DocumentValidationJob>(
            job => job.ExecuteAsync(documentId));
        
        // Dependent extraction job
        var extractionJob = BackgroundJob.ContinueJobWith<DocumentExtractionJob>(
            validationJob,
            job => job.ExecuteAsync(documentId));
        
        // Dependent processing job
        var processingJob = BackgroundJob.ContinueJobWith<DocumentProcessingJob>(
            extractionJob,
            job => job.ExecuteAsync(documentId));
        
        // Final notification
        BackgroundJob.ContinueJobWith<NotificationJob>(
            processingJob,
            job => job.SendProcessingCompleteNotificationAsync(documentId));
    }
}
```

### Retry and Error Handling

```csharp
public class ResilientJobProcessor
{
    [AutomaticRetry(Attempts = 3, DelaysInSeconds = new[] { 60, 300, 600 })]
    public async Task ProcessWithRetryAsync(Guid documentId)
    {
        try
        {
            await _processor.ProcessDocumentAsync(documentId);
        }
        catch (TemporaryException ex)
        {
            // Log and allow retry
            _logger.LogWarning(ex, "Temporary processing failure for document {DocumentId}", documentId);
            throw;
        }
        catch (PermanentException ex)
        {
            // Log and move to error queue
            _logger.LogError(ex, "Permanent processing failure for document {DocumentId}", documentId);
            await _errorHandler.HandlePermanentFailureAsync(documentId, ex);
            return; // Don't retry
        }
    }
}
```

## Email Processing Business Logic

### Email Workflow Engine

```csharp
public class EmailProcessingEngine
{
    public async Task ProcessIncomingEmailAsync(EmailMessage email)
    {
        // 1. Email classification
        var classification = await ClassifyEmailAsync(email);
        
        // 2. Attachment processing
        var attachments = await ProcessAttachmentsAsync(email.Attachments);
        
        // 3. Document creation
        var documents = await CreateDocumentsFromAttachmentsAsync(attachments);
        
        // 4. Automatic processing initiation
        foreach (var document in documents)
        {
            await _jobOrchestrator.ScheduleDocumentProcessingAsync(document.Id);
        }
        
        // 5. Email archiving
        await ArchiveProcessedEmailAsync(email);
    }
}
```

## Business Logic Testing Patterns

### Unit Testing Business Logic

```csharp
[TestClass]
public class TransactionProcessingTests
{
    [TestMethod]
    public async Task ProcessInvoice_ValidData_ShouldSucceed()
    {
        // Arrange
        var mockRepository = new Mock<ITransactionRepository>();
        var mockValidator = new Mock<IBusinessValidator>();
        var processor = new TransactionProcessor(mockRepository.Object, mockValidator.Object);
        
        var invoice = CreateValidInvoice();
        mockValidator.Setup(v => v.ValidateAsync(It.IsAny<Transaction>()))
                    .ReturnsAsync(ValidationResult.Success());
        
        // Act
        var result = await processor.ProcessTransactionAsync(invoice);
        
        // Assert
        Assert.IsTrue(result.IsSuccess);
        mockRepository.Verify(r => r.SaveAsync(It.IsAny<Transaction>()), Times.Once);
    }
}
```

### Integration Testing

```csharp
[TestClass]
public class DocumentProcessingIntegrationTests
{
    [TestMethod]
    public async Task EndToEndProcessing_InvoicePDF_ShouldComplete()
    {
        // Arrange
        var testPdf = LoadTestInvoicePDF();
        var documentId = await UploadDocumentAsync(testPdf);
        
        // Act
        await _orchestrator.ScheduleDocumentProcessingAsync(documentId);
        
        // Wait for processing completion
        var timeout = TimeSpan.FromMinutes(5);
        var completed = await WaitForProcessingCompletionAsync(documentId, timeout);
        
        // Assert
        Assert.IsTrue(completed);
        var transaction = await _repository.GetTransactionByDocumentAsync(documentId);
        Assert.IsNotNull(transaction);
        Assert.AreEqual(TransactionState.Completed, transaction.State);
    }
}
```

## Performance Considerations

### Asynchronous Processing

```csharp
public class PerformantProcessor
{
    public async Task ProcessBatchAsync(IEnumerable<Document> documents)
    {
        var semaphore = new SemaphoreSlim(Environment.ProcessorCount);
        var tasks = documents.Select(async document =>
        {
            await semaphore.WaitAsync();
            try
            {
                return await ProcessDocumentAsync(document);
            }
            finally
            {
                semaphore.Release();
            }
        });
        
        var results = await Task.WhenAll(tasks);
        await ProcessBatchResultsAsync(results);
    }
}
```

### Caching Strategy

```csharp
public class CachedBusinessRuleService
{
    private readonly IMemoryCache _cache;
    private readonly TimeSpan _cacheExpiry = TimeSpan.FromHours(1);
    
    public async Task<IEnumerable<BusinessRule>> GetRulesAsync(Guid customerId)
    {
        var cacheKey = $"business_rules_{customerId}";
        
        if (_cache.TryGetValue(cacheKey, out IEnumerable<BusinessRule> cachedRules))
        {
            return cachedRules;
        }
        
        var rules = await _repository.GetBusinessRulesAsync(customerId);
        _cache.Set(cacheKey, rules, _cacheExpiry);
        
        return rules;
    }
}
```

## Common Development Patterns

### Adding a New Business Process

1. **Define State Machine States**:
```csharp
public enum YourProcessState
{
    Created,
    Processing,
    Validated,
    Approved,
    Completed,
    Failed
}
```

2. **Implement Business Logic**:
```csharp
public class YourProcessLogic
{
    public async Task<ProcessResult> ExecuteAsync(YourProcessRequest request)
    {
        // Validation
        var validation = await ValidateRequestAsync(request);
        if (!validation.IsValid)
        {
            return ProcessResult.Failed(validation.Errors);
        }
        
        // Business logic execution
        var result = await PerformBusinessLogicAsync(request);
        
        // State transition
        await TransitionStateAsync(request.Id, YourProcessState.Completed);
        
        return ProcessResult.Success(result);
    }
}
```

3. **Add Workflow Integration**:
```csharp
public class YourProcessWorkflow : IWorkflowStep
{
    public async Task<WorkflowResult> ExecuteAsync(WorkflowContext context)
    {
        var logic = new YourProcessLogic();
        var result = await logic.ExecuteAsync(context.Request);
        
        if (result.IsSuccess)
        {
            context.MoveToNextStep();
        }
        else
        {
            context.HandleError(result.Error);
        }
        
        return WorkflowResult.FromProcessResult(result);
    }
}
```

## Related Documents

- **[DatabaseArchitecture.md](./DatabaseArchitecture.md)** - Understanding the data layer supporting business logic
- **[APIArchitecture.md](./APIArchitecture.md)** - How APIs expose business functionality
- **[PDFProcessingEngine.md](./PDFProcessingEngine.md)** - Detailed PDF processing capabilities
- **[WorkflowEngine.md](./WorkflowEngine.md)** - Workflow orchestration and approval processes
- **[BackgroundJobProcessing.md](./BackgroundJobProcessing.md)** - Asynchronous business process execution

---

*This business logic architecture documentation provides the foundation for understanding how eHub implements complex document processing workflows, business rules, and transaction management. The state machine-driven approach, comprehensive validation engine, and sophisticated matching capabilities make this a robust platform for enterprise document processing operations.*