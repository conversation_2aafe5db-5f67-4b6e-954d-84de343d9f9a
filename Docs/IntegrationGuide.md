# eHub Integration Guide

## Purpose & Scope

This document provides comprehensive guidance for integrating external systems with the eHub electronic trading platform. It covers integration patterns, protocols, configuration examples, and best practices for connecting ERP systems, trading partners, financial institutions, and other business systems to eHub's document processing and workflow capabilities.

## Prerequisites

- **System Integration Experience**: Understanding of enterprise system integration patterns
- **Protocol Knowledge**: Familiarity with AS2, FTP/SFTP, HTTP/REST, and email protocols
- **Security Concepts**: Knowledge of certificates, encryption, and secure data transmission
- **Business Process Understanding**: Awareness of B2B document exchange workflows
- **Technical Access**: Administrative access to both eHub and target systems

## Core Concepts

### Integration Architecture Overview

eHub supports multiple integration patterns to accommodate diverse business requirements and technical constraints:

```mermaid
graph TB
    subgraph "External Systems"
        ERP[ERP Systems<br/>SAP, Sage, Oracle]
        TP[Trading Partners<br/>Suppliers, Customers]
        BANK[Financial Systems<br/>Banks, Payment Providers]
        GOV[Government Systems<br/>HMRC, Regulatory Bodies]
        LEGACY[Legacy Systems<br/>Mainframes, Custom Apps]
    end
    
    subgraph "Integration Protocols"
        API[REST API<br/>Real-time Integration]
        AS2[AS2 Protocol<br/>Secure B2B Exchange]
        FTP[FTP/SFTP<br/>File-based Transfer]
        EMAIL[Email Integration<br/>Document Attachments]
        WEBHOOK[Webhooks<br/>Event Notifications]
    end
    
    subgraph "eHub Platform"
        GATEWAY[Integration Gateway]
        PROCESSOR[Document Processor]
        WORKFLOW[Workflow Engine]
        STORAGE[Document Storage]
        AUDIT[Audit & Compliance]
    end
    
    ERP --> API
    TP --> AS2
    BANK --> FTP
    GOV --> AS2
    LEGACY --> EMAIL
    
    API --> GATEWAY
    AS2 --> GATEWAY
    FTP --> GATEWAY
    EMAIL --> GATEWAY
    WEBHOOK --> GATEWAY
    
    GATEWAY --> PROCESSOR
    PROCESSOR --> WORKFLOW
    WORKFLOW --> STORAGE
    STORAGE --> AUDIT
```

### Integration Patterns

#### 1. Real-time API Integration
- **Use Case**: ERP systems, modern applications
- **Protocol**: REST API with JWT authentication
- **Data Flow**: Synchronous request/response
- **Benefits**: Immediate processing, real-time status updates

#### 2. Secure B2B Exchange (AS2)
- **Use Case**: Trading partners, government systems
- **Protocol**: AS2 with digital certificates
- **Data Flow**: Asynchronous with MDN receipts
- **Benefits**: Non-repudiation, encryption, compliance

#### 3. File-based Transfer (FTP/SFTP)
- **Use Case**: Legacy systems, batch processing
- **Protocol**: FTP/SFTP with polling
- **Data Flow**: Scheduled file transfers
- **Benefits**: Simple implementation, reliable delivery

#### 4. Email Integration
- **Use Case**: Small suppliers, ad-hoc documents
- **Protocol**: SMTP/IMAP with attachment processing
- **Data Flow**: Email monitoring and extraction
- **Benefits**: Universal accessibility, low barrier to entry

## REST API Integration

### Authentication Setup

#### JWT Token Configuration
```json
{
  "authentication": {
    "type": "JWT",
    "issuer": "eHub-Auth",
    "audience": "eHub-API",
    "tokenEndpoint": "https://auth.ehub.com/token",
    "clientId": "your-client-id",
    "clientSecret": "your-client-secret"
  }
}
```

#### Token Request Example
```http
POST https://auth.ehub.com/token
Content-Type: application/x-www-form-urlencoded

grant_type=client_credentials
&client_id=your-client-id
&client_secret=your-client-secret
&scope=ehub-api
```

#### Token Response
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "expires_in": 3600,
  "scope": "ehub-api"
}
```

### Document Upload Integration

#### Single Document Upload
```http
POST https://api.ehub.com/api/Document/AddAndProcess
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "fileName": "invoice_12345.pdf",
  "fileContent": "<base64-encoded-pdf>",
  "customerId": "123e4567-e89b-12d3-a456-426614174000",
  "documentType": "Invoice",
  "metadata": {
    "source": "ERP_System",
    "priority": "Normal",
    "supplierCode": "SUP001",
    "purchaseOrderNumber": "PO-2024-001"
  }
}
```

#### Bulk Document Upload
```http
POST https://api.ehub.com/api/Document/BulkUpload
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "customerId": "123e4567-e89b-12d3-a456-426614174000",
  "batchId": "batch-2024-001",
  "documents": [
    {
      "fileName": "invoice_001.pdf",
      "fileContent": "<base64-encoded-content>",
      "documentType": "Invoice",
      "metadata": {
        "supplierCode": "SUP001",
        "invoiceNumber": "INV-001"
      }
    },
    {
      "fileName": "receipt_002.pdf",
      "fileContent": "<base64-encoded-content>",
      "documentType": "Receipt",
      "metadata": {
        "supplierCode": "SUP002",
        "receiptNumber": "REC-002"
      }
    }
  ]
}
```

### Transaction Status Monitoring

#### Polling for Status Updates
```http
POST https://api.ehub.com/api/members/{customerId}/transactions/GetTransactionList
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "selectedPage": 1,
  "itemsPerPage": 100,
  "filteredItems": [
    {
      "field": "status",
      "values": ["Processing", "Processed", "Failed"]
    },
    {
      "field": "createdDate",
      "values": ["2024-01-15T00:00:00Z", "2024-01-15T23:59:59Z"],
      "operator": "between"
    }
  ],
  "sortBy": "createdDate",
  "sortAsc": false
}
```

#### Webhook Configuration for Real-time Updates
```http
POST https://api.ehub.com/api/Webhooks/Configure
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "customerId": "123e4567-e89b-12d3-a456-426614174000",
  "webhookUrl": "https://your-erp-system.com/webhooks/ehub",
  "events": [
    "document.processed",
    "transaction.approved",
    "transaction.rejected",
    "workflow.completed"
  ],
  "secretKey": "your-webhook-secret-key",
  "isActive": true,
  "retryPolicy": {
    "maxRetries": 3,
    "retryDelay": "00:05:00"
  }
}
```

## AS2 Protocol Integration

### AS2 Configuration

#### Trading Partner Setup
```json
{
  "tradingPartner": {
    "partnerId": "PARTNER001",
    "partnerName": "ABC Trading Company",
    "as2Identifier": "ABC_TRADING_AS2",
    "url": "https://as2.partner.com/receive",
    "certificates": {
      "encryption": {
        "certificateId": "cert-encrypt-001",
        "algorithm": "AES256"
      },
      "signing": {
        "certificateId": "cert-sign-001",
        "algorithm": "SHA256withRSA"
      }
    },
    "mdnSettings": {
      "requestMdn": true,
      "signedMdn": true,
      "asyncMdn": false,
      "mdnUrl": null
    },
    "compressionEnabled": true,
    "retryPolicy": {
      "maxRetries": 3,
      "retryInterval": "00:15:00"
    }
  }
}
```

#### Certificate Management
```bash
# Generate private key
openssl genrsa -out ehub-private.key 2048

# Generate certificate signing request
openssl req -new -key ehub-private.key -out ehub.csr \
  -subj "/C=GB/ST=London/L=London/O=Your Company/CN=ehub.yourcompany.com"

# Generate self-signed certificate (for testing)
openssl x509 -req -days 365 -in ehub.csr -signkey ehub-private.key -out ehub.crt

# Convert to PKCS#12 format
openssl pkcs12 -export -out ehub.p12 -inkey ehub-private.key -in ehub.crt
```

#### AS2 Message Flow
```mermaid
sequenceDiagram
    participant ERP as ERP System
    participant eHub as eHub Platform
    participant AS2 as AS2 Gateway
    participant Partner as Trading Partner
    
    ERP->>eHub: Submit Document
    eHub->>eHub: Process Document
    eHub->>AS2: Prepare AS2 Message
    AS2->>AS2: Encrypt & Sign
    AS2->>Partner: Send AS2 Message
    Partner->>AS2: MDN Receipt
    AS2->>eHub: Delivery Confirmation
    eHub->>ERP: Status Update
```

### AS2 Message Processing

#### Outbound Message Configuration
```csharp
public class AS2MessageConfig
{
    public string MessageId { get; set; }
    public string From { get; set; }
    public string To { get; set; }
    public string Subject { get; set; }
    public byte[] Content { get; set; }
    public string ContentType { get; set; }
    public bool RequestMdn { get; set; }
    public bool SignMessage { get; set; }
    public bool EncryptMessage { get; set; }
    public string SigningCertificate { get; set; }
    public string EncryptionCertificate { get; set; }
}
```

#### Error Handling and Retry Logic
```csharp
public class AS2RetryPolicy
{
    public int MaxRetries { get; set; } = 3;
    public TimeSpan InitialDelay { get; set; } = TimeSpan.FromMinutes(5);
    public TimeSpan MaxDelay { get; set; } = TimeSpan.FromHours(1);
    public double BackoffMultiplier { get; set; } = 2.0;
    
    public async Task<bool> ExecuteWithRetryAsync(Func<Task<bool>> operation)
    {
        var delay = InitialDelay;
        
        for (int attempt = 0; attempt <= MaxRetries; attempt++)
        {
            try
            {
                var result = await operation();
                if (result) return true;
            }
            catch (Exception ex)
            {
                if (attempt == MaxRetries) throw;
                
                await Task.Delay(delay);
                delay = TimeSpan.FromMilliseconds(
                    Math.Min(delay.TotalMilliseconds * BackoffMultiplier, MaxDelay.TotalMilliseconds));
            }
        }
        
        return false;
    }
}
```

## FTP/SFTP Integration

### FTP Configuration

#### Connection Settings
```json
{
  "ftpConfig": {
    "server": "ftp.tradingpartner.com",
    "port": 21,
    "username": "ehub_user",
    "password": "secure_password",
    "usePassive": true,
    "useTLS": false,
    "timeout": 30000,
    "directories": {
      "inbound": "/incoming/ehub/",
      "outbound": "/outgoing/ehub/",
      "processed": "/processed/ehub/",
      "error": "/error/ehub/"
    },
    "filePatterns": {
      "include": ["*.xml", "*.pdf", "*.csv"],
      "exclude": ["*.tmp", "*.processing"]
    },
    "polling": {
      "interval": "00:15:00",
      "enabled": true,
      "deleteAfterProcessing": false,
      "moveToProcessed": true
    }
  }
}
```

#### SFTP Configuration
```json
{
  "sftpConfig": {
    "server": "sftp.tradingpartner.com",
    "port": 22,
    "username": "ehub_user",
    "authentication": {
      "type": "PublicKey",
      "privateKeyPath": "/certificates/ehub-sftp-key.pem",
      "passphrase": "key_passphrase"
    },
    "hostKeyValidation": true,
    "knownHostsFile": "/certificates/known_hosts",
    "directories": {
      "inbound": "/home/<USER>/incoming/",
      "outbound": "/home/<USER>/outgoing/"
    },
    "fileOperations": {
      "preserveTimestamp": true,
      "setPermissions": "644",
      "createDirectories": true
    }
  }
}
```

### File Processing Workflow

```mermaid
graph TB
    subgraph "FTP/SFTP Polling Process"
        POLL[Scheduled Polling] --> SCAN[Scan Directory]
        SCAN --> FILTER[Apply File Filters]
        FILTER --> DOWNLOAD[Download Files]
        DOWNLOAD --> VALIDATE[Validate Files]
        VALIDATE --> PROCESS[Process Documents]
        PROCESS --> MOVE[Move to Processed]
        MOVE --> NOTIFY[Send Notifications]
    end
    
    subgraph "Error Handling"
        VALIDATE --> ERROR{Validation Failed?}
        ERROR -->|Yes| QUARANTINE[Move to Error Directory]
        ERROR -->|No| PROCESS
        QUARANTINE --> ALERT[Send Error Alert]
    end
    
    subgraph "File Types"
        XML[XML Documents<br/>EDI, UBL]
        PDF[PDF Documents<br/>Invoices, Receipts]
        CSV[CSV Files<br/>Transaction Data]
        ZIP[ZIP Archives<br/>Batch Documents]
    end
    
    DOWNLOAD --> XML
    DOWNLOAD --> PDF
    DOWNLOAD --> CSV
    DOWNLOAD --> ZIP
```

### Automated File Processing

#### File Naming Conventions
```
Pattern: {DocumentType}_{PartnerCode}_{Date}_{Sequence}.{Extension}
Examples:
- INVOICE_ABC001_20240115_001.pdf
- RECEIPT_XYZ002_20240115_002.xml
- STATEMENT_DEF003_20240115_001.csv
```

#### Processing Rules Configuration
```json
{
  "processingRules": [
    {
      "filePattern": "INVOICE_*.pdf",
      "documentType": "Invoice",
      "workflow": "InvoiceApprovalWorkflow",
      "priority": "Normal",
      "autoProcess": true
    },
    {
      "filePattern": "URGENT_*.pdf",
      "documentType": "Invoice",
      "workflow": "UrgentInvoiceWorkflow",
      "priority": "High",
      "autoProcess": true,
      "notifications": ["<EMAIL>"]
    },
    {
      "filePattern": "STATEMENT_*.csv",
      "documentType": "Statement",
      "workflow": "StatementReconciliation",
      "priority": "Low",
      "autoProcess": false
    }
  ]
}
```

## Email Integration

### Email Configuration

#### IMAP Settings for Document Retrieval
```json
{
  "emailConfig": {
    "imap": {
      "server": "imap.company.com",
      "port": 993,
      "username": "<EMAIL>",
      "password": "email_password",
      "useSsl": true,
      "folders": {
        "inbox": "INBOX",
        "processed": "INBOX/Processed",
        "error": "INBOX/Error"
      }
    },
    "processing": {
      "pollInterval": "00:05:00",
      "maxAttachmentSize": 10485760,
      "allowedExtensions": [".pdf", ".xml", ".csv", ".zip"],
      "subjectFilters": [
        "Invoice",
        "Receipt",
        "Statement",
        "Purchase Order"
      ]
    },
    "senderWhitelist": [
      "@trustedsupplier.com",
      "<EMAIL>",
      "<EMAIL>"
    ]
  }
}
```

#### SMTP Settings for Notifications
```json
{
  "smtpConfig": {
    "server": "smtp.company.com",
    "port": 587,
    "username": "<EMAIL>",
    "password": "email_password",
    "useTls": true,
    "fromAddress": "<EMAIL>",
    "fromName": "eHub Document Processing",
    "templates": {
      "documentProcessed": "template-001",
      "approvalRequired": "template-002",
      "processingError": "template-003"
    }
  }
}
```

### Email Processing Workflow

```mermaid
graph TB
    subgraph "Email Processing Pipeline"
        MONITOR[Email Monitoring] --> RECEIVE[Receive Email]
        RECEIVE --> VALIDATE[Validate Sender]
        VALIDATE --> EXTRACT[Extract Attachments]
        EXTRACT --> CLASSIFY[Classify Documents]
        CLASSIFY --> PROCESS[Process Documents]
        PROCESS --> RESPOND[Send Confirmation]
        RESPOND --> ARCHIVE[Archive Email]
    end
    
    subgraph "Validation Rules"
        SENDER[Sender Whitelist]
        SIZE[File Size Limits]
        TYPE[File Type Validation]
        VIRUS[Virus Scanning]
    end
    
    subgraph "Document Classification"
        AI[AI Classification]
        RULES[Rule-based Classification]
        MANUAL[Manual Classification]
    end
    
    VALIDATE --> SENDER
    EXTRACT --> SIZE
    EXTRACT --> TYPE
    EXTRACT --> VIRUS
    
    CLASSIFY --> AI
    CLASSIFY --> RULES
    CLASSIFY --> MANUAL
```

## ERP System Integration Examples

### SAP Integration

#### SAP Business One Integration
```csharp
public class SAPBusinessOneIntegration
{
    private readonly HttpClient httpClient;
    private readonly SAPConfig config;
    
    public async Task<bool> SendInvoiceToSAPAsync(TransactionModel transaction)
    {
        var sapInvoice = new
        {
            CardCode = transaction.SupplierCode,
            DocDate = transaction.InvoiceDate.ToString("yyyy-MM-dd"),
            DocDueDate = transaction.DueDate.ToString("yyyy-MM-dd"),
            DocumentLines = transaction.LineItems.Select(line => new
            {
                ItemCode = line.ItemCode,
                Quantity = line.Quantity,
                Price = line.UnitPrice,
                TaxCode = line.VATCode
            }).ToArray()
        };
        
        var response = await httpClient.PostAsJsonAsync(
            $"{config.BaseUrl}/Invoices", 
            sapInvoice);
            
        return response.IsSuccessStatusCode;
    }
}
```

#### SAP S/4HANA Integration
```csharp
public class SAPS4HANAIntegration
{
    public async Task<string> CreateSupplierInvoiceAsync(InvoiceData invoice)
    {
        var oDataPayload = new
        {
            CompanyCode = "1000",
            DocumentType = "RE",
            PostingDate = invoice.PostingDate,
            DocumentDate = invoice.DocumentDate,
            InvoiceReference = invoice.InvoiceNumber,
            BusinessPartner = invoice.SupplierCode,
            DocumentCurrency = invoice.Currency,
            InvoiceGrossAmount = invoice.TotalAmount,
            TaxAmount = invoice.VATAmount,
            PaymentTerms = invoice.PaymentTerms,
            to_SuplrInvcItemPurOrdRef = invoice.LineItems.Select(item => new
            {
                SuplrInvcItem = item.LineNumber.ToString(),
                Plant = item.Plant,
                PurchaseOrder = item.PurchaseOrderNumber,
                PurchaseOrderItem = item.PurchaseOrderLine,
                TaxCode = item.TaxCode,
                DocumentCurrency = invoice.Currency,
                SupplierInvoiceItemAmount = item.LineAmount
            }).ToArray()
        };
        
        var response = await httpClient.PostAsJsonAsync(
            "/sap/opu/odata/sap/API_SUPPLIERINVOICE_PROCESS_SRV/A_SupplierInvoice",
            oDataPayload);
            
        if (response.IsSuccessStatusCode)
        {
            var result = await response.Content.ReadAsStringAsync();
            // Parse response to get SAP document number
            return ExtractDocumentNumber(result);
        }
        
        throw new Exception($"SAP integration failed: {response.StatusCode}");
    }
}
```

### Sage Integration

#### Sage 200 Integration
```csharp
public class Sage200Integration
{
    private readonly SageApiClient sageClient;
    
    public async Task<bool> PostPurchaseInvoiceAsync(TransactionModel transaction)
    {
        var invoice = new
        {
            supplier_reference = transaction.SupplierCode,
            document_date = transaction.InvoiceDate,
            due_date = transaction.DueDate,
            document_no = transaction.InvoiceNumber,
            gross_value = transaction.TotalAmount,
            tax_value = transaction.VATAmount,
            net_value = transaction.NetAmount,
            currency = new { code = transaction.Currency },
            invoice_lines = transaction.LineItems.Select(line => new
            {
                line_number = line.LineNumber,
                product_code = line.ProductCode,
                description = line.Description,
                quantity = line.Quantity,
                unit_price = line.UnitPrice,
                line_total = line.LineTotal,
                tax_code = line.VATCode
            }).ToArray()
        };
        
        var response = await sageClient.PostAsync("/purchase_invoices", invoice);
        return response.IsSuccessful;
    }
}
```

## Best Practices

### Security Best Practices

#### 1. Authentication and Authorisation
- **Use Strong Authentication**: Implement multi-factor authentication where possible
- **Rotate Credentials Regularly**: Change passwords and API keys on a scheduled basis
- **Principle of Least Privilege**: Grant minimum necessary permissions
- **Monitor Access**: Log and monitor all integration access attempts

#### 2. Data Protection
- **Encrypt in Transit**: Use TLS 1.2 or higher for all communications
- **Encrypt at Rest**: Ensure sensitive data is encrypted in storage
- **Data Masking**: Mask sensitive data in logs and non-production environments
- **Secure Key Management**: Use dedicated key management systems

#### 3. Certificate Management
- **Regular Renewal**: Monitor certificate expiry dates and renew proactively
- **Secure Storage**: Store certificates in secure, access-controlled locations
- **Backup Certificates**: Maintain secure backups of all certificates
- **Certificate Validation**: Always validate certificate chains and revocation status

### Performance Optimisation

#### 1. Batch Processing
- **Group Operations**: Batch multiple documents in single API calls
- **Optimal Batch Size**: Use 10-50 documents per batch for best performance
- **Parallel Processing**: Process multiple batches concurrently where possible
- **Error Isolation**: Ensure one failed document doesn't affect the entire batch

#### 2. Connection Management
- **Connection Pooling**: Reuse HTTP connections for multiple requests
- **Timeout Configuration**: Set appropriate timeouts for different operations
- **Retry Logic**: Implement exponential backoff for transient failures
- **Circuit Breaker**: Prevent cascade failures with circuit breaker patterns

#### 3. Monitoring and Alerting
- **Health Checks**: Implement regular health checks for all integrations
- **Performance Metrics**: Monitor response times, throughput, and error rates
- **Proactive Alerting**: Set up alerts for performance degradation
- **Capacity Planning**: Monitor resource usage and plan for growth

### Error Handling and Recovery

#### 1. Retry Strategies
```csharp
public class RetryPolicy
{
    public static async Task<T> ExecuteWithRetryAsync<T>(
        Func<Task<T>> operation,
        int maxRetries = 3,
        TimeSpan delay = default)
    {
        if (delay == default) delay = TimeSpan.FromSeconds(1);
        
        for (int attempt = 0; attempt <= maxRetries; attempt++)
        {
            try
            {
                return await operation();
            }
            catch (Exception ex) when (attempt < maxRetries && IsTransientError(ex))
            {
                await Task.Delay(delay * (int)Math.Pow(2, attempt));
            }
        }
        
        // Final attempt without catching exceptions
        return await operation();
    }
    
    private static bool IsTransientError(Exception ex)
    {
        return ex is HttpRequestException ||
               ex is TaskCanceledException ||
               ex is SocketException;
    }
}
```

#### 2. Dead Letter Queue Processing
```csharp
public class DeadLetterQueueProcessor
{
    public async Task ProcessFailedMessagesAsync()
    {
        var failedMessages = await GetFailedMessagesAsync();
        
        foreach (var message in failedMessages)
        {
            try
            {
                // Attempt to reprocess
                await ProcessMessageAsync(message);
                await MarkAsProcessedAsync(message.Id);
            }
            catch (Exception ex)
            {
                // Log error and potentially move to manual review queue
                await LogErrorAsync(message.Id, ex);
                
                if (message.RetryCount >= MaxRetries)
                {
                    await MoveToManualReviewAsync(message);
                }
                else
                {
                    await ScheduleRetryAsync(message);
                }
            }
        }
    }
}
```

## Related Documents

- **[APIReference.md](./APIReference.md)** - Complete REST API documentation
- **[AuthenticationGuide.md](./AuthenticationGuide.md)** - Authentication and authorisation details
- **[ErrorCodesAndHandling.md](./ErrorCodesAndHandling.md)** - Error handling and troubleshooting
- **[IntegrationSystems.md](./IntegrationSystems.md)** - Internal integration architecture
- **[SecurityArchitecture.md](./SecurityArchitecture.md)** - Security implementation details
- **[SystemOverview.md](./SystemOverview.md)** - High-level system architecture

---

*This integration guide provides comprehensive guidance for connecting external systems to the eHub platform. For additional support or specific integration requirements, please contact the eHub integration team.*
