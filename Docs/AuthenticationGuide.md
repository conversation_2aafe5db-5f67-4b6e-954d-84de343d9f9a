# eHub Authentication Guide

## Purpose & Scope

This document provides comprehensive guidance for implementing authentication and authorisation with the eHub electronic trading platform. It covers OIDC flows, JWT token management, session handling, multi-tenant security, and integration patterns for external authentication providers. This guide is essential for developers implementing secure integrations with eHub APIs.

## Prerequisites

- **OAuth 2.0/OIDC Knowledge**: Understanding of OAuth 2.0 and OpenID Connect protocols
- **JWT Token Concepts**: Familiarity with JSON Web Tokens and their structure
- **HTTP Security**: Knowledge of secure HTTP communication and headers
- **Multi-tenancy**: Understanding of tenant-based data isolation
- **Certificate Management**: Basic knowledge of digital certificates and PKI

## Core Concepts

### Authentication Architecture Overview

eHub implements a comprehensive authentication system supporting multiple authentication providers and protocols:

```mermaid
graph TB
    subgraph "External Authentication Providers"
        OIDC[OIDC Providers<br/>Azure AD, Auth0, etc.]
        BASIC[Basic Authentication<br/>Username/Password]
        NTLM[NTLM Authentication<br/>Windows Integrated]
        CERT[Certificate Authentication<br/>Client Certificates]
    end
    
    subgraph "eHub Authentication Layer"
        AUTH_GATEWAY[Authentication Gateway]
        TOKEN_SERVICE[Token Service]
        SESSION_MGR[Session Manager]
        VALIDATOR[Token Validator]
    end
    
    subgraph "Authorization Layer"
        RBAC[Role-Based Access Control]
        TENANT[Multi-Tenant Isolation]
        RESOURCE[Resource Permissions]
        AUDIT[Audit Logging]
    end
    
    subgraph "API Layer"
        REST_API[REST API Endpoints]
        MIDDLEWARE[Auth Middleware]
        CONTROLLERS[API Controllers]
    end
    
    OIDC --> AUTH_GATEWAY
    BASIC --> AUTH_GATEWAY
    NTLM --> AUTH_GATEWAY
    CERT --> AUTH_GATEWAY
    
    AUTH_GATEWAY --> TOKEN_SERVICE
    TOKEN_SERVICE --> SESSION_MGR
    SESSION_MGR --> VALIDATOR
    
    VALIDATOR --> RBAC
    RBAC --> TENANT
    TENANT --> RESOURCE
    RESOURCE --> AUDIT
    
    AUDIT --> MIDDLEWARE
    MIDDLEWARE --> REST_API
    REST_API --> CONTROLLERS
```

### Authentication Flow Types

#### 1. OIDC/OAuth 2.0 Flow
- **Use Case**: Modern applications, SSO integration
- **Flow Type**: Authorization Code with PKCE
- **Token Types**: Access tokens, refresh tokens, ID tokens
- **Security**: High security with token rotation

#### 2. JWT Bearer Token Flow
- **Use Case**: API-to-API communication
- **Flow Type**: Client credentials or pre-issued tokens
- **Token Types**: JWT access tokens
- **Security**: Stateless validation with signature verification

#### 3. Basic Authentication Flow
- **Use Case**: Legacy systems, simple integrations
- **Flow Type**: Username/password credentials
- **Token Types**: Session-based tokens
- **Security**: HTTPS required, credential validation

## OIDC Authentication Implementation

### OIDC Configuration

#### Provider Configuration
```json
{
  "oidcProviders": {
    "azureAD": {
      "providerType": "Office365",
      "authority": "https://login.microsoftonline.com/{tenant-id}",
      "clientId": "your-client-id",
      "clientSecret": "your-client-secret",
      "redirectUri": "https://your-app.com/auth/callback",
      "scopes": ["openid", "profile", "email", "offline_access"],
      "responseType": "code",
      "responseMode": "query"
    },
    "auth0": {
      "providerType": "Auth0",
      "authority": "https://your-domain.auth0.com",
      "clientId": "your-auth0-client-id",
      "clientSecret": "your-auth0-client-secret",
      "redirectUri": "https://your-app.com/auth/callback",
      "scopes": ["openid", "profile", "email"]
    }
  }
}
```

#### OIDC Authentication Flow
```mermaid
sequenceDiagram
    participant User as User
    participant App as Client Application
    participant eHub as eHub Auth Service
    participant Provider as OIDC Provider
    participant API as eHub API
    
    User->>App: Initiate Login
    App->>eHub: Request Authentication
    eHub->>Provider: Redirect to OIDC Provider
    Provider->>User: Present Login Form
    User->>Provider: Enter Credentials
    Provider->>eHub: Authorization Code
    eHub->>Provider: Exchange Code for Tokens
    Provider->>eHub: Access Token + ID Token
    eHub->>eHub: Validate Tokens
    eHub->>App: eHub JWT Token
    App->>API: API Request with JWT
    API->>API: Validate JWT
    API->>App: API Response
```

### OIDC Session Management

#### Session Creation
```csharp
public class OidcSessionManager
{
    public async Task<OidcSession> CreateSessionAsync(
        Guid customerId, 
        string authorizationCode, 
        EnumOidcProviderType providerType)
    {
        var tokenResponse = await ExchangeCodeForTokensAsync(authorizationCode, providerType);
        
        var session = new OidcSession
        {
            Id = Guid.NewGuid(),
            CustomerId = customerId,
            OidcProviderType = providerType,
            AccessToken = tokenResponse.AccessToken,
            RefreshToken = tokenResponse.RefreshToken,
            IdToken = tokenResponse.IdToken,
            TokenType = tokenResponse.TokenType,
            ExpiresIn = tokenResponse.ExpiresIn,
            LastLoggedIn = DateTime.UtcNow,
            RefreshTokenLifeTimeDays = 30
        };
        
        await SaveSessionAsync(session);
        return session;
    }
    
    private async Task<TokenResponse> ExchangeCodeForTokensAsync(
        string authorizationCode, 
        EnumOidcProviderType providerType)
    {
        var config = GetProviderConfiguration(providerType);
        
        var tokenRequest = new
        {
            grant_type = "authorization_code",
            client_id = config.ClientId,
            client_secret = config.ClientSecret,
            code = authorizationCode,
            redirect_uri = config.RedirectUri
        };
        
        var response = await httpClient.PostAsJsonAsync(config.TokenEndpoint, tokenRequest);
        response.EnsureSuccessStatusCode();
        
        return await response.Content.ReadFromJsonAsync<TokenResponse>();
    }
}
```

#### Token Refresh Implementation
```csharp
public async Task<DataOrErrorResult<string, OidcSessionError>> RefreshAccessTokenAsync(
    Guid customerId, 
    EnumOidcProviderType providerType)
{
    var session = await GetSessionAsync(customerId, providerType);
    
    if (session == null)
    {
        return DataOrErrorResult<string, OidcSessionError>.Failure(OidcSessionError.SessionNotFound);
    }
    
    if (IsRefreshTokenExpired(session))
    {
        return DataOrErrorResult<string, OidcSessionError>.Failure(OidcSessionError.RefreshTokenExpired);
    }
    
    try
    {
        var config = GetProviderConfiguration(providerType);
        
        var refreshRequest = new
        {
            grant_type = "refresh_token",
            client_id = config.ClientId,
            client_secret = config.ClientSecret,
            refresh_token = session.RefreshToken
        };
        
        var response = await httpClient.PostAsJsonAsync(config.TokenEndpoint, refreshRequest);
        
        if (!response.IsSuccessStatusCode)
        {
            return DataOrErrorResult<string, OidcSessionError>.Failure(OidcSessionError.RefreshFailed);
        }
        
        var tokenResponse = await response.Content.ReadFromJsonAsync<TokenResponse>();
        
        // Update session with new tokens
        session.AccessToken = tokenResponse.AccessToken;
        session.ExpiresIn = tokenResponse.ExpiresIn;
        session.LastRefreshed = DateTime.UtcNow;
        
        if (!string.IsNullOrEmpty(tokenResponse.RefreshToken))
        {
            session.RefreshToken = tokenResponse.RefreshToken;
        }
        
        await UpdateSessionAsync(session);
        
        return DataOrErrorResult<string, OidcSessionError>.Success(session.AccessToken);
    }
    catch (Exception ex)
    {
        Logger.Error("OIDC", $"Token refresh failed: {ex.Message}");
        return DataOrErrorResult<string, OidcSessionError>.Failure(OidcSessionError.RefreshFailed);
    }
}
```

## JWT Token Management

### JWT Token Structure

eHub uses JWT tokens with the following structure:

#### Header
```json
{
  "alg": "HS256",
  "typ": "JWT"
}
```

#### Payload (Claims)
```json
{
  "iss": "eHub-Auth",
  "aud": "eHub-API",
  "sub": "user-123",
  "iat": 1705225800,
  "exp": 1705312200,
  "UserId": "123e4567-e89b-12d3-a456-426614174000",
  "fullName": "John Smith",
  "EHubCustomerId": "987fcdeb-51a2-43d1-9f12-123456789abc",
  "memberName": "Acme Corporation",
  "roles": ["user", "approver"],
  "permissions": ["transaction.read", "transaction.update"],
  "tenantId": "tenant-456"
}
```

### JWT Token Validation

#### Server-Side Validation
```csharp
public class JwtTokenValidator
{
    private readonly IConfiguration configuration;
    private readonly TokenValidationParameters validationParameters;
    
    public JwtTokenValidator(IConfiguration configuration)
    {
        this.configuration = configuration;
        this.validationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = false,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = configuration["Jwt:Issuer"],
            IssuerSigningKey = new SymmetricSecurityKey(
                Convert.FromBase64String(configuration["Jwt:Base64Key"])),
            ClockSkew = TimeSpan.FromMinutes(5)
        };
    }
    
    public async Task<ClaimsPrincipal> ValidateTokenAsync(string token)
    {
        try
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            
            var principal = tokenHandler.ValidateToken(
                token, 
                validationParameters, 
                out SecurityToken validatedToken);
                
            return principal;
        }
        catch (SecurityTokenExpiredException)
        {
            throw new UnauthorizedAccessException("Token has expired");
        }
        catch (SecurityTokenInvalidSignatureException)
        {
            throw new UnauthorizedAccessException("Invalid token signature");
        }
        catch (Exception ex)
        {
            throw new UnauthorizedAccessException($"Token validation failed: {ex.Message}");
        }
    }
    
    public Actor ExtractActor(ClaimsPrincipal principal)
    {
        var userIdClaim = principal.FindFirst("UserId")?.Value;
        var fullNameClaim = principal.FindFirst("fullName")?.Value;
        var memberIdClaim = principal.FindFirst("EHubCustomerId")?.Value;
        var memberNameClaim = principal.FindFirst("memberName")?.Value;
        
        if (string.IsNullOrEmpty(userIdClaim) || string.IsNullOrEmpty(memberIdClaim))
        {
            throw new UnauthorizedAccessException("Invalid token claims");
        }
        
        return new Actor(
            Guid.Parse(userIdClaim),
            fullNameClaim ?? "Unknown",
            Guid.Parse(memberIdClaim),
            memberNameClaim ?? "Unknown"
        );
    }
}
```

#### Client-Side Token Management
```javascript
class TokenManager {
    constructor() {
        this.token = localStorage.getItem('ehub_token');
        this.refreshToken = localStorage.getItem('ehub_refresh_token');
        this.tokenExpiry = localStorage.getItem('ehub_token_expiry');
    }
    
    isTokenValid() {
        if (!this.token || !this.tokenExpiry) {
            return false;
        }
        
        const expiry = new Date(this.tokenExpiry);
        const now = new Date();
        const bufferTime = 5 * 60 * 1000; // 5 minutes buffer
        
        return expiry.getTime() > (now.getTime() + bufferTime);
    }
    
    async getValidToken() {
        if (this.isTokenValid()) {
            return this.token;
        }
        
        if (this.refreshToken) {
            try {
                await this.refreshAccessToken();
                return this.token;
            } catch (error) {
                console.error('Token refresh failed:', error);
                this.clearTokens();
                throw new Error('Authentication required');
            }
        }
        
        throw new Error('Authentication required');
    }
    
    async refreshAccessToken() {
        const response = await fetch('/auth/refresh', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                refreshToken: this.refreshToken
            })
        });
        
        if (!response.ok) {
            throw new Error('Token refresh failed');
        }
        
        const tokenData = await response.json();
        this.setTokens(tokenData);
    }
    
    setTokens(tokenData) {
        this.token = tokenData.accessToken;
        this.refreshToken = tokenData.refreshToken;
        this.tokenExpiry = new Date(Date.now() + (tokenData.expiresIn * 1000)).toISOString();
        
        localStorage.setItem('ehub_token', this.token);
        localStorage.setItem('ehub_refresh_token', this.refreshToken);
        localStorage.setItem('ehub_token_expiry', this.tokenExpiry);
    }
    
    clearTokens() {
        this.token = null;
        this.refreshToken = null;
        this.tokenExpiry = null;
        
        localStorage.removeItem('ehub_token');
        localStorage.removeItem('ehub_refresh_token');
        localStorage.removeItem('ehub_token_expiry');
    }
}
```

## Multi-Tenant Security

### Tenant Isolation

#### Request Context Validation
```csharp
public class TenantContextMiddleware
{
    private readonly RequestDelegate next;
    
    public TenantContextMiddleware(RequestDelegate next)
    {
        this.next = next;
    }
    
    public async Task InvokeAsync(HttpContext context)
    {
        var token = ExtractToken(context.Request);
        
        if (!string.IsNullOrEmpty(token))
        {
            var principal = await ValidateTokenAsync(token);
            var actor = ExtractActor(principal);
            
            // Set tenant context for the request
            context.Items["TenantId"] = actor.MemberId;
            context.Items["Actor"] = actor;
            
            // Validate tenant access for the requested resource
            if (!await ValidateTenantAccessAsync(context, actor))
            {
                context.Response.StatusCode = 403;
                await context.Response.WriteAsync("Access denied");
                return;
            }
        }
        
        await next(context);
    }
    
    private async Task<bool> ValidateTenantAccessAsync(HttpContext context, Actor actor)
    {
        // Extract customer ID from route or request body
        var requestedCustomerId = ExtractCustomerIdFromRequest(context);
        
        if (requestedCustomerId.HasValue)
        {
            // Ensure the actor's tenant matches the requested customer
            return actor.MemberId == requestedCustomerId.Value;
        }
        
        return true; // No specific customer context required
    }
}
```

### Role-Based Access Control

#### Permission Validation
```csharp
public class PermissionAttribute : Attribute, IAuthorizationFilter
{
    private readonly string[] requiredPermissions;
    
    public PermissionAttribute(params string[] permissions)
    {
        this.requiredPermissions = permissions;
    }
    
    public void OnAuthorization(AuthorizationFilterContext context)
    {
        var actor = context.HttpContext.Items["Actor"] as Actor;
        
        if (actor == null)
        {
            context.Result = new UnauthorizedResult();
            return;
        }
        
        var userPermissions = GetUserPermissions(actor.UserId, actor.MemberId);
        
        var hasRequiredPermissions = requiredPermissions.All(permission => 
            userPermissions.Contains(permission));
            
        if (!hasRequiredPermissions)
        {
            context.Result = new ForbidResult();
            return;
        }
    }
    
    private List<string> GetUserPermissions(Guid userId, Guid customerId)
    {
        // Retrieve user permissions from database
        using var context = new SecurityContext();
        
        return context.UserPermissions
            .Where(up => up.UserId == userId && up.CustomerId == customerId)
            .Select(up => up.Permission)
            .ToList();
    }
}

// Usage example
[HttpPost("UpdateTransaction")]
[Permission("transaction.update")]
public async Task<IActionResult> UpdateTransaction([FromBody] UpdateTransactionModel model)
{
    // Implementation
}
```

## Authentication Integration Examples

### ASP.NET Core Integration

#### Startup Configuration
```csharp
public void ConfigureServices(IServiceCollection services)
{
    // JWT Authentication
    services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
        .AddJwtBearer(options =>
        {
            options.TokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuer = true,
                ValidateAudience = false,
                ValidateLifetime = true,
                ValidateIssuerSigningKey = true,
                ValidIssuer = Configuration["Jwt:Issuer"],
                IssuerSigningKey = new SymmetricSecurityKey(
                    Convert.FromBase64String(Configuration["Jwt:Base64Key"])),
                ClockSkew = TimeSpan.FromMinutes(5)
            };
            
            options.Events = new JwtBearerEvents
            {
                OnTokenValidated = async context =>
                {
                    // Additional validation logic
                    var actor = ExtractActor(context.Principal);
                    context.HttpContext.Items["Actor"] = actor;
                },
                OnAuthenticationFailed = context =>
                {
                    // Log authentication failures
                    Logger.Warning("Authentication failed: {Error}", context.Exception.Message);
                    return Task.CompletedTask;
                }
            };
        });
    
    // Authorization policies
    services.AddAuthorization(options =>
    {
        options.AddPolicy("RequireUser", policy =>
            policy.RequireAuthenticatedUser());
            
        options.AddPolicy("RequireApprover", policy =>
            policy.RequireClaim("roles", "approver"));
            
        options.AddPolicy("RequireAdmin", policy =>
            policy.RequireClaim("roles", "admin"));
    });
}

public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
{
    app.UseAuthentication();
    app.UseMiddleware<TenantContextMiddleware>();
    app.UseAuthorization();
}
```

### JavaScript/TypeScript Integration

#### Authentication Service
```typescript
interface AuthConfig {
    authUrl: string;
    clientId: string;
    redirectUri: string;
    scopes: string[];
}

interface TokenResponse {
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
    tokenType: string;
}

class AuthenticationService {
    private config: AuthConfig;
    private tokenManager: TokenManager;
    
    constructor(config: AuthConfig) {
        this.config = config;
        this.tokenManager = new TokenManager();
    }
    
    async login(): Promise<void> {
        const state = this.generateState();
        const codeVerifier = this.generateCodeVerifier();
        const codeChallenge = await this.generateCodeChallenge(codeVerifier);
        
        // Store PKCE parameters
        sessionStorage.setItem('auth_state', state);
        sessionStorage.setItem('code_verifier', codeVerifier);
        
        const authUrl = new URL(this.config.authUrl);
        authUrl.searchParams.set('response_type', 'code');
        authUrl.searchParams.set('client_id', this.config.clientId);
        authUrl.searchParams.set('redirect_uri', this.config.redirectUri);
        authUrl.searchParams.set('scope', this.config.scopes.join(' '));
        authUrl.searchParams.set('state', state);
        authUrl.searchParams.set('code_challenge', codeChallenge);
        authUrl.searchParams.set('code_challenge_method', 'S256');
        
        window.location.href = authUrl.toString();
    }
    
    async handleCallback(code: string, state: string): Promise<void> {
        const storedState = sessionStorage.getItem('auth_state');
        const codeVerifier = sessionStorage.getItem('code_verifier');
        
        if (state !== storedState) {
            throw new Error('Invalid state parameter');
        }
        
        if (!codeVerifier) {
            throw new Error('Missing code verifier');
        }
        
        const tokenResponse = await this.exchangeCodeForTokens(code, codeVerifier);
        this.tokenManager.setTokens(tokenResponse);
        
        // Clean up session storage
        sessionStorage.removeItem('auth_state');
        sessionStorage.removeItem('code_verifier');
    }
    
    private async exchangeCodeForTokens(code: string, codeVerifier: string): Promise<TokenResponse> {
        const response = await fetch('/auth/token', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                grant_type: 'authorization_code',
                client_id: this.config.clientId,
                code: code,
                redirect_uri: this.config.redirectUri,
                code_verifier: codeVerifier
            })
        });
        
        if (!response.ok) {
            throw new Error('Token exchange failed');
        }
        
        return await response.json();
    }
    
    private generateState(): string {
        return btoa(String.fromCharCode(...crypto.getRandomValues(new Uint8Array(32))));
    }
    
    private generateCodeVerifier(): string {
        return btoa(String.fromCharCode(...crypto.getRandomValues(new Uint8Array(32))))
            .replace(/\+/g, '-')
            .replace(/\//g, '_')
            .replace(/=/g, '');
    }
    
    private async generateCodeChallenge(verifier: string): Promise<string> {
        const encoder = new TextEncoder();
        const data = encoder.encode(verifier);
        const digest = await crypto.subtle.digest('SHA-256', data);
        
        return btoa(String.fromCharCode(...new Uint8Array(digest)))
            .replace(/\+/g, '-')
            .replace(/\//g, '_')
            .replace(/=/g, '');
    }
}
```

## Security Best Practices

### Token Security

#### 1. Token Storage
- **Web Applications**: Use secure HTTP-only cookies for refresh tokens
- **Mobile Applications**: Use secure keychain/keystore for token storage
- **Desktop Applications**: Use OS credential managers
- **Never**: Store tokens in local storage for production applications

#### 2. Token Transmission
- **Always use HTTPS**: Never transmit tokens over unencrypted connections
- **Authorization Header**: Use `Authorization: Bearer <token>` header format
- **Custom Headers**: Support custom `EHubBearer` header for compatibility
- **Token Validation**: Always validate tokens on the server side

#### 3. Token Lifecycle Management
- **Short-lived Access Tokens**: Use access tokens with 1-hour expiry
- **Long-lived Refresh Tokens**: Use refresh tokens with 30-day expiry
- **Token Rotation**: Implement refresh token rotation for enhanced security
- **Revocation**: Provide token revocation endpoints for logout

### Session Security

#### 1. Session Management
```csharp
public class SecureSessionManager
{
    public async Task<bool> ValidateSessionAsync(Guid sessionId, Guid customerId)
    {
        var session = await GetSessionAsync(sessionId);
        
        if (session == null || session.CustomerId != customerId)
        {
            return false;
        }
        
        // Check session expiry
        if (session.ExpiresAt < DateTime.UtcNow)
        {
            await InvalidateSessionAsync(sessionId);
            return false;
        }
        
        // Update last activity
        session.LastActivity = DateTime.UtcNow;
        await UpdateSessionAsync(session);
        
        return true;
    }
    
    public async Task InvalidateSessionAsync(Guid sessionId)
    {
        var session = await GetSessionAsync(sessionId);
        if (session != null)
        {
            session.IsActive = false;
            session.InvalidatedAt = DateTime.UtcNow;
            await UpdateSessionAsync(session);
        }
    }
}
```

#### 2. Concurrent Session Handling
- **Session Limits**: Implement maximum concurrent session limits per user
- **Session Monitoring**: Monitor for suspicious session activity
- **Session Invalidation**: Provide mechanisms to invalidate all user sessions
- **Device Tracking**: Track and display active sessions to users

### Audit and Monitoring

#### Authentication Event Logging
```csharp
public class AuthenticationAuditor
{
    public async Task LogAuthenticationEventAsync(AuthenticationEvent authEvent)
    {
        var auditEntry = new AuditEntry
        {
            EventType = authEvent.EventType,
            UserId = authEvent.UserId,
            CustomerId = authEvent.CustomerId,
            IpAddress = authEvent.IpAddress,
            UserAgent = authEvent.UserAgent,
            Timestamp = DateTime.UtcNow,
            Success = authEvent.Success,
            FailureReason = authEvent.FailureReason,
            AdditionalData = JsonSerializer.Serialize(authEvent.AdditionalData)
        };
        
        await SaveAuditEntryAsync(auditEntry);
        
        // Alert on suspicious activity
        if (ShouldAlert(authEvent))
        {
            await SendSecurityAlertAsync(authEvent);
        }
    }
    
    private bool ShouldAlert(AuthenticationEvent authEvent)
    {
        // Multiple failed attempts
        if (!authEvent.Success && GetRecentFailedAttempts(authEvent.UserId) > 5)
        {
            return true;
        }
        
        // Login from new location
        if (authEvent.Success && IsNewLocation(authEvent.UserId, authEvent.IpAddress))
        {
            return true;
        }
        
        // Unusual time of access
        if (authEvent.Success && IsUnusualTime(authEvent.UserId, authEvent.Timestamp))
        {
            return true;
        }
        
        return false;
    }
}
```

## Related Documents

- **[APIReference.md](./APIReference.md)** - Complete REST API documentation with authentication examples
- **[SecurityArchitecture.md](./SecurityArchitecture.md)** - Comprehensive security architecture and implementation
- **[IntegrationGuide.md](./IntegrationGuide.md)** - External system integration patterns and security
- **[ErrorCodesAndHandling.md](./ErrorCodesAndHandling.md)** - Authentication error handling and troubleshooting
- **[SystemOverview.md](./SystemOverview.md)** - High-level system architecture and security overview

---

*This authentication guide provides comprehensive guidance for implementing secure authentication and authorisation with the eHub platform. Proper implementation of these patterns ensures robust security, compliance with industry standards, and seamless user experience.*
