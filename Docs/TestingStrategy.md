# eHub Testing Strategy

## Purpose & Scope

This document provides comprehensive documentation of the eHub testing strategy, including unit testing, integration testing, API testing approaches, and quality assurance practices. It serves as the definitive guide for understanding and implementing testing strategies across the eHub platform.

## Prerequisites

- Understanding of .NET testing frameworks and patterns
- Knowledge of MSTest, Moq, and dependency injection
- Familiarity with eHub architecture and business processes
- Basic understanding of test-driven development (TDD) principles

## Core Concepts

### Testing Philosophy

The eHub testing strategy is built on several key principles:

1. **Comprehensive Coverage**: Unit, integration, and end-to-end testing across all components
2. **Customer-Specific Testing**: Dedicated test suites for customer-specific business rules
3. **Automated Testing**: Continuous integration with automated test execution
4. **Test Data Management**: Structured approach to test data creation and maintenance
5. **Performance Testing**: Load testing and performance validation for critical paths

### Testing Architecture Overview

```
eHub Testing Architecture
├── Unit Testing
│   ├── Business Logic Tests - Core business rule validation
│   ├── Component Tests - Individual component functionality
│   ├── Service Tests - Service layer testing with mocking
│   └── Utility Tests - Helper and utility function testing
├── Integration Testing
│   ├── Database Integration - Entity Framework and SQL testing
│   ├── API Integration - REST API endpoint testing
│   ├── External System Integration - Third-party service testing
│   └── Workflow Integration - End-to-end process testing
├── Customer-Specific Testing
│   ├── PDF Processing Tests - Customer document processing
│   ├── Business Rule Tests - Customer-specific validation
│   ├── Workflow Tests - Customer workflow configuration
│   └── Data Extraction Tests - Customer data extraction rules
└── Performance Testing
    ├── Load Testing - High-volume transaction processing
    ├── Stress Testing - System limits and breaking points
    ├── Endurance Testing - Long-running process validation
    └── Scalability Testing - Multi-tenant performance validation
```

## Unit Testing Framework

### MSTest Framework Implementation

```csharp
[TestClass]
[TestCategory("Developer")]
[TestCategory("Production")]
[TestCategory("ProgramRule")]
public class TransactionProcessingTests : TestBase
{
    private Mock<IDataLayer> mockDataLayer;
    private Mock<IAuditRepository> mockAuditRepository;
    private TransactionProcessor transactionProcessor;

    [TestInitialize]
    public void SetUp()
    {
        this.mockDataLayer = new Mock<IDataLayer>();
        this.mockAuditRepository = new Mock<IAuditRepository>();
        
        // Setup common mock behaviours
        this.SetupMockDefaults();
        
        this.transactionProcessor = new TransactionProcessor(
            this.mockDataLayer.Object,
            this.mockAuditRepository.Object);
    }

    [TestCleanup]
    public void Cleanup()
    {
        // Clean up resources
        this.mockDataLayer?.Reset();
        this.mockAuditRepository?.Reset();
    }

    [TestMethod]
    public void ProcessTransaction_ValidInput_ReturnsSuccess()
    {
        // Arrange
        var transactionId = Guid.NewGuid();
        var expectedStatus = EnumTransactionStatus.ValidationSuccess;
        
        this.mockDataLayer
            .Setup(d => d.TransactionRepository.GetById(transactionId))
            .Returns(CreateValidTransaction(transactionId));

        // Act
        var result = this.transactionProcessor.ProcessTransaction(transactionId);

        // Assert
        Assert.IsTrue(result.Success);
        Assert.AreEqual(expectedStatus, result.Transaction.Status);
        
        // Verify audit logging
        this.mockAuditRepository.Verify(
            a => a.LogTransactionProcessed(It.IsAny<Guid>(), It.IsAny<Actor>()),
            Times.Once);
    }

    [TestMethod]
    [ExpectedException(typeof(ArgumentException))]
    public void ProcessTransaction_InvalidTransactionId_ThrowsException()
    {
        // Arrange
        var invalidId = Guid.Empty;

        // Act
        this.transactionProcessor.ProcessTransaction(invalidId);

        // Assert - Exception expected
    }
}
```

### Test Categories and Organisation

```csharp
// Test categories for different execution contexts
[TestCategory("Developer")]      // Run during development
[TestCategory("Production")]     // Safe for production environment
[TestCategory("CustomerData")]   // Customer-specific data tests
[TestCategory("ProgramRule")]    // Business rule validation tests
[TestCategory("Critical")]       // Critical functionality tests
[TestCategory("Performance")]    // Performance and load tests
[TestCategory("Integration")]    // Integration tests

// Example usage in test classes
[TestClass]
[TestCategory("Developer")]
[TestCategory("Production")]
[TestCategory("Critical")]
public class CoreBusinessLogicTests
{
    // Critical business logic tests
}

[TestClass]
[TestCategory("CustomerData")]
public class CustomerSpecificTests
{
    // Customer-specific processing tests
}
```

## Customer-Specific Testing

### PDF Processing Tests

```csharp
[TestCategory("Customer_PDF_CWBerry_Extraction")]
[TestClass]
public class CWBerryPDFProcessingTests : ProcessDocumentsBase
{
    [TestMethod]
    public void CWBerry_BrettMartin_Extraction_Success()
    {
        // Arrange
        var document = this.GetDocument(
            fullFileNameOfPDFExtract: $"{TestDataPath}\\Brett Martin\\Brett Martin.pdf",
            pdfFileContainer: "BrettMartin",
            customerName: "CW Berry",
            azureRulesContainer: "CW Berry");

        var container = new TestContainers().ProgramExecutionContainer();
        var pdfExtract = container.Resolve<IPDFExtract>();
        var processProgram = container.Resolve<RunProgram>();

        // Act
        var pdfExtractModel = pdfExtract.BeginRead(document.FullFilePath, outputParameters);
        var programModel = new ProgramModel(document, directoryStructureModel, pdfExtractModel);
        var results = processProgram.UnitTestProcess("Test", programModel, pdfExtract);

        // Assert
        var transaction0 = results.Data.ProgramModel.Transactions[0];
        var transactionData = transaction0.TransactionData;

        Assert.AreEqual(25, programModel.WorkflowFieldStateModels.Count());
        Assert.AreEqual(49, transactionData.Count());
        
        // Verify specific field extractions
        this.AssertFieldValue(transactionData, "invoice_number", "INV-12345");
        this.AssertFieldValue(transactionData, "total_amount", "1234.56");
        this.AssertFieldValue(transactionData, "supplier_name", "Brett Martin");
    }

    private void AssertFieldValue(List<TransactionData> transactionData, string fieldName, string expectedValue)
    {
        var field = transactionData.FirstOrDefault(td => td.Name == fieldName);
        Assert.IsNotNull(field, $"Field '{fieldName}' not found in transaction data");
        Assert.AreEqual(expectedValue, field.ValueToString, $"Field '{fieldName}' has incorrect value");
    }
}
```

### Business Rule Testing

```mermaid
graph TB
    subgraph "Business Rule Test Flow"
        TR[Test Rule Definition] --> TP[Test Prolog Parsing]
        TP --> TE[Test Execution]
        TE --> TV[Test Validation]
        TV --> TA[Test Assertion]
    end
    
    subgraph "Test Data Setup"
        TD[Test Document] --> TM[Test Model Creation]
        TM --> TC[Test Context Setup]
        TC --> TE
    end
    
    subgraph "Validation Checks"
        TV --> FV[Field Validation]
        TV --> BV[Business Validation]
        TV --> WV[Workflow Validation]
        TV --> AV[Audit Validation]
    end
    
    style TR fill:#e3f2fd
    style TE fill:#fff3e0
    style TA fill:#e8f5e8
```

```csharp
[TestClass]
[TestCategory("ProgramRule")]
public class BusinessRuleTests : ProcessDocumentsBase
{
    [TestMethod]
    public void ProgramRule_VATNumberExtraction_Success()
    {
        // Arrange
        var convertPrologToJson = new ProgramSyntaxParser();
        var commands = convertPrologToJson.ProcessProlog(
            "i_rule_list( [ get_vat_number ] )." +
            "i_default( new_invoice_page )." +
            "PeteRule( get_vat_number, [`VAT`, Dummy(w), `No`, `.`, newline, supplier_vat_number(s1), newline ] )] ).");

        var jsonContainer = new JsonContainer<ExtractModel>(
            $"{TestDataPath}\\Json\\test_vat_extraction.json", 
            new FileSystemReader());

        var data = new DataModel(new ProgramModel(jsonContainer.Data, commands), new RuleData());

        // Act
        var container = new TestContainers().ProgramExecutionContainer();
        var executionFactory = container.Resolve<IExecutionFactory>();
        var program = new TestDocumentProcessor(executionFactory);
        data = program.ProcessDocumentsAndPages(data).Data;

        // Assert
        Assert.IsFalse(data.ProgramModel.Failures.Any(), "Processing should not have failures");
        
        var document0 = data.ProgramModel.Transactions[0];
        Assert.IsFalse(document0.Failures.Any(), "Document processing should not have failures");
        Assert.AreEqual(1, document0.ExecutionResults.Count, "Should have one execution result");
        
        // Verify VAT number extraction
        var vatField = document0.TransactionData.FirstOrDefault(td => td.Name == "supplier_vat_number");
        Assert.IsNotNull(vatField, "VAT number field should be extracted");
        Assert.IsTrue(!string.IsNullOrEmpty(vatField.ValueToString), "VAT number should have a value");
    }
}
```

## Integration Testing

### Database Integration Testing

```csharp
[TestClass]
[TestCategory("Integration")]
public class DatabaseIntegrationTests
{
    private DataContext testContext;
    private TransactionRepository transactionRepository;

    [TestInitialize]
    public void SetUp()
    {
        // Use in-memory database for testing
        this.testContext = DataContext.GetMemoryContext();
        this.transactionRepository = new TransactionRepository(this.testContext);
        
        // Seed test data
        this.SeedTestData();
    }

    [TestCleanup]
    public void Cleanup()
    {
        this.testContext?.Dispose();
    }

    [TestMethod]
    public void TransactionRepository_GetById_ReturnsCorrectTransaction()
    {
        // Arrange
        var expectedTransactionId = Guid.Parse("12345678-1234-1234-1234-123456789012");

        // Act
        var transaction = this.transactionRepository.GetById(expectedTransactionId);

        // Assert
        Assert.IsNotNull(transaction);
        Assert.AreEqual(expectedTransactionId, transaction.Id);
        Assert.IsNotNull(transaction.TransactionLines);
        Assert.IsTrue(transaction.TransactionLines.Any());
    }

    [TestMethod]
    public void TransactionRepository_SaveTransaction_PersistsCorrectly()
    {
        // Arrange
        var newTransaction = CreateTestTransaction();

        // Act
        this.transactionRepository.Add(newTransaction);
        this.testContext.SaveChanges();

        // Assert
        var savedTransaction = this.transactionRepository.GetById(newTransaction.Id);
        Assert.IsNotNull(savedTransaction);
        Assert.AreEqual(newTransaction.TransactionNumber, savedTransaction.TransactionNumber);
        Assert.AreEqual(newTransaction.Total, savedTransaction.Total);
    }

    private void SeedTestData()
    {
        var testCustomer = new CustomerDetail
        {
            Id = Guid.NewGuid(),
            TenantId = Guid.NewGuid(),
            Description = "Test Customer"
        };

        var testTransaction = new TransactionHeader
        {
            Id = Guid.Parse("12345678-1234-1234-1234-123456789012"),
            CustomerId = testCustomer.Id,
            TransactionNumber = "TEST-001",
            Total = 1000.00m,
            TransactionDate = DateTime.UtcNow
        };

        this.testContext.CustomerDetails.Add(testCustomer);
        this.testContext.TransactionHeaders.Add(testTransaction);
        this.testContext.SaveChanges();
    }
}
```

### API Integration Testing

```csharp
[TestClass]
[TestCategory("Integration")]
public class APIIntegrationTests
{
    private TestServer testServer;
    private HttpClient httpClient;

    [TestInitialize]
    public void SetUp()
    {
        var webHostBuilder = new WebHostBuilder()
            .UseStartup<TestStartup>()
            .ConfigureServices(services =>
            {
                // Replace real services with test implementations
                services.AddScoped<IDataLayer, MockDataLayer>();
                services.AddScoped<IAuditRepository, MockAuditRepository>();
            });

        this.testServer = new TestServer(webHostBuilder);
        this.httpClient = this.testServer.CreateClient();
        
        // Setup authentication
        this.SetupTestAuthentication();
    }

    [TestCleanup]
    public void Cleanup()
    {
        this.httpClient?.Dispose();
        this.testServer?.Dispose();
    }

    [TestMethod]
    public async Task TransactionAPI_GetTransaction_ReturnsCorrectData()
    {
        // Arrange
        var transactionId = Guid.NewGuid();
        var expectedTransaction = CreateTestTransaction(transactionId);
        
        // Setup mock to return expected data
        this.SetupMockTransaction(expectedTransaction);

        // Act
        var response = await this.httpClient.GetAsync($"/api/transaction/{transactionId}");
        var content = await response.Content.ReadAsStringAsync();
        var transaction = JsonConvert.DeserializeObject<TransactionModel>(content);

        // Assert
        Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
        Assert.IsNotNull(transaction);
        Assert.AreEqual(transactionId, transaction.Id);
        Assert.AreEqual(expectedTransaction.TransactionNumber, transaction.TransactionNumber);
    }

    [TestMethod]
    public async Task TransactionAPI_ApproveTransaction_UpdatesStatus()
    {
        // Arrange
        var transactionId = Guid.NewGuid();
        var approvalRequest = new { Comments = "Approved for testing" };
        var requestContent = new StringContent(
            JsonConvert.SerializeObject(approvalRequest),
            Encoding.UTF8,
            "application/json");

        // Act
        var response = await this.httpClient.PostAsync(
            $"/api/transaction/{transactionId}/approve",
            requestContent);

        // Assert
        Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
        
        // Verify the transaction status was updated
        var updatedTransaction = await this.GetTransactionFromAPI(transactionId);
        Assert.AreEqual(EnumTransactionStatus.ActionPending, updatedTransaction.Status);
    }
}
```

## Test Data Management

### Test Data Factory Pattern

```csharp
public class TestDataFactory
{
    public static TransactionHeader CreateTestTransaction(
        Guid? id = null,
        Guid? customerId = null,
        string transactionNumber = null,
        decimal? total = null)
    {
        return new TransactionHeader
        {
            Id = id ?? Guid.NewGuid(),
            CustomerId = customerId ?? Guid.NewGuid(),
            TransactionNumber = transactionNumber ?? $"TEST-{DateTime.Now.Ticks}",
            Total = total ?? 1000.00m,
            TransactionDate = DateTime.UtcNow,
            TransactionStatus = EnumTransactionStatus.Created,
            DateDocumentAdded = DateTime.UtcNow
        };
    }

    public static CustomerDetail CreateTestCustomer(
        Guid? id = null,
        Guid? tenantId = null,
        string description = null)
    {
        return new CustomerDetail
        {
            Id = id ?? Guid.NewGuid(),
            TenantId = tenantId ?? Guid.NewGuid(),
            Description = description ?? "Test Customer",
            DisableEmail = false,
            DeploymentColor = "#007bff"
        };
    }

    public static Document CreateTestDocument(
        Guid? id = null,
        Guid? customerId = null,
        string fileName = null)
    {
        return new Document
        {
            Id = id ?? Guid.NewGuid(),
            CustomerId = customerId ?? Guid.NewGuid(),
            FileName = fileName ?? "test-document.pdf",
            FullFilePath = $"test-container/{fileName ?? "test-document.pdf"}",
            FileContainer = "test-container",
            DateAdded = DateTime.UtcNow,
            Progress = EnumDocumentProgress.Processing
        };
    }
}
```

### Test Container Configuration

```csharp
public class TestContainers
{
    public WindsorContainer ProgramExecutionContainer()
    {
        var container = new WindsorContainer();
        
        // Register test implementations
        container.Register(
            Component.For<IDataLayer>().ImplementedBy<MockDataLayer>().LifestyleSingleton(),
            Component.For<IPDFExtract>().ImplementedBy<TestPDFExtract>().LifestyleSingleton(),
            Component.For<IExecutionFactory>().ImplementedBy<ExecutionFactory>().LifestyleSingleton(),
            Component.For<IFileSystemReader>().ImplementedBy<FileSystemReader>().LifestyleSingleton(),
            Component.For<IAuditRepository>().ImplementedBy<MockAuditRepository>().LifestyleSingleton()
        );

        return container;
    }

    public void InstallContainer(WindsorContainer container)
    {
        // Install test-specific components
        container.Install(new TestBusinessLogicInstaller());
        container.Install(new TestDataLayerInstaller());
        container.Install(new TestInfrastructureInstaller());
    }
}

public class MockDataLayer : IDataLayer
{
    public ITransactionRepository TransactionRepository { get; }
    public ICustomerRepository CustomerRepository { get; }
    public IAuditRepository AuditRepository { get; }
    
    public MockDataLayer()
    {
        this.TransactionRepository = new MockTransactionRepository();
        this.CustomerRepository = new MockCustomerRepository();
        this.AuditRepository = new MockAuditRepository();
    }
}
```

## Performance Testing

### Load Testing Framework

```csharp
[TestClass]
[TestCategory("Performance")]
public class LoadTests
{
    [TestMethod]
    public async Task TransactionProcessing_HighVolume_MaintainsPerformance()
    {
        // Arrange
        const int numberOfTransactions = 1000;
        const int maxProcessingTimeSeconds = 300; // 5 minutes
        
        var transactions = Enumerable.Range(1, numberOfTransactions)
            .Select(i => TestDataFactory.CreateTestTransaction())
            .ToList();

        var stopwatch = Stopwatch.StartNew();
        var processingTasks = new List<Task>();

        // Act
        foreach (var transaction in transactions)
        {
            var task = ProcessTransactionAsync(transaction);
            processingTasks.Add(task);
            
            // Throttle to prevent overwhelming the system
            if (processingTasks.Count >= 50)
            {
                await Task.WhenAny(processingTasks);
                processingTasks.RemoveAll(t => t.IsCompleted);
            }
        }

        await Task.WhenAll(processingTasks);
        stopwatch.Stop();

        // Assert
        Assert.IsTrue(stopwatch.Elapsed.TotalSeconds < maxProcessingTimeSeconds,
            $"Processing took {stopwatch.Elapsed.TotalSeconds} seconds, expected less than {maxProcessingTimeSeconds}");
        
        // Verify all transactions were processed successfully
        var failedTransactions = transactions.Where(t => t.TransactionStatus == EnumTransactionStatus.FailedExtraction);
        Assert.IsFalse(failedTransactions.Any(), 
            $"Found {failedTransactions.Count()} failed transactions during load test");
    }

    private async Task ProcessTransactionAsync(TransactionHeader transaction)
    {
        var processor = new TransactionProcessor(mockDataLayer, mockAuditRepository);
        await processor.ProcessTransactionAsync(transaction.Id);
    }
}
```

## Test Execution and CI/CD Integration

### Test Execution Strategy

```mermaid
graph TB
    subgraph "Development Testing"
        DEV[Developer Workstation] --> UT[Unit Tests]
        DEV --> IT[Integration Tests]
        UT --> LT[Local Test Run]
        IT --> LT
    end
    
    subgraph "Continuous Integration"
        PR[Pull Request] --> BUILD[Build Pipeline]
        BUILD --> UNIT[Unit Test Execution]
        UNIT --> INT[Integration Test Execution]
        INT --> PERF[Performance Test Execution]
        PERF --> DEPLOY[Deployment to Test Environment]
    end
    
    subgraph "Test Environment"
        DEPLOY --> E2E[End-to-End Tests]
        E2E --> SMOKE[Smoke Tests]
        SMOKE --> UAT[User Acceptance Testing]
    end
    
    subgraph "Production Deployment"
        UAT --> PROD[Production Deployment]
        PROD --> MONITOR[Production Monitoring]
        MONITOR --> HEALTH[Health Checks]
    end
    
    style UT fill:#e3f2fd
    style BUILD fill:#fff3e0
    style E2E fill:#e8f5e8
    style PROD fill:#ffebee
```

### Test Configuration Management

```xml
<!-- Test execution configuration -->
<RunSettings>
  <TestRunParameters>
    <Parameter name="TestEnvironment" value="Development" />
    <Parameter name="DatabaseConnectionString" value="InMemory" />
    <Parameter name="EnablePerformanceTests" value="false" />
    <Parameter name="CustomerTestDataPath" value="TestData\Customers" />
  </TestRunParameters>
  
  <DataCollectionRunSettings>
    <DataCollectors>
      <DataCollector friendlyName="Code Coverage" uri="datacollector://Microsoft/CodeCoverage/2.0">
        <Configuration>
          <CodeCoverage>
            <ModulePaths>
              <Include>
                <ModulePath>.*\.dll$</ModulePath>
              </Include>
              <Exclude>
                <ModulePath>.*Tests\.dll$</ModulePath>
                <ModulePath>.*TestFramework\.dll$</ModulePath>
              </Exclude>
            </ModulePaths>
          </CodeCoverage>
        </Configuration>
      </DataCollector>
    </DataCollectors>
  </DataCollectionRunSettings>
</RunSettings>
```

## Related Documents

- **[SystemOverview.md](./SystemOverview.md)** - High-level system architecture
- **[DeveloperQuickStart.md](./DeveloperQuickStart.md)** - Development environment setup
- **[PerformanceAndScaling.md](./PerformanceAndScaling.md)** - Performance optimization strategies
- **[TroubleshootingGuide.md](./TroubleshootingGuide.md)** - Debugging and troubleshooting
- **[BuildAndDeployment.md](./BuildAndDeployment.md)** - Build and deployment procedures

---

*This document provides comprehensive coverage of the eHub testing strategy. Refer to the related documentation for detailed implementation guidance on specific testing scenarios.*
