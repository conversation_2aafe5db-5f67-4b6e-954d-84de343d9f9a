# eHub Security Architecture

## Purpose & Scope

This document provides comprehensive documentation of the eHub security architecture, including authentication systems, authorization mechanisms, multi-tenant security, data protection, and security monitoring. It serves as the definitive guide for understanding and implementing security measures in the eHub platform.

## Prerequisites

- Understanding of OAuth 2.0/OIDC authentication protocols
- Knowledge of JWT token structure and validation
- Familiarity with multi-tenant application security
- Basic understanding of encryption and data protection concepts

## Core Concepts

### Security Architecture Philosophy

The eHub security architecture is built on several key principles:

1. **Defense in Depth**: Multiple layers of security validation and protection
2. **Zero Trust Model**: All requests are authenticated and authorized regardless of source
3. **Multi-Tenant Isolation**: Complete data separation between customers/tenants
4. **Comprehensive Auditing**: Full audit trails for compliance and forensics
5. **Least Privilege Access**: Users have minimal access required for their role

### Security Overview

```
eHub Security Architecture
├── Authentication Layer
│   ├── OIDC/OAuth 2.0 Integration
│   ├── JWT Token Management
│   ├── External Provider Support
│   └── Session Management
├── Authorization Layer
│   ├── Role-Based Access Control
│   ├── Multi-Tenant Isolation
│   ├── Resource-Level Permissions
│   └── Business Rule Authorization
├── Data Protection Layer
│   ├── Encryption at Rest
│   ├── Encryption in Transit
│   ├── Data Masking
│   └── Secure File Storage
└── Monitoring & Compliance
    ├── Comprehensive Audit Logging
    ├── Security Event Monitoring
    ├── Compliance Reporting
    └── Threat Detection
```

## Authentication Systems

### OIDC/OAuth 2.0 Integration

**Location**: `ECXAuth/`

The eHub platform implements a sophisticated OIDC integration system supporting multiple external identity providers:

```csharp
public enum EnumOidcProviderType
{
    Sage = 1,
    Hansaworld = 2,
    BasicAuth = 3,
    NTLMAuth = 4,
    Office365 = 5,
    Insphire = 6
}
```

#### OIDC Session Management

```csharp
[Table("OidcSession", Schema = "Portal")]
public class OidcSession
{
    public Guid Id { get; set; }
    public string AccessToken { get; set; }
    public string RefreshToken { get; set; }
    public DateTime TokenExpiresAtUtc { get; set; }
    public DateTime RefreshTokenExpiresAtUtc { get; set; }
    
    // Multi-tenant linking
    public Guid EHubCustomerId { get; set; }
    public string UserName { get; set; }
    
    // Provider integration
    public int OidcProviderType { get; set; }
    public string ExternalUserId { get; set; }
    public string CallBackUrl { get; set; }
}
```

#### Authentication Flow

```csharp
public class OidcHelper
{
    public static string GetAccessToken(OidcSession session)
    {
        // Check token expiration with 10-second buffer
        if (session.TokenExpiresAtUtc <= DateTime.UtcNow.AddSeconds(10))
        {
            // Token expired - attempt refresh
            if (session.RefreshTokenExpiresAtUtc > DateTime.UtcNow)
            {
                return RefreshAccessToken(session);
            }
            
            // Refresh token also expired - require re-authentication
            throw new AuthenticationExpiredException("Authentication session has expired");
        }
        
        return session.AccessToken;
    }
    
    private static string RefreshAccessToken(OidcSession session)
    {
        // Provider-specific token refresh logic
        var provider = GetProvider(session.OidcProviderType);
        var newTokens = provider.RefreshTokenAsync(session.RefreshToken).Result;
        
        // Update session with new tokens
        session.AccessToken = newTokens.AccessToken;
        session.TokenExpiresAtUtc = newTokens.ExpiresAt;
        
        return newTokens.AccessToken;
    }
}
```

### JWT Bearer Authentication

**Configuration**: `ETradingAPI/Startup.cs`

```csharp
public void ConfigureServices(IServiceCollection services)
{
    services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
        .AddJwtBearer(options =>
        {
            options.TokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuer = true,
                ValidateAudience = false,
                ValidateLifetime = true,
                ValidateIssuerSigningKey = true,
                ValidIssuer = Configuration["Jwt:Issuer"],
                IssuerSigningKey = new SymmetricSecurityKey(
                    Convert.FromBase64String(Configuration["Jwt:Key"])
                ),
                ClockSkew = TimeSpan.Zero
            };
        });
}
```

#### Custom Token Extraction

```csharp
public class TokenExtractor
{
    public static Actor ExtractActor(HttpRequest request)
    {
        return new Actor
        {
            UserId = GetGuidFromHeader(request, "ehub-user-id"),
            UserFullName = GetStringFromHeader(request, "ehub-user-full-name"),
            UserMemberId = GetGuidFromHeader(request, "ehub-user-member-id"),
            MemberName = GetStringFromHeader(request, "ehub-user-member-name")
        };
    }
    
    private static Guid GetGuidFromHeader(HttpRequest request, string headerName)
    {
        if (request.Headers.TryGetValue(headerName, out var headerValue))
        {
            if (Guid.TryParse(headerValue.FirstOrDefault(), out var guid))
            {
                return guid;
            }
        }
        
        throw new SecurityException($"Required header '{headerName}' not found or invalid");
    }
}
```

#### JWT Claims Structure

```csharp
public class EHubJwtClaims
{
    public const string UserId = "userId";
    public const string FullName = "fullName";
    public const string EHubCustomerId = "eHubCustomerId";
    public const string MemberName = "memberName";
    public const string Role = "role";
    public const string Permissions = "permissions";
}
```

### Authentication Validation

```csharp
public static class ValidateActor
{
    public static IDataOrErrorResult<Actor, ValidateActorErrors> Validate(
        Guid userId, 
        string userFullName, 
        Guid userMemberId, 
        string memberName)
    {
        // Validate required fields
        if (userId == Guid.Empty)
        {
            return ErrorResult.Create(ValidateActorErrors.InvalidUserId);
        }
        
        if (string.IsNullOrWhiteSpace(userFullName))
        {
            return ErrorResult.Create(ValidateActorErrors.InvalidUserName);
        }
        
        if (userMemberId == Guid.Empty)
        {
            return ErrorResult.Create(ValidateActorErrors.InvalidCustomerId);
        }
        
        if (string.IsNullOrWhiteSpace(memberName))
        {
            return ErrorResult.Create(ValidateActorErrors.InvalidCustomerName);
        }
        
        return DataResult.Create(new Actor
        {
            UserId = userId,
            UserFullName = userFullName,
            UserMemberId = userMemberId,
            MemberName = memberName
        });
    }
}
```

## Authorization Mechanisms

### Role-Based Access Control (RBAC)

#### Actor-Based Security Model

```csharp
public class Actor
{
    public Guid UserId { get; set; }
    public string UserFullName { get; set; }
    public Guid UserMemberId { get; set; }
    public string MemberName { get; set; }
    public List<string> Roles { get; set; } = new List<string>();
    public List<string> Permissions { get; set; } = new List<string>();
}

// Predefined system actors
public static class SystemActors
{
    public static readonly Actor Marketplace = new Actor
    {
        UserId = new Guid("00000000-0000-0000-0000-000000000001"),
        UserFullName = "Marketplace System",
        UserMemberId = EHubConstants.MarketplaceMemberId,
        MemberName = "Marketplace"
    };
    
    public static readonly Actor ETrading = new Actor
    {
        UserId = new Guid("00000000-0000-0000-0000-000000000002"),
        UserFullName = "ETrading System",
        UserMemberId = EHubConstants.ETradingMemberId,
        MemberName = "ETrading"
    };
}
```

#### Authorization Attributes

```csharp
[Authorize]
[Route("api/members/{customerId:Guid}/[controller]")]
public class TransactionController : ControllerBase
{
    [HttpPost("GetTransactions")]
    public async Task<IActionResult> GetTransactions(Guid customerId, [FromBody] GridParametersModel model)
    {
        // Extract and validate actor
        var actor = TokenExtractor.ExtractActor(Request);
        var validation = ValidateActor.Validate(actor);
        if (!validation.Success)
        {
            return BadRequest(validation.Error);
        }
        
        // Verify customer ownership
        if (actor.UserMemberId != customerId)
        {
            return Forbid("User does not have access to this customer's data");
        }
        
        // Proceed with business logic
        var transactions = await _service.GetTransactionsAsync(customerId, model);
        return Ok(transactions);
    }
}
```

### Multi-Tenant Authorization

#### Customer Ownership Validation

```csharp
public static class ValidateActor
{
    public static IDataOrErrorResult<bool, ValidateActorErrors> CheckCustomerOwnsTransaction(
        Guid customerId, 
        Guid transactionHeaderId)
    {
        using (var context = new DataContext())
        {
            var transaction = context.TransactionHeaders
                .Where(t => t.Id == transactionHeaderId)
                .Select(t => new { t.TenantId, t.CustomerId })
                .FirstOrDefault();
            
            if (transaction == null)
            {
                return ErrorResult.Create(ValidateActorErrors.TransactionNotFound);
            }
            
            if (transaction.CustomerId != customerId)
            {
                return ErrorResult.Create(ValidateActorErrors.CustomerDoesNotOwnTransaction);
            }
            
            return DataResult.Create(true);
        }
    }
}
```

#### Route-Level Security

```csharp
// Member-scoped routing ensures automatic tenant isolation
[Route("api/members/{customerId:Guid}/transactions/{transactionId:Guid}")]
public async Task<IActionResult> GetTransaction(Guid customerId, Guid transactionId)
{
    // The route parameters automatically provide tenant context
    // Business logic can safely assume user has access to this customer
    var transaction = await _service.GetTransactionAsync(customerId, transactionId);
    return Ok(transaction);
}
```

### Permission-Based Authorization

```csharp
public class PermissionBasedAuthorization
{
    public static async Task<bool> HasPermissionAsync(
        Actor actor, 
        string resource, 
        string action)
    {
        // Check explicit permissions
        var permission = $"{resource}.{action}";
        if (actor.Permissions.Contains(permission))
        {
            return true;
        }
        
        // Check role-based permissions
        var rolePermissions = await GetRolePermissionsAsync(actor.Roles);
        return rolePermissions.Contains(permission);
    }
    
    private static async Task<List<string>> GetRolePermissionsAsync(List<string> roles)
    {
        using (var context = new SecurityContext())
        {
            return await context.RolePermissions
                .Where(rp => roles.Contains(rp.RoleName))
                .Select(rp => rp.Permission)
                .ToListAsync();
        }
    }
}
```

## Data Protection and Encryption

### Password Security

```csharp
public class PasswordHasher
{
    private const int IterationCount = 10000;
    private const int SaltSize = 32;
    private const int HashSize = 32;
    
    public static string HashPassword(string password)
    {
        // Generate random salt
        var salt = GenerateRandomSalt();
        
        // Hash password with PBKDF2
        using (var pbkdf2 = new Rfc2898DeriveBytes(password, salt, IterationCount, HashAlgorithmName.SHA256))
        {
            var hash = pbkdf2.GetBytes(HashSize);
            
            // Combine salt and hash for storage
            var combined = new byte[SaltSize + HashSize];
            Array.Copy(salt, 0, combined, 0, SaltSize);
            Array.Copy(hash, 0, combined, SaltSize, HashSize);
            
            return Convert.ToBase64String(combined);
        }
    }
    
    public static bool VerifyPassword(string password, string hashedPassword)
    {
        var combined = Convert.FromBase64String(hashedPassword);
        
        // Extract salt and hash
        var salt = new byte[SaltSize];
        var hash = new byte[HashSize];
        Array.Copy(combined, 0, salt, 0, SaltSize);
        Array.Copy(combined, SaltSize, hash, 0, HashSize);
        
        // Hash input password with extracted salt
        using (var pbkdf2 = new Rfc2898DeriveBytes(password, salt, IterationCount, HashAlgorithmName.SHA256))
        {
            var inputHash = pbkdf2.GetBytes(HashSize);
            return CryptographicOperations.FixedTimeEquals(hash, inputHash);
        }
    }
}
```

### Data Encryption in Transit

```csharp
public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
{
    // Force HTTPS redirection
    app.UseHttpsRedirection();
    
    // Add security headers
    app.Use(async (context, next) =>
    {
        context.Response.Headers.Add("X-Frame-Options", "DENY");
        context.Response.Headers.Add("X-Content-Type-Options", "nosniff");
        context.Response.Headers.Add("X-XSS-Protection", "1; mode=block");
        context.Response.Headers.Add("Strict-Transport-Security", "max-age=31536000; includeSubDomains");
        
        await next();
    });
    
    app.UseAuthentication();
    app.UseAuthorization();
}
```

### File System Security

```csharp
public class SecureFileStorage
{
    private readonly IAzureStorageService _storage;
    private readonly IEncryptionService _encryption;
    
    public async Task<string> StoreSecureFileAsync(byte[] fileData, string fileName)
    {
        // Encrypt file data
        var encryptedData = await _encryption.EncryptAsync(fileData);
        
        // Generate secure file path
        var secureFileName = GenerateSecureFileName(fileName);
        
        // Store with access controls
        var containerName = GetSecureContainer();
        await _storage.UploadAsync(containerName, secureFileName, encryptedData);
        
        return secureFileName;
    }
    
    private string GenerateSecureFileName(string originalFileName)
    {
        var extension = Path.GetExtension(originalFileName);
        var secureId = Guid.NewGuid().ToString("N");
        return $"{secureId}{extension}";
    }
}
```

### AS2 Protocol Security

```csharp
public class AS2SecurityConfiguration
{
    public class EncryptionSettings
    {
        public string Algorithm { get; set; } = "DES3"; // or "RC2"
        public X509Certificate2 Certificate { get; set; }
        public bool RequireEncryption { get; set; } = true;
    }
    
    public class SigningSettings
    {
        public string Algorithm { get; set; } = "SHA256";
        public X509Certificate2 Certificate { get; set; }
        public bool RequireSigning { get; set; } = true;
    }
    
    public async Task<AS2Message> SecureMessageAsync(AS2Message message)
    {
        // Apply encryption
        if (EncryptionSettings.RequireEncryption)
        {
            message = await EncryptMessageAsync(message);
        }
        
        // Apply digital signature
        if (SigningSettings.RequireSigning)
        {
            message = await SignMessageAsync(message);
        }
        
        return message;
    }
}
```

## Security Monitoring and Audit

### Comprehensive Audit Logging

```csharp
public class SecurityAuditService
{
    public async Task LogSecurityEventAsync(SecurityEvent securityEvent)
    {
        var auditEntry = new AuditEntry
        {
            Timestamp = DateTime.UtcNow,
            EventType = securityEvent.Type,
            UserId = securityEvent.Actor?.UserId,
            UserName = securityEvent.Actor?.UserFullName,
            Resource = securityEvent.Resource,
            Action = securityEvent.Action,
            Result = securityEvent.Result,
            IpAddress = securityEvent.IpAddress,
            UserAgent = securityEvent.UserAgent,
            AdditionalData = JsonSerializer.Serialize(securityEvent.AdditionalData)
        };
        
        await _auditRepository.SaveAsync(auditEntry);
        
        // Real-time security monitoring
        if (IsSecurityCritical(securityEvent))
        {
            await _alertService.SendSecurityAlertAsync(securityEvent);
        }
    }
}
```

### Security Event Types

```csharp
public enum SecurityEventType
{
    Authentication,
    AuthenticationFailure,
    Authorization,
    AuthorizationFailure,
    DataAccess,
    DataModification,
    PasswordChange,
    PasswordReset,
    AccountLockout,
    SessionTimeout,
    SuspiciousActivity,
    SecurityViolation
}

public class SecurityEvent
{
    public SecurityEventType Type { get; set; }
    public Actor Actor { get; set; }
    public string Resource { get; set; }
    public string Action { get; set; }
    public SecurityResult Result { get; set; }
    public string IpAddress { get; set; }
    public string UserAgent { get; set; }
    public Dictionary<string, object> AdditionalData { get; set; }
    public DateTime Timestamp { get; set; }
}
```

### Real-Time Security Monitoring

```csharp
public class SecurityMonitoringService
{
    private readonly ILogger<SecurityMonitoringService> _logger;
    
    public async Task MonitorRequestAsync(HttpContext context)
    {
        // Track suspicious patterns
        await DetectBruteForceAttacksAsync(context);
        await DetectSqlInjectionAttemptsAsync(context);
        await DetectXssAttemptsAsync(context);
        await DetectUnauthorizedAccessPatternsAsync(context);
    }
    
    private async Task DetectBruteForceAttacksAsync(HttpContext context)
    {
        var ipAddress = GetClientIpAddress(context);
        var failureCount = await GetAuthenticationFailureCountAsync(ipAddress, TimeSpan.FromMinutes(15));
        
        if (failureCount > 5)
        {
            await _securityAudit.LogSecurityEventAsync(new SecurityEvent
            {
                Type = SecurityEventType.SuspiciousActivity,
                IpAddress = ipAddress,
                Resource = "Authentication",
                Action = "BruteForceDetected",
                Result = SecurityResult.Blocked,
                AdditionalData = new Dictionary<string, object>
                {
                    ["FailureCount"] = failureCount,
                    ["TimeWindow"] = "15 minutes"
                }
            });
            
            // Implement rate limiting or IP blocking
            await BlockIpAddressAsync(ipAddress, TimeSpan.FromHours(1));
        }
    }
}
```

## Security Configuration Management

### Environment-Specific Security

```csharp
public class SecurityConfiguration
{
    public class DevelopmentSettings
    {
        public bool AllowAnonymousAccess { get; set; } = false;
        public bool EnableDebugEndpoints { get; set; } = false;
        public bool BypassAuthentication { get; set; } = false;
    }
    
    public class ProductionSettings
    {
        public bool RequireHttps { get; set; } = true;
        public bool EnforceStrictSecurityHeaders { get; set; } = true;
        public int TokenExpirationMinutes { get; set; } = 60;
        public int RefreshTokenExpirationDays { get; set; } = 30;
    }
}
```

### Secure Configuration Management

```csharp
public class SecureConfigurationService
{
    public async Task<string> GetSecureConfigValueAsync(string key)
    {
        // Try Azure Key Vault first
        try
        {
            return await _keyVaultClient.GetSecretAsync(key);
        }
        catch (KeyVaultException)
        {
            // Fallback to encrypted configuration
            var encryptedValue = _configuration[key];
            return await _encryptionService.DecryptAsync(encryptedValue);
        }
    }
    
    public async Task SetSecureConfigValueAsync(string key, string value)
    {
        // Always store in Key Vault for production
        if (_environment.IsProduction())
        {
            await _keyVaultClient.SetSecretAsync(key, value);
        }
        else
        {
            // Encrypt for non-production environments
            var encryptedValue = await _encryptionService.EncryptAsync(value);
            _configuration[key] = encryptedValue;
        }
    }
}
```

## Security Best Practices Implementation

### Input Validation and Sanitization

```csharp
public class SecurityValidationService
{
    public static ValidationResult ValidateInput(string input, InputType type)
    {
        if (string.IsNullOrWhiteSpace(input))
        {
            return ValidationResult.Error("Input cannot be empty");
        }
        
        return type switch
        {
            InputType.Email => ValidateEmail(input),
            InputType.Username => ValidateUsername(input),
            InputType.Password => ValidatePassword(input),
            InputType.TransactionNumber => ValidateTransactionNumber(input),
            _ => ValidationResult.Success()
        };
    }
    
    private static ValidationResult ValidatePassword(string password)
    {
        var requirements = new List<string>();
        
        if (password.Length < 8)
            requirements.Add("at least 8 characters");
        if (!password.Any(char.IsUpper))
            requirements.Add("at least one uppercase letter");
        if (!password.Any(char.IsLower))
            requirements.Add("at least one lowercase letter");
        if (!password.Any(char.IsDigit))
            requirements.Add("at least one number");
        if (!password.Any(c => "!@#$%^&*()_+-=[]{}|;:,.<>?".Contains(c)))
            requirements.Add("at least one special character");
        
        if (requirements.Any())
        {
            return ValidationResult.Error($"Password must contain {string.Join(", ", requirements)}");
        }
        
        return ValidationResult.Success();
    }
}
```

### SQL Injection Prevention

```csharp
public class SecureDataAccess
{
    // Always use parameterized queries
    public async Task<Transaction> GetTransactionSecureAsync(Guid transactionId, Guid customerId)
    {
        const string sql = @"
            SELECT * FROM TransactionHeader 
            WHERE Id = @TransactionId 
            AND CustomerId = @CustomerId";
        
        using var connection = new SqlConnection(_connectionString);
        var parameters = new
        {
            TransactionId = transactionId,
            CustomerId = customerId
        };
        
        return await connection.QueryFirstOrDefaultAsync<Transaction>(sql, parameters);
    }
    
    // Never use string concatenation for SQL
    public async Task<IEnumerable<Transaction>> SearchTransactionsAsync(string searchTerm, Guid customerId)
    {
        // Validate and sanitize search term
        var sanitizedTerm = SanitizeSearchTerm(searchTerm);
        
        const string sql = @"
            SELECT * FROM TransactionHeader 
            WHERE CustomerId = @CustomerId 
            AND (TransactionNumber LIKE @SearchTerm OR SupplierName LIKE @SearchTerm)";
        
        using var connection = new SqlConnection(_connectionString);
        var parameters = new
        {
            CustomerId = customerId,
            SearchTerm = $"%{sanitizedTerm}%"
        };
        
        return await connection.QueryAsync<Transaction>(sql, parameters);
    }
}
```

### Cross-Site Scripting (XSS) Prevention

```csharp
public class XssProtectionService
{
    public static string SanitizeHtml(string input)
    {
        if (string.IsNullOrWhiteSpace(input))
            return string.Empty;
        
        // Use HtmlEncoder to encode dangerous characters
        return HtmlEncoder.Default.Encode(input);
    }
    
    public static string SanitizeForJson(string input)
    {
        if (string.IsNullOrWhiteSpace(input))
            return string.Empty;
        
        // Use JsonEncoder to encode dangerous characters
        return JsonEncoder.Default.Encode(input);
    }
}

// Usage in API responses
public async Task<IActionResult> GetCustomer(Guid customerId)
{
    var customer = await _service.GetCustomerAsync(customerId);
    
    // Sanitize sensitive fields before returning
    var response = new CustomerResponse
    {
        Id = customer.Id,
        Name = XssProtectionService.SanitizeHtml(customer.Name),
        Description = XssProtectionService.SanitizeHtml(customer.Description)
    };
    
    return Ok(response);
}
```

## Security Testing and Validation

### Security Unit Tests

```csharp
[TestClass]
public class SecurityTests
{
    [TestMethod]
    public async Task Authentication_ExpiredToken_ShouldReturnUnauthorized()
    {
        // Arrange
        var expiredToken = GenerateExpiredJwtToken();
        var request = new HttpRequestMessage();
        request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", expiredToken);
        
        // Act
        var response = await _client.SendAsync(request);
        
        // Assert
        Assert.AreEqual(HttpStatusCode.Unauthorized, response.StatusCode);
    }
    
    [TestMethod]
    public async Task Authorization_CrossTenantAccess_ShouldReturnForbidden()
    {
        // Arrange
        var userToken = GenerateTokenForCustomer(CustomerA.Id);
        var request = new HttpRequestMessage(HttpMethod.Get, $"/api/members/{CustomerB.Id}/transactions");
        request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", userToken);
        
        // Act
        var response = await _client.SendAsync(request);
        
        // Assert
        Assert.AreEqual(HttpStatusCode.Forbidden, response.StatusCode);
    }
    
    [TestMethod]
    public void PasswordHashing_SamePassword_ShouldProduceDifferentHashes()
    {
        // Arrange
        const string password = "TestPassword123!";
        
        // Act
        var hash1 = PasswordHasher.HashPassword(password);
        var hash2 = PasswordHasher.HashPassword(password);
        
        // Assert
        Assert.AreNotEqual(hash1, hash2, "Same password should produce different hashes due to salt");
        Assert.IsTrue(PasswordHasher.VerifyPassword(password, hash1));
        Assert.IsTrue(PasswordHasher.VerifyPassword(password, hash2));
    }
}
```

## Security Incident Response

### Incident Detection and Response

```csharp
public class SecurityIncidentResponse
{
    public async Task HandleSecurityIncidentAsync(SecurityIncident incident)
    {
        // Log the incident
        await _logger.LogCriticalAsync($"Security incident detected: {incident.Type}", incident);
        
        // Immediate response based on severity
        switch (incident.Severity)
        {
            case IncidentSeverity.Critical:
                await HandleCriticalIncidentAsync(incident);
                break;
            case IncidentSeverity.High:
                await HandleHighSeverityIncidentAsync(incident);
                break;
            case IncidentSeverity.Medium:
                await HandleMediumSeverityIncidentAsync(incident);
                break;
        }
        
        // Notify security team
        await _notificationService.NotifySecurityTeamAsync(incident);
        
        // Begin forensic data collection
        await _forensicsService.CollectEvidenceAsync(incident);
    }
    
    private async Task HandleCriticalIncidentAsync(SecurityIncident incident)
    {
        // Immediate containment actions
        if (incident.Type == IncidentType.DataBreach)
        {
            await _dataProtectionService.EnableEmergencyEncryptionAsync();
            await _accessControlService.RevokeAllActiveSessionsAsync();
        }
        
        if (incident.Type == IncidentType.SystemCompromise)
        {
            await _systemProtectionService.EnableEmergencyShutdownAsync();
            await _backupService.CreateEmergencyBackupAsync();
        }
    }
}
```

## Security Recommendations

### Immediate Security Improvements

1. **Implement Proper Hangfire Authorization**:
```csharp
public class HangfireAuthorizationFilter : IDashboardAuthorizationFilter
{
    public bool Authorize(DashboardContext context)
    {
        var httpContext = context.GetHttpContext();
        var user = httpContext.User;
        
        return user.Identity.IsAuthenticated && 
               user.IsInRole("Administrator");
    }
}
```

2. **Migrate to Azure Key Vault**:
```csharp
public void ConfigureServices(IServiceCollection services)
{
    if (_environment.IsProduction())
    {
        var keyVaultEndpoint = Configuration["KeyVault:Endpoint"];
        var credential = new DefaultAzureCredential();
        
        Configuration.AddAzureKeyVault(keyVaultEndpoint, credential);
    }
}
```

3. **Implement Rate Limiting**:
```csharp
public void ConfigureServices(IServiceCollection services)
{
    services.AddRateLimiter(options =>
    {
        options.GlobalLimiter = PartitionedRateLimiter.Create<HttpContext, string>(
            httpContext => RateLimitPartition.GetFixedWindowLimiter(
                partitionKey: httpContext.User.Identity?.Name ?? httpContext.Request.Headers.Host.ToString(),
                factory: partition => new FixedWindowRateLimiterOptions
                {
                    AutoReplenishment = true,
                    PermitLimit = 100,
                    Window = TimeSpan.FromMinutes(1)
                }));
    });
}
```

4. **Add Content Security Policy**:
```csharp
app.Use(async (context, next) =>
{
    context.Response.Headers.Add("Content-Security-Policy", 
        "default-src 'self'; " +
        "script-src 'self' 'unsafe-inline'; " +
        "style-src 'self' 'unsafe-inline'; " +
        "img-src 'self' data:; " +
        "connect-src 'self'; " +
        "font-src 'self'");
    
    await next();
});
```

## Related Documents

- **[DatabaseArchitecture.md](./DatabaseArchitecture.md)** - Database security and multi-tenancy
- **[APIArchitecture.md](./APIArchitecture.md)** - API security patterns and authentication
- **[BusinessLogicArchitecture.md](./BusinessLogicArchitecture.md)** - Business rule security
- **[AuthenticationGuide.md](./AuthenticationGuide.md)** - Detailed authentication flows
- **[ComplianceAndAuditing.md](./ComplianceAndAuditing.md)** - Compliance and audit requirements

---

*This security architecture documentation provides comprehensive guidance for understanding and implementing security measures in the eHub platform. The multi-layered security approach, comprehensive audit system, and robust authentication mechanisms provide a strong foundation for enterprise-grade security, though continuous improvement and monitoring are essential for maintaining security posture.*