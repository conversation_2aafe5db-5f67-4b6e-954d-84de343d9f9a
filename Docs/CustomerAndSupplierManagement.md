# eHub Customer and Supplier Management

## Purpose & Scope

This document provides comprehensive documentation of the eHub customer and supplier management system, including master data management, customer configuration, supplier setup, and relationship management. It serves as the definitive guide for understanding and managing business relationships in the eHub system.

## Prerequisites

- Understanding of B2B trading relationships and master data concepts
- Knowledge of multi-tenant architecture and data isolation
- Familiarity with eHub data models and entity relationships
- Basic understanding of customer configuration and business rules

## Core Concepts

### Master Data Management Philosophy

The eHub customer and supplier management system is built on several key principles:

1. **Centralised Master Data**: Single source of truth for customer and supplier information
2. **Multi-Tenant Isolation**: Secure separation of customer data across tenants
3. **Flexible Configuration**: Customer-specific settings and business rules
4. **Relationship Mapping**: Complex supplier-customer relationship management
5. **Audit and Compliance**: Complete change tracking for regulatory requirements

### Customer and Supplier Management Overview

```
eHub Customer and Supplier Management Architecture
├── Customer Management
│   ├── Customer Master Data - Core customer information and settings
│   ├── Customer Configuration - Business rules and workflow settings
│   ├── Customer Relationships - Supplier and partner relationships
│   └── Customer Workbenches - Processing and workflow configuration
├── Supplier Management
│   ├── Supplier Master Data - Supplier information and capabilities
│   ├── Supplier Relationships - Customer-supplier mappings
│   ├── Supplier Configuration - Integration and processing settings
│   └── Supplier Validation - Compliance and verification
├── Relationship Management
│   ├── Customer-Supplier Links - Trading relationship configuration
│   ├── Address Management - Contact and delivery address management
│   ├── Contact Management - Communication and notification settings
│   └── Trading Terms - Commercial terms and conditions
└── Configuration Management
    ├── Workflow Configuration - Customer-specific workflow rules
    ├── Validation Rules - Business validation and compliance
    ├── Integration Settings - External system configuration
    └── Notification Settings - Email and alert configuration
```

## Customer Master Data Management

### Customer Entity Structure

```csharp
[Table("CustomerDetail", Schema = "Customer")]
public class CustomerDetail
{
    // Dual-key pattern for performance
    [Key]
    public Guid Id { get; set; }
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int ClusteredId { get; set; }
    
    // Multi-tenancy
    [Required]
    public Guid TenantId { get; set; }
    
    // Core customer information
    [Required]
    public string Description { get; set; }
    public bool DisableEmail { get; set; }
    public string DeploymentColor { get; set; }
    
    // Approval workflow configuration
    public bool CustomerApprovesMappings { get; set; }
    public string CustomerApprovalEmailAddresses { get; set; }
    
    // Processing configuration
    public bool EnableCustomerSuspensions { get; set; }
    public string DefaultCurrency { get; set; }
    public string TimeZone { get; set; }
    
    // Integration settings
    public string ExternalSystemId { get; set; }
    public string APIEndpoint { get; set; }
    public string AuthenticationToken { get; set; }
    
    // Relationships
    public ICollection<WorkBench> WorkBenches { get; set; }
    public ICollection<CustomerSupplierLink> CustomerSupplierLinks { get; set; }
    public ICollection<TransactionGroup> TransactionGroups { get; set; }
}
```

### Customer Service Implementation

```csharp
public class CustomerService
{
    public async Task<FormResponse<CustomerModel>> CreateCustomerAsync(CustomerCreateModel customerCreateModel, Actor createdBy)
    {
        var formResponse = new FormResponse<CustomerModel>(true);

        // Validation
        if (string.IsNullOrEmpty(customerCreateModel.Description))
        {
            formResponse.AddError("Customer description is required", new FieldIdentifier(nameof(CustomerCreateModel.Description)));
        }

        if (!formResponse.Success)
            return formResponse;

        try
        {
            var customer = new CustomerDetail
            {
                Id = Guid.NewGuid(),
                TenantId = customerCreateModel.TenantId,
                Description = customerCreateModel.Description,
                DisableEmail = customerCreateModel.DisableEmail,
                DeploymentColor = customerCreateModel.DeploymentColor ?? "#007bff",
                CustomerApprovesMappings = customerCreateModel.CustomerApprovesMappings,
                CustomerApprovalEmailAddresses = customerCreateModel.CustomerApprovalEmailAddresses,
                EnableCustomerSuspensions = customerCreateModel.EnableCustomerSuspensions,
                DefaultCurrency = customerCreateModel.DefaultCurrency ?? "GBP",
                TimeZone = customerCreateModel.TimeZone ?? "GMT Standard Time"
            };

            this.context.CustomerDetails.Add(customer);

            // Create audit trail
            var parent = new Entity(customer.TenantId, nameof(Tenant));
            var logs = this.context.GetTrackedChangesForCreate(createdBy, parent);

            await this.context.SaveChangesAsync();
            await this.genericAudit.CreateAudit(logs);

            formResponse.Data = customer.ToCustomerModel();
            return formResponse;
        }
        catch (Exception ex)
        {
            var errorMessage = ex.InnerException?.Message ?? ex.Message;
            return formResponse.AddError(errorMessage, new FieldIdentifier(string.Empty));
        }
    }

    public async Task<GridResultModel<CustomerListItemModel>> GetCustomersAsync(Guid tenantId, GridParametersModel gridParametersModel)
    {
        var query = this.context.CustomerDetails
            .Where(c => c.TenantId == tenantId)
            .Select(c => new CustomerListItemModel
            {
                Id = c.Id,
                Description = c.Description,
                DeploymentColor = c.DeploymentColor,
                DisableEmail = c.DisableEmail,
                CustomerApprovesMappings = c.CustomerApprovesMappings,
                EnableCustomerSuspensions = c.EnableCustomerSuspensions,
                WorkBenchCount = c.WorkBenches.Count(),
                SupplierCount = c.CustomerSupplierLinks.Count(),
                TransactionGroupCount = c.TransactionGroups.Count()
            }).AsQueryable();

        return await GridHelper.CreateGridAsync(query, gridParametersModel);
    }
}
```

## Supplier Management System

### Supplier Entity and Relationships

```csharp
[Table("Supplier", Schema = "Customer")]
public class Supplier
{
    [Key]
    public Guid Id { get; set; }
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int ClusteredId { get; set; }
    
    // Core supplier information
    [Required]
    public string Name { get; set; }
    public string Description { get; set; }
    public string SupplierCode { get; set; }
    public string TaxNumber { get; set; }
    public string RegistrationNumber { get; set; }
    
    // Contact information
    public string PrimaryContactName { get; set; }
    public string PrimaryContactEmail { get; set; }
    public string PrimaryContactPhone { get; set; }
    
    // Address information
    public string AddressLine1 { get; set; }
    public string AddressLine2 { get; set; }
    public string City { get; set; }
    public string PostalCode { get; set; }
    public string Country { get; set; }
    
    // Trading configuration
    public string PreferredCurrency { get; set; }
    public string PaymentTerms { get; set; }
    public decimal? CreditLimit { get; set; }
    public bool IsActive { get; set; }
    
    // Integration settings
    public string EDIIdentifier { get; set; }
    public string AS2Identifier { get; set; }
    public string FTPConfiguration { get; set; }
    
    // Relationships
    public ICollection<CustomerSupplierLink> CustomerSupplierLinks { get; set; }
    public ICollection<SupplierAddress> SupplierAddresses { get; set; }
    public ICollection<SupplierContact> SupplierContacts { get; set; }
}
```

### Customer-Supplier Relationship Management

```csharp
[Table("CustomerSupplierLink", Schema = "Customer")]
public class CustomerSupplierLink
{
    [Key]
    public Guid Id { get; set; }
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int ClusteredId { get; set; }
    
    // Relationship identifiers
    [Required]
    public Guid CustomerId { get; set; }
    [Required]
    public Guid SupplierId { get; set; }
    
    // Trading relationship configuration
    public string CustomerSupplierCode { get; set; }
    public string SupplierCustomerCode { get; set; }
    public bool IsActive { get; set; }
    public DateTime? EffectiveDate { get; set; }
    public DateTime? ExpiryDate { get; set; }
    
    // Trading terms
    public string PaymentTerms { get; set; }
    public decimal? CreditLimit { get; set; }
    public string Currency { get; set; }
    public string DeliveryTerms { get; set; }
    
    // Processing configuration
    public bool RequiresPOMatching { get; set; }
    public bool RequiresGRNMatching { get; set; }
    public bool AutoApprovalEnabled { get; set; }
    public decimal? AutoApprovalLimit { get; set; }
    
    // Tolerance settings
    public decimal? PriceTolerance { get; set; }
    public decimal? QuantityTolerance { get; set; }
    public decimal? DateTolerance { get; set; }
    
    // Relationships
    [ForeignKey(nameof(CustomerId))]
    public CustomerDetail Customer { get; set; }
    
    [ForeignKey(nameof(SupplierId))]
    public Supplier Supplier { get; set; }
}

public class CustomerSupplierService
{
    public async Task<FormResponse> CreateCustomerSupplierLinkAsync(CustomerSupplierLinkCreateModel model, Actor createdBy)
    {
        var formResponse = new FormResponse(true);

        // Validation
        if (model.CustomerId == Guid.Empty)
        {
            formResponse.AddError("Customer is required", new FieldIdentifier(nameof(model.CustomerId)));
        }

        if (model.SupplierId == Guid.Empty)
        {
            formResponse.AddError("Supplier is required", new FieldIdentifier(nameof(model.SupplierId)));
        }

        // Check for existing relationship
        var existingLink = await this.context.CustomerSupplierLinks
            .FirstOrDefaultAsync(csl => csl.CustomerId == model.CustomerId && csl.SupplierId == model.SupplierId);

        if (existingLink != null)
        {
            formResponse.AddError("Customer-Supplier relationship already exists", new FieldIdentifier(string.Empty));
        }

        if (!formResponse.Success)
            return formResponse;

        try
        {
            var customerSupplierLink = new CustomerSupplierLink
            {
                Id = Guid.NewGuid(),
                CustomerId = model.CustomerId,
                SupplierId = model.SupplierId,
                CustomerSupplierCode = model.CustomerSupplierCode,
                SupplierCustomerCode = model.SupplierCustomerCode,
                IsActive = model.IsActive,
                EffectiveDate = model.EffectiveDate,
                PaymentTerms = model.PaymentTerms,
                CreditLimit = model.CreditLimit,
                Currency = model.Currency ?? "GBP",
                RequiresPOMatching = model.RequiresPOMatching,
                RequiresGRNMatching = model.RequiresGRNMatching,
                AutoApprovalEnabled = model.AutoApprovalEnabled,
                AutoApprovalLimit = model.AutoApprovalLimit,
                PriceTolerance = model.PriceTolerance ?? 0.05m, // 5% default
                QuantityTolerance = model.QuantityTolerance ?? 0.10m, // 10% default
                DateTolerance = model.DateTolerance ?? 7 // 7 days default
            };

            this.context.CustomerSupplierLinks.Add(customerSupplierLink);

            // Create audit trail
            var parent = new Entity(model.CustomerId, nameof(CustomerDetail));
            var logs = this.context.GetTrackedChangesForCreate(createdBy, parent);

            await this.context.SaveChangesAsync();
            await this.genericAudit.CreateAudit(logs);

            return formResponse;
        }
        catch (Exception ex)
        {
            var errorMessage = ex.InnerException?.Message ?? ex.Message;
            return formResponse.AddError(errorMessage, new FieldIdentifier(string.Empty));
        }
    }
}
```

## Address and Contact Management

### Address Management System

```csharp
[Table("CustomerAddress", Schema = "Customer")]
public class CustomerAddress
{
    [Key]
    public Guid Id { get; set; }
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int ClusteredId { get; set; }
    
    [Required]
    public Guid CustomerId { get; set; }
    
    // Address type and identification
    public EnumAddressType AddressType { get; set; }
    public string AddressCode { get; set; }
    public string Description { get; set; }
    
    // Address details
    [Required]
    public string AddressLine1 { get; set; }
    public string AddressLine2 { get; set; }
    public string AddressLine3 { get; set; }
    [Required]
    public string City { get; set; }
    public string StateProvince { get; set; }
    [Required]
    public string PostalCode { get; set; }
    [Required]
    public string Country { get; set; }
    
    // Address flags
    public bool IsDefault { get; set; }
    public bool IsActive { get; set; }
    public bool IsBillingAddress { get; set; }
    public bool IsShippingAddress { get; set; }
    
    // Relationships
    [ForeignKey(nameof(CustomerId))]
    public CustomerDetail Customer { get; set; }
}

public enum EnumAddressType
{
    Billing,
    Shipping,
    Remittance,
    Corporate,
    Warehouse,
    Other
}
```

### Contact Management System

```csharp
[Table("CustomerContact", Schema = "Customer")]
public class CustomerContact
{
    [Key]
    public Guid Id { get; set; }
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int ClusteredId { get; set; }
    
    [Required]
    public Guid CustomerId { get; set; }
    
    // Contact information
    [Required]
    public string FirstName { get; set; }
    [Required]
    public string LastName { get; set; }
    public string Title { get; set; }
    public string Department { get; set; }
    public string JobTitle { get; set; }
    
    // Communication details
    [Required]
    public string Email { get; set; }
    public string Phone { get; set; }
    public string Mobile { get; set; }
    public string Fax { get; set; }
    
    // Contact preferences
    public bool IsPrimaryContact { get; set; }
    public bool ReceiveInvoiceNotifications { get; set; }
    public bool ReceiveOrderNotifications { get; set; }
    public bool ReceivePaymentNotifications { get; set; }
    public bool ReceiveSystemAlerts { get; set; }
    
    // Contact status
    public bool IsActive { get; set; }
    public DateTime? LastContactDate { get; set; }
    
    // Relationships
    [ForeignKey(nameof(CustomerId))]
    public CustomerDetail Customer { get; set; }
}

public class ContactService
{
    public async Task<List<CustomerContact>> GetCustomerContactsAsync(Guid customerId, EnumContactType? contactType = null)
    {
        var query = this.context.CustomerContacts
            .Where(cc => cc.CustomerId == customerId && cc.IsActive);

        if (contactType.HasValue)
        {
            switch (contactType.Value)
            {
                case EnumContactType.Primary:
                    query = query.Where(cc => cc.IsPrimaryContact);
                    break;
                case EnumContactType.Invoice:
                    query = query.Where(cc => cc.ReceiveInvoiceNotifications);
                    break;
                case EnumContactType.Order:
                    query = query.Where(cc => cc.ReceiveOrderNotifications);
                    break;
            }
        }

        return await query.OrderBy(cc => cc.LastName).ThenBy(cc => cc.FirstName).ToListAsync();
    }

    public async Task SendNotificationToContacts(Guid customerId, EnumNotificationType notificationType, string subject, string message)
    {
        var contacts = await this.GetContactsForNotification(customerId, notificationType);

        foreach (var contact in contacts)
        {
            await this.emailService.SendEmailAsync(contact.Email, subject, message);
            
            // Update last contact date
            contact.LastContactDate = DateTime.UtcNow;
        }

        await this.context.SaveChangesAsync();
    }
}
```

## Customer Configuration and Workbenches

### WorkBench Configuration

```csharp
[Table("WorkBench", Schema = "Customer")]
public class WorkBench
{
    [Key]
    public Guid Id { get; set; }
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int ClusteredId { get; set; }
    
    [Required]
    public Guid CustomerId { get; set; }
    
    // WorkBench identification
    [Required]
    public string Name { get; set; }
    public string Description { get; set; }
    public string WorkBenchCode { get; set; }
    
    // Processing configuration
    public bool IsActive { get; set; }
    public bool AutoProcessing { get; set; }
    public int ProcessingPriority { get; set; }
    
    // Document type configuration
    public string SupportedDocumentTypes { get; set; }
    public string DefaultWorkflowId { get; set; }
    
    // Integration settings
    public string InputDirectory { get; set; }
    public string OutputDirectory { get; set; }
    public string ErrorDirectory { get; set; }
    public string ArchiveDirectory { get; set; }
    
    // Processing rules
    public string ValidationRules { get; set; }
    public string ProcessingRules { get; set; }
    public string ExportRules { get; set; }
    
    // Relationships
    [ForeignKey(nameof(CustomerId))]
    public CustomerDetail Customer { get; set; }
}

public class WorkBenchService
{
    public async Task<WorkBenchModel> GetWorkBenchConfigurationAsync(Guid customerId, string documentType)
    {
        var workBench = await this.context.WorkBenches
            .Where(wb => wb.CustomerId == customerId && wb.IsActive)
            .Where(wb => wb.SupportedDocumentTypes.Contains(documentType))
            .OrderBy(wb => wb.ProcessingPriority)
            .FirstOrDefaultAsync();

        if (workBench == null)
        {
            // Return default configuration
            return this.GetDefaultWorkBenchConfiguration(customerId, documentType);
        }

        return workBench.ToWorkBenchModel();
    }

    public async Task<List<WorkBenchListItemModel>> GetCustomerWorkBenchesAsync(Guid customerId)
    {
        return await this.context.WorkBenches
            .Where(wb => wb.CustomerId == customerId)
            .Select(wb => new WorkBenchListItemModel
            {
                Id = wb.Id,
                Name = wb.Name,
                Description = wb.Description,
                WorkBenchCode = wb.WorkBenchCode,
                IsActive = wb.IsActive,
                AutoProcessing = wb.AutoProcessing,
                ProcessingPriority = wb.ProcessingPriority,
                SupportedDocumentTypes = wb.SupportedDocumentTypes
            })
            .OrderBy(wb => wb.ProcessingPriority)
            .ToListAsync();
    }
}
```

## Customer Validation and Business Rules

### Customer-Specific Validation

```csharp
public class CustomerValidationService
{
    public async Task<ValidationResult> ValidateCustomerConfigurationAsync(Guid customerId)
    {
        var validationResult = new ValidationResult();

        // 1. Validate customer exists and is active
        var customer = await this.context.CustomerDetails
            .FirstOrDefaultAsync(c => c.Id == customerId);

        if (customer == null)
        {
            validationResult.AddError("Customer not found");
            return validationResult;
        }

        // 2. Validate required configuration
        await this.ValidateRequiredConfiguration(customer, validationResult);

        // 3. Validate supplier relationships
        await this.ValidateSupplierRelationships(customerId, validationResult);

        // 4. Validate workbench configuration
        await this.ValidateWorkBenchConfiguration(customerId, validationResult);

        // 5. Validate integration settings
        await this.ValidateIntegrationSettings(customer, validationResult);

        return validationResult;
    }

    private async Task ValidateSupplierRelationships(Guid customerId, ValidationResult validationResult)
    {
        var supplierLinks = await this.context.CustomerSupplierLinks
            .Where(csl => csl.CustomerId == customerId && csl.IsActive)
            .ToListAsync();

        if (!supplierLinks.Any())
        {
            validationResult.AddWarning("No active supplier relationships configured");
        }

        foreach (var link in supplierLinks)
        {
            // Validate tolerance settings
            if (link.PriceTolerance < 0 || link.PriceTolerance > 1)
            {
                validationResult.AddError($"Invalid price tolerance for supplier {link.SupplierId}");
            }

            if (link.QuantityTolerance < 0 || link.QuantityTolerance > 1)
            {
                validationResult.AddError($"Invalid quantity tolerance for supplier {link.SupplierId}");
            }

            // Validate credit limits
            if (link.CreditLimit.HasValue && link.CreditLimit <= 0)
            {
                validationResult.AddError($"Invalid credit limit for supplier {link.SupplierId}");
            }
        }
    }
}
```

## Multi-Tenant Customer Management

### Tenant Isolation Implementation

```csharp
public class TenantAwareCustomerService
{
    public async Task<List<CustomerListItemModel>> GetTenantCustomersAsync(Guid tenantId, string searchTerm = null)
    {
        var query = this.context.CustomerDetails
            .Where(c => c.TenantId == tenantId);

        if (!string.IsNullOrEmpty(searchTerm))
        {
            query = query.Where(c => c.Description.Contains(searchTerm) || 
                                   c.ExternalSystemId.Contains(searchTerm));
        }

        return await query
            .Select(c => new CustomerListItemModel
            {
                Id = c.Id,
                Description = c.Description,
                DeploymentColor = c.DeploymentColor,
                DisableEmail = c.DisableEmail,
                ExternalSystemId = c.ExternalSystemId,
                IsActive = true // Derived from business logic
            })
            .OrderBy(c => c.Description)
            .ToListAsync();
    }

    public async Task<bool> ValidateCustomerAccessAsync(Guid customerId, Guid tenantId)
    {
        return await this.context.CustomerDetails
            .AnyAsync(c => c.Id == customerId && c.TenantId == tenantId);
    }
}
```

## Related Documents

- **[DataModelsAndEntities.md](./DataModelsAndEntities.md)** - Core business entities and relationships
- **[WorkflowEngine.md](./WorkflowEngine.md)** - Approval workflows and business rules
- **[TransactionProcessing.md](./TransactionProcessing.md)** - Transaction processing and validation
- **[SecurityArchitecture.md](./SecurityArchitecture.md)** - Multi-tenant security and access control
- **[SystemOverview.md](./SystemOverview.md)** - High-level system architecture

---

*This document provides comprehensive coverage of the eHub customer and supplier management system. Refer to the related documentation for detailed implementation guidance on specific components.*
