# eHub Workflow Engine

## Purpose & Scope

This document provides comprehensive documentation of the eHub workflow engine, including approval workflows, business rules, process orchestration, and workflow configuration. It serves as the definitive guide for understanding and implementing workflow automation in the eHub system.

## Prerequisites

- Understanding of business process automation and workflow concepts
- Knowledge of approval hierarchies and business rules
- Familiarity with eHub transaction processing and state management
- Basic understanding of rule engines and process orchestration

## Core Concepts

### Workflow Engine Philosophy

The eHub workflow engine is built on several key principles:

1. **Configurable Workflows**: Customer-specific workflow configuration and customisation
2. **Rule-Based Processing**: Dynamic business rules with custom logic execution
3. **Approval Hierarchies**: Flexible approval workflows with escalation and delegation
4. **Process Orchestration**: Automated coordination of business processes
5. **Audit and Compliance**: Complete workflow audit trails for regulatory requirements

### Workflow Engine Overview

```
eHub Workflow Engine Architecture
├── Workflow Configuration
│   ├── Workflow Definition - Process structure and rules
│   ├── Workflow Fields - Dynamic field configuration
│   ├── Workflow Rules - Business logic and validation
│   └── Workflow States - Process state management
├── Approval Engine
│   ├── Approval Hierarchies - Multi-level approval chains
│   ├── Approval Thresholds - Amount-based approval routing
│   ├── Escalation Logic - Time-based escalation rules
│   └── Delegation Management - Approval delegation capabilities
├── Rule Engine
│   ├── Business Rules - Custom business logic execution
│   ├── Validation Rules - Data validation and compliance
│   ├── Field Rules - Dynamic field behaviour
│   └── Process Rules - Workflow orchestration logic
└── Process Orchestration
    ├── State Management - Workflow state transitions
    ├── Event Handling - Process event management
    ├── Integration Points - External system integration
    └── Error Handling - Exception management and recovery
```

## Workflow Configuration and Management

### Workflow Definition Structure

```csharp
[Table("Workflow", Schema = "Customer")]
public class Workflow
{
    public Guid Id { get; set; }
    public int ClusteredId { get; set; }
    public Guid? CustomerId { get; set; }
    public string Name { get; set; }
    public Guid? ParentWorkflowId { get; set; }
    public EnumProcessingEngine ProcessingEngine { get; set; }
    public bool Lock { get; set; }
    
    // Relationships
    [ForeignKey(nameof(CustomerId))]
    public CustomerDetail Customer { get; set; }
    
    [ForeignKey(nameof(ParentWorkflowId))]
    public Workflow ParentWorkflow { get; set; }
    
    public ICollection<WorkflowField> WorkflowFields { get; set; }
    public ICollection<WorkflowRule> WorkflowRules { get; set; }
}

public enum EnumProcessingEngine
{
    StandardProcessing,
    CustomProcessing,
    ExternalProcessing,
    HybridProcessing
}
```

### Workflow Processing Implementation

```csharp
public class WorkflowProcessor
{
    public GenericResponse<Workflow> DetermineFromConfigFileThenDatabase(string correlationId, ProgramModel programModel, TransactionCouple transactionCouple)
    {
        try
        {
            // 1. Find configuration and base rule files
            programModel.DirectoryStructure = new RuleFileProcessor(this.directory)
                .SetDefaultFileName(programModel.DirectoryStructure);
            
            MasterLogging.Trace(correlationId, $"WorkflowFile name: {programModel.DirectoryStructure.WorkflowFileName}");

            // 2. Process workflow configuration
            var workflowResult = this.ProcessWorkflow(programModel);
            if (!workflowResult.Success)
                return workflowResult;

            // 3. Set workflow properties
            programModel.Workflow = workflowResult.Data;
            programModel.DirectoryStructure.PrimaryRuleFile = workflowResult.Data.Name;
            programModel.Document.Chain = workflowResult.Data.Name;
            programModel.Document.WorkflowId = workflowResult.Data.Id;

            MasterLogging.Trace(correlationId, $"Workflow Set: {programModel.Workflow.Name} {programModel.Workflow.Id}");

            // 4. Update document with workflow information
            this.contexts.TransactionContext.DetachAllEntities();
            this.DocumentUpdate(programModel);
            this.contexts.TransactionContext.SaveChangesAndClearTracking();

            // 5. Get workflow fields
            this.GetWorkflowFields(programModel);

            return workflowResult;
        }
        catch (Exception ex)
        {
            return new GenericResponse<Workflow>(ex);
        }
    }

    public GenericResponse<Workflow> ProcessWorkflow(ProgramModel programModel)
    {
        var correlationId = programModel.Document.CorrelationId;

        // Check if database-driven workflow exists
        var useDatabase = this.contexts.DataContext.Identifications
            .Any(i => i.CustomerId == programModel.CustomerId && 
                     i.AzureRuleFolder == programModel.DirectoryStructure.AzureRulesContainer);

        if (!useDatabase)
        {
            MasterLogging.Trace(correlationId, "Processing workflow from rule file");
            return this.ProcessFromRules(programModel);
        }

        MasterLogging.Trace(correlationId, "Processing workflow from database");
        return this.WorkflowFromDatabase(programModel);
    }
}
```

### Workflow Field Management

```csharp
public class WorkflowFieldGenerator
{
    public static List<WorkflowField> CreateWorkflowFields(
        TransactionLoggingDetails transactionLoggingDetails,
        Guid workflowId,
        List<StateModel<WorkflowField>> workflowFieldStateModels,
        List<StateModel<WorkflowField>> workflowFieldSetStateModels,
        List<TransactionData> transactionData,
        int pageNumber)
    {
        var workflowFields = new List<WorkflowField>();

        // Process workflow field state models
        foreach (var stateModel in workflowFieldStateModels)
        {
            var workflowField = CreateWorkflowFieldFromState(stateModel, workflowId, pageNumber);
            if (workflowField != null)
            {
                workflowFields.Add(workflowField);
            }
        }

        // Process workflow field set state models
        foreach (var setStateModel in workflowFieldSetStateModels)
        {
            var workflowField = CreateWorkflowFieldFromSetState(setStateModel, workflowId, pageNumber);
            if (workflowField != null)
            {
                workflowFields.Add(workflowField);
            }
        }

        return workflowFields;
    }

    private static WorkflowField CreateWorkflowFieldFromState(StateModel<WorkflowField> stateModel, Guid workflowId, int pageNumber)
    {
        return new WorkflowField
        {
            Id = Guid.NewGuid(),
            WorkflowId = workflowId,
            FieldName = stateModel.Data.FieldName,
            FieldValue = stateModel.Data.FieldValue,
            PageNumber = pageNumber,
            State = stateModel.State,
            CreatedDate = DateTime.UtcNow
        };
    }
}

public static class WorkflowFieldStateManager
{
    public static StateModel<WorkflowField> DetermineWorkflowFieldState(WorkflowField workflowField, ref List<StateModel<WorkflowField>> workflowFields)
    {
        var existingWorkflowField = GetExistingWorkflow(workflowField, workflowFields);

        if (existingWorkflowField == null)
        {
            // New workflow field
            var newStateModel = new StateModel<WorkflowField>
            {
                Data = workflowField,
                State = EnumState.Create
            };
            workflowFields.Add(newStateModel);
            return newStateModel;
        }

        // Check if field has changed
        if (HasWorkflowFieldChanged(existingWorkflowField.Data, workflowField))
        {
            existingWorkflowField.Data.FieldValue = workflowField.FieldValue;
            existingWorkflowField.State = EnumState.Update;
        }

        return existingWorkflowField;
    }

    private static bool HasWorkflowFieldChanged(WorkflowField existing, WorkflowField current)
    {
        return existing.FieldValue != current.FieldValue ||
               existing.FieldName != current.FieldName;
    }
}
```

## Approval Workflow Engine

### Approval Hierarchy Configuration

```csharp
public class ApprovalWorkflowEngine
{
    public async Task<WorkflowResult> InitiateApprovalWorkflowAsync(TransactionHeader transaction)
    {
        // 1. Get customer-specific workflow configuration
        var workflow = await GetWorkflowConfigurationAsync(transaction.CustomerId, transaction.DocumentType);
        
        // 2. Determine approval requirements based on transaction
        var approvalRequirements = await DetermineApprovalRequirementsAsync(transaction, workflow);
        
        // 3. Create approval tasks for each required approver
        var approvalTasks = await CreateApprovalTasksAsync(transaction, approvalRequirements);
        
        // 4. Initiate first approval step
        var firstStep = approvalTasks.OrderBy(t => t.Sequence).First();
        await InitiateApprovalStepAsync(firstStep);
        
        return WorkflowResult.Success(approvalTasks.Select(t => t.Id).ToList());
    }

    private async Task<List<ApprovalRequirement>> DetermineApprovalRequirementsAsync(TransactionHeader transaction, WorkflowConfiguration workflow)
    {
        var requirements = new List<ApprovalRequirement>();

        // Amount-based approval thresholds
        foreach (var threshold in workflow.ApprovalThresholds.OrderBy(t => t.Amount))
        {
            if (transaction.Total >= threshold.Amount)
            {
                requirements.Add(new ApprovalRequirement
                {
                    ApproverRole = threshold.RequiredRole,
                    ApproverLevel = threshold.Level,
                    Sequence = threshold.Sequence,
                    IsRequired = true,
                    TimeoutHours = threshold.TimeoutHours
                });
            }
        }

        // Document type specific requirements
        var documentRequirements = await GetDocumentTypeRequirementsAsync(transaction.DocumentType, transaction.CustomerId);
        requirements.AddRange(documentRequirements);

        // Supplier-specific requirements
        if (transaction.SupplierId.HasValue)
        {
            var supplierRequirements = await GetSupplierRequirementsAsync(transaction.SupplierId.Value);
            requirements.AddRange(supplierRequirements);
        }

        return requirements.OrderBy(r => r.Sequence).ToList();
    }
}
```

### Approval Processing Implementation

```csharp
public class ApprovalController : ControllerBase
{
    [HttpPost("{transactionHeaderId:Guid}/approve")]
    public async Task<IActionResult> Approve(
        [FromRoute] Guid memberId,
        [FromRoute] Guid transactionHeaderId,
        [FromHeader(Name = "ehub-user-id")] Guid userId,
        [FromHeader(Name = "ehub-user-full-name")] string userFullName,
        [FromHeader(Name = "ehub-user-member-id")] Guid userMemberId,
        [FromHeader(Name = "ehub-user-member-name")] string memberName)
    {
        // 1. Validate current transaction status
        var status = this.contexts.TransactionContext.Transactions
            .Where(t => t.TransactionHeaderId == transactionHeaderId)
            .AsNoTracking()
            .Select(t => t.Status)
            .First();

        if (status == EnumTransactionStatus.Reprocess)
        {
            return this.Problem(
                "Unable to approve whilst reprocessing",
                title: "Failed",
                statusCode: StatusCodes.Status409Conflict);
        }

        // 2. Validate actor permissions
        var actorResult = ValidateActor.Validate(userId, userFullName, userMemberId, memberName);
        if (!actorResult.Success)
        {
            return this.BadRequest(actorResult.Error);
        }

        // 3. Log approval action
        this.auditLogRepository.LogApprove(
            new Entity(transactionHeaderId, nameof(TransactionHeader)),
            actorResult.Data);

        // 4. Get existing transaction data
        var existingTransactionData = this.contexts.TransactionContext
            .GetTransactionModelWithData(t => t.TransactionHeaderId == transactionHeaderId);

        // 5. Update transaction status
        var transaction = this.contexts.TransactionContext.Transactions
            .Where(t => t.TransactionHeaderId == transactionHeaderId)
            .FirstOrDefault();

        var transactionHeader = this.contexts.TransactionContext.TransactionHeaders
            .Where(t => t.Id == transactionHeaderId)
            .FirstOrDefault();

        // 6. Set approval status and continue processing
        transaction.Status = EnumTransactionStatus.ActionPending;
        transactionHeader.ApprovalDate = DateTime.UtcNow;

        // 7. Save changes and continue workflow
        this.contexts.TransactionContext.SaveChanges();

        // 8. Continue transaction processing
        await this.ContinueTransactionProcessing(transactionHeaderId);

        return this.Ok();
    }

    private async Task ContinueTransactionProcessing(Guid transactionHeaderId)
    {
        // Queue transaction for continued processing
        var backgroundJob = new ProcessTransactionJob
        {
            TransactionHeaderId = transactionHeaderId,
            ProcessingType = ProcessingType.ContinueFromApproval
        };

        await this.backgroundJobService.EnqueueAsync(backgroundJob);
    }
}
```

## Business Rules Engine

### Rule Definition and Execution

```csharp
public class WorkflowRuleCapture
{
    private ProgramRunner programRunner;

    public void ProcessRules(DataModel dataModel, List<WorkflowRule> rules)
    {
        var convertPrologToJson = new ProgramSyntaxParser();

        // Convert Prolog rules to executable commands
        var commands = rules.SelectMany(r => convertPrologToJson.ProcessProlog(r.Rule).Commands).ToList();

        var executionResult = new ExecutionResult();
        var documentResults = this.programRunner.ExecuteItemsInList(dataModel, commands, new List<IVariable>());
        
        // Process execution results
        foreach (var result in documentResults)
        {
            if (!result.Success)
            {
                Logger.Error("WorkflowRules", $"Rule execution failed: {result.ErrorMessage}");
            }
        }
    }
}

public class RuleEngine
{
    public List<IExecutionResult> Process(DataModel dataModel, TransactionModel transactionModel)
    {
        var executionResult = new ExecutionResult();
        
        // Execute transaction-specific rules
        var documentResults = this.programRunner.ExecuteCustomPlan(dataModel, ListOfCommands.TransactionFunctions);
        executionResult.AddChildren(documentResults);
        dataModel.ProgramModel.CurrentTransaction.ExecutionResults.AddRange(documentResults);

        // Create workflow fields based on rule execution
        this.CreateWorkflowFields(dataModel, transactionModel);

        return documentResults;
    }

    private void CreateWorkflowFields(DataModel dataModel, TransactionModel transactionModel)
    {
        // Generate fields from transaction data
        transactionModel.TransactionData = new FieldGenerator(this.context)
            .ProcessFields(dataModel.ProgramModel, transactionModel.TransactionData);

        // Create workflow-specific fields
        var results = WorkflowFieldGenerator.CreateWorkflowFields(
            transactionModel.TransactionLoggingDetails,
            dataModel.ProgramModel.Workflow.Id,
            dataModel.ProgramModel.WorkflowFieldStateModels,
            dataModel.ProgramModel.WorkflowFieldSetStateModels,
            transactionModel.TransactionData,
            transactionModel.PageNumberFirst);

        // Process adhoc line items
        var adhocLines = transactionModel.TransactionData
            .Where(td => !td.Variable.CapturedDuringLineProcessing && 
                        td.LineType == TransactionData.EnumLineType.Line)
            .ToArray();

        foreach (var adhocLine in adhocLines)
        {
            // Process individual line item rules
            this.ProcessLineItemRules(dataModel, adhocLine);
        }
    }
}
```

### Custom Business Logic Execution

```csharp
public class CustomCodeChecker
{
    private readonly IExecutionFactory executionFactory;

    public CustomCodeChecker(IExecutionFactory executionFactory)
    {
        this.executionFactory = executionFactory;
    }

    public ExecutionResult ExecuteCustomCode(string customCode, DataModel dataModel)
    {
        try
        {
            // Parse and compile custom code
            var compiledCode = this.CompileCustomCode(customCode);
            
            // Create execution context
            var executionContext = new ExecutionContext
            {
                DataModel = dataModel,
                Variables = dataModel.ProgramModel.CurrentTransaction.TransactionData,
                Logger = Logger.Instance
            };

            // Execute custom logic
            var result = compiledCode.Execute(executionContext);

            return new ExecutionResult
            {
                Success = result.Success,
                Message = result.Message,
                Data = result.Data
            };
        }
        catch (Exception ex)
        {
            return new ExecutionResult
            {
                Success = false,
                Message = $"Custom code execution failed: {ex.Message}",
                Exception = ex
            };
        }
    }

    private ICompiledCode CompileCustomCode(string customCode)
    {
        // Compile custom business logic
        var compiler = this.executionFactory.CreateCompiler();
        return compiler.Compile(customCode);
    }
}

public class ServerValidation
{
    private readonly CustomCodeChecker customCodeChecker;

    public ValidationResult ValidateWithCustomLogic(TransactionModel transaction, ValidationRule rule)
    {
        if (string.IsNullOrEmpty(rule.CustomValidationCode))
        {
            return ValidationResult.Success();
        }

        var dataModel = new DataModel
        {
            ProgramModel = new ProgramModel
            {
                CurrentTransaction = transaction
            }
        };

        var executionResult = this.customCodeChecker.ExecuteCustomCode(rule.CustomValidationCode, dataModel);

        if (!executionResult.Success)
        {
            return ValidationResult.Failure(rule.ErrorMessage ?? executionResult.Message);
        }

        // Check if custom validation passed
        var validationPassed = executionResult.Data as bool? ?? true;
        
        return validationPassed 
            ? ValidationResult.Success() 
            : ValidationResult.Failure(rule.ErrorMessage);
    }
}
```

## Workflow Service Management

### Workflow CRUD Operations

```csharp
public class WorkflowService
{
    public async Task<FormResponse> CreateWorkflowAsync(WorkflowCreateModel workflowCreateModel, Actor updatedBy)
    {
        try
        {
            var workflow = workflowCreateModel.ToWorkflow();

            this.context.Workflows.Add(workflow);

            var parent = new Entity(workflow.CustomerId.Value, nameof(CustomerDetail));
            var logs = this.context.GetTrackedChangesForCreate(updatedBy, parent);

            await this.context.SaveChangesAsync();
            await this.genericAudit.CreateAudit(logs).ConfigureAwait(false);

            return new FormResponse();
        }
        catch (Exception ex)
        {
            var errorMessage = ex.InnerException?.Message ?? ex.Message;
            return new FormResponse().AddError(errorMessage, new FieldIdentifier(string.Empty));
        }
    }

    public async Task<FormResponse> UpdateWorkflowAsync(WorkflowModel workflowModel, Actor updatedBy)
    {
        try
        {
            var workflow = await this.context.Workflows
                .FirstOrDefaultAsync(w => w.Id == workflowModel.Id);

            workflow.CustomerId = workflowModel.CustomerId;
            workflow.Name = workflowModel.Name;
            workflow.ParentWorkflowId = workflowModel.ParentWorkflowId;
            workflow.ProcessingEngine = workflowModel.ProcessingEngine;
            workflow.Lock = workflowModel.Lock;

            var parent = new Entity(workflow.CustomerId.Value, nameof(CustomerDetail));
            var logs = this.context.GetTrackedChangesForUpdate(updatedBy, parent);

            await this.context.SaveChangesAsync();
            await this.genericAudit.UpdateAudit(logs).ConfigureAwait(false);

            return new FormResponse();
        }
        catch (Exception ex)
        {
            var errorMessage = ex.InnerException?.Message ?? ex.Message;
            return new FormResponse().AddError(errorMessage, new FieldIdentifier(string.Empty));
        }
    }

    public async Task<GridResultModel<WorkflowListItemModel>> ListWorkflowsAsync(Guid customerId, GridParametersModel gridParametersModel)
    {
        var query = this.context.Workflows
            .Where(w => w.CustomerId == customerId)
            .Select(self => new WorkflowListItemModel
            {
                Id = self.Id,
                CustomerId = self.CustomerId.Value,
                Name = self.Name,
                ParentWorkflowName = self.ParentWorkflow.Name,
                ProcessingEngineDescription = self.ProcessingEngine.GetEnumDescription(),
                Lock = self.Lock,
            }).AsQueryable();

        return await GridHelper.CreateGridAsync(query, gridParametersModel);
    }
}
```

## Process Orchestration

### Workflow State Transitions

```mermaid
stateDiagram-v2
    [*] --> WorkflowInitiated
    WorkflowInitiated --> RulesEvaluation : Start Processing
    RulesEvaluation --> FieldGeneration : Rules Passed
    RulesEvaluation --> ValidationFailed : Rules Failed
    FieldGeneration --> ApprovalRequired : Fields Generated
    FieldGeneration --> AutoApproved : Auto Approval
    ApprovalRequired --> PendingApproval : Approval Needed
    PendingApproval --> Approved : Approval Granted
    PendingApproval --> Rejected : Approval Denied
    PendingApproval --> Escalated : Timeout Reached
    Escalated --> PendingApproval : Escalation Complete
    Approved --> WorkflowComplete : Process Complete
    AutoApproved --> WorkflowComplete : Process Complete
    ValidationFailed --> ManualIntervention : Requires Attention
    ManualIntervention --> RulesEvaluation : Retry Processing
    Rejected --> WorkflowComplete : Process Terminated
    WorkflowComplete --> [*]
```

### Event-Driven Workflow Processing

```csharp
public class WorkflowEventHandler
{
    public async Task HandleWorkflowEvent(WorkflowEvent workflowEvent)
    {
        switch (workflowEvent.EventType)
        {
            case WorkflowEventType.TransactionCreated:
                await this.HandleTransactionCreated(workflowEvent);
                break;
                
            case WorkflowEventType.ValidationCompleted:
                await this.HandleValidationCompleted(workflowEvent);
                break;
                
            case WorkflowEventType.ApprovalRequired:
                await this.HandleApprovalRequired(workflowEvent);
                break;
                
            case WorkflowEventType.ApprovalCompleted:
                await this.HandleApprovalCompleted(workflowEvent);
                break;
                
            case WorkflowEventType.WorkflowCompleted:
                await this.HandleWorkflowCompleted(workflowEvent);
                break;
                
            default:
                Logger.Warning("WorkflowEvents", $"Unhandled workflow event type: {workflowEvent.EventType}");
                break;
        }
    }

    private async Task HandleApprovalRequired(WorkflowEvent workflowEvent)
    {
        var transactionId = workflowEvent.TransactionId;
        var approvalRequirements = await this.GetApprovalRequirements(transactionId);

        foreach (var requirement in approvalRequirements)
        {
            await this.CreateApprovalTask(transactionId, requirement);
            await this.SendApprovalNotification(transactionId, requirement);
        }

        // Set transaction status to approval pending
        await this.UpdateTransactionStatus(transactionId, EnumTransactionStatus.ApprovalPending);
    }
}
```

## Related Documents

- **[TransactionProcessing.md](./TransactionProcessing.md)** - Transaction processing and state management
- **[BusinessLogicArchitecture.md](./BusinessLogicArchitecture.md)** - Business logic patterns and architecture
- **[CustomerAndSupplierManagement.md](./CustomerAndSupplierManagement.md)** - Customer configuration and management
- **[AuditingAndLogging.md](./AuditingAndLogging.md)** - Audit trails and compliance logging
- **[SystemOverview.md](./SystemOverview.md)** - High-level system architecture

---

*This document provides comprehensive coverage of the eHub workflow engine. Refer to the related documentation for detailed implementation guidance on specific components.*
