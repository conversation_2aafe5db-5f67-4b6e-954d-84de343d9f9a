# eHub Transaction Processing

## Purpose & Scope

This document provides comprehensive documentation of the eHub transaction processing system, including invoice processing workflows, three-way matching algorithms, validation rules, and transaction lifecycle management. It serves as the definitive guide for understanding and working with the eHub transaction processing engine.

## Prerequisites

- Understanding of B2B trading processes and document types
- Knowledge of three-way matching concepts (Purchase Orders, Invoices, Goods Receipt Notes)
- Familiarity with business validation rules and approval workflows
- Basic understanding of eHub data models and state management

## Core Concepts

### Transaction Processing Philosophy

The eHub transaction processing system is built on several key principles:

1. **State-Driven Processing**: Comprehensive state machine managing transaction lifecycle
2. **Three-Way Matching**: Automated reconciliation of Purchase Orders, Invoices, and Goods Receipt Notes
3. **Business Rule Validation**: Configurable validation rules for compliance and accuracy
4. **Exception Handling**: Robust error handling with manual intervention capabilities
5. **Audit Trail**: Complete audit logging for regulatory compliance and troubleshooting

### Transaction Processing Overview

```
eHub Transaction Processing Architecture
├── Transaction State Machine
│   ├── Document Ingestion - Initial document capture and processing
│   ├── Extraction & Validation - Data extraction and business rule validation
│   ├── Matching & Reconciliation - Three-way matching and variance analysis
│   └── Approval & Export - Workflow approval and system export
├── Matching Processors
│   ├── Two-Way Matching - Invoice to PO or Invoice to GRN
│   ├── Three-Way Matching - PO, Invoice, and GRN reconciliation
│   ├── Statement Matching - Bank statement reconciliation
│   └── Manual Matching - User-driven matching for exceptions
├── Validation Engine
│   ├── Field Validation - Data type and format validation
│   ├── Business Rules - Customer-specific validation logic
│   ├── Workflow Rules - Process-specific validation
│   └── Compliance Checks - Regulatory requirement validation
└── Status Management
    ├── Transaction States - Comprehensive status tracking
    ├── Error Handling - Exception management and recovery
    ├── Manual Intervention - User override capabilities
    └── Audit Logging - Complete change tracking
```

## Transaction State Machine

### Comprehensive State Management

```csharp
public enum EnumTransactionStatus
{
    Created,
    FailedExtraction,
    ReadyForProcessing,
    FailedToDetermineWorkflow,
    ValidationPending,
    SuspenseRequeued,
    Suspense,
    ValidationSuccess,
    MatchPending,
    MatchRejected,
    MatchComplete,
    ApprovalPending,
    ExportOnHold,
    ActionPending,
    ExportPending,
    SentForEmail,
    Completed,
    Reprocess
}
```

### State Machine Implementation

```csharp
public class TransactionStateMachine
{
    private Dictionary<EnumTransactionStatus, Func<DataModel, TransactionCouple, bool>> StateDictionary => 
        new Dictionary<EnumTransactionStatus, Func<DataModel, TransactionCouple, bool>>
        {
            { EnumTransactionStatus.Created, this.stageProcessing.Value.ProcessTransaction },
            { EnumTransactionStatus.FailedExtraction, this.stageProcessing.Value.ProcessTransaction },
            { EnumTransactionStatus.ReadyForProcessing, this.stageProcessing.Value.ProcessTransaction },
            { EnumTransactionStatus.FailedToDetermineWorkflow, this.stageProcessing.Value.ProcessTransaction },
            { EnumTransactionStatus.ValidationPending, this.stageValidation.Value.Validate },
            { EnumTransactionStatus.SuspenseRequeued, this.stageValidation.Value.Validate },
            { EnumTransactionStatus.Suspense, this.stageValidation.Value.Validate },
            { EnumTransactionStatus.ValidationSuccess, this.stageValidationSuccessfull.Value.Update },
            { EnumTransactionStatus.MatchPending, this.stageMatchPending.Value.TryCheckMatch },
            { EnumTransactionStatus.MatchRejected, this.stageMatchPending.Value.TryCheckMatch },
            { EnumTransactionStatus.MatchComplete, this.stageMatchSuccessfull.Value.Update },
            { EnumTransactionStatus.ApprovalPending, this.stageApproval.Value.TryCheckApproval },
            { EnumTransactionStatus.ExportOnHold, this.stageHold.Value.TryCheckHold },
            { EnumTransactionStatus.ActionPending, this.stageFinalAction.Value.TryComplete },
            { EnumTransactionStatus.ExportPending, this.stageFinalAction.Value.TryComplete },
            { EnumTransactionStatus.SentForEmail, this.stageFinalAction.Value.TryComplete },
            { EnumTransactionStatus.Completed, this.stageCompleted.Value.TryComplete },
        };

    public void ProcessTransaction(DataModel data, TransactionCouple transactionCouple, bool reprocessing, EnumTransactionStatus? stopAtStatus = null)
    {
        var transactionModel = transactionCouple.TransactionModel;
        this.SetStages(reprocessing);

        var counter = 0;
        var keepProcessing = true;
        EnumTransactionStatus? lastStatus = null;
        
        while (keepProcessing)
        {
            if (!this.StateDictionary.ContainsKey(transactionModel.Status))
                break;

            if (stopAtStatus.HasValue && transactionModel.Status == stopAtStatus.Value)
                break;

            var thisStatus = transactionModel.Status;

            if (lastStatus != null && lastStatus == thisStatus)
            {
                throw new Exception($"Status {lastStatus} has not changed causing the StateMachine to attempt to run the previous stage again");
            }

            lastStatus = thisStatus;

            try
            {
                var stateProcessor = this.StateDictionary[thisStatus];
                keepProcessing = stateProcessor.Invoke(data, transactionCouple);
                keepProcessing = keepProcessing && thisStatus != transactionModel.Status;
            }
            catch (Exception ex)
            {
                // Handle state processing errors
                transactionModel.Status = EnumTransactionStatus.FailedExtraction;
                transactionModel.Reason = ex.Message;
                break;
            }

            counter++;
            if (counter > 20) // Prevent infinite loops
            {
                throw new Exception("Transaction processing exceeded maximum iterations");
            }
        }
    }
}
```

### Transaction Processing Flow

```mermaid
stateDiagram-v2
    [*] --> Created
    Created --> ReadyForProcessing : Document Extracted
    Created --> FailedExtraction : Extraction Failed
    FailedExtraction --> ReadyForProcessing : Retry Successful
    ReadyForProcessing --> ValidationPending : Workflow Determined
    ReadyForProcessing --> FailedToDetermineWorkflow : Workflow Failed
    ValidationPending --> ValidationSuccess : Validation Passed
    ValidationPending --> Suspense : Validation Failed
    Suspense --> SuspenseRequeued : Manual Intervention
    SuspenseRequeued --> ValidationPending : Revalidation
    ValidationSuccess --> MatchPending : Matching Required
    ValidationSuccess --> ApprovalPending : No Matching Required
    MatchPending --> MatchComplete : Match Successful
    MatchPending --> MatchRejected : Match Failed
    MatchRejected --> MatchPending : Manual Match
    MatchComplete --> ApprovalPending : Approval Required
    ApprovalPending --> ActionPending : Approved
    ApprovalPending --> ExportOnHold : On Hold
    ExportOnHold --> ActionPending : Released
    ActionPending --> ExportPending : Export Ready
    ExportPending --> SentForEmail : Email Sent
    SentForEmail --> Completed : Process Complete
    Completed --> [*]
    
    note right of Suspense : Manual intervention required
    note right of MatchRejected : Variance exceeds tolerance
    note right of ExportOnHold : Approval workflow hold
```

## Three-Way Matching System

### Matching Types and Strategies

```csharp
public enum EnumMatchType : int
{
    InvoiceMatch = 0,              // Invoice to invoice matching
    OrderAcknowledgementMatch = 1, // Order acknowledgment processing
    StatementMatch = 2,            // Bank statement reconciliation
    InvoiceToGRN = 3,             // Invoice to Goods Receipt Note
    InvoiceToGRNChild = 4,        // Child line item matching
    Quote = 5                      // Quote processing
}

public enum EnumInvoiceMatchMode
{
    TwoWayMatching,    // Invoice to PO or Invoice to GRN
    ThreeWay           // PO, Invoice, and GRN reconciliation
}
```

### Three-Way Matching Process

```csharp
public class InvoiceOrderGRNThreeWayMatch
{
    public PurchaseOrderMatchModel TryInvoiceMatch(TransactionCouple transactionCouple, Matching matching, MatchLinking matchLinking)
    {
        // 1. Build match model with all related documents
        var matchModel = this.BuildMatchModel(transactionCouple, matching, matchLinking);
        
        // 2. Get matching configuration fields
        var fields = this.contexts.DataContext.MatchingFields.GetMatchingFields(
            EnumMatchType.InvoiceMatch, 
            matching.CustomerId, 
            matching.SupplierId, 
            matching.TransactionGroupId);
            
        var results = MatchingHelpers.GetFieldData(fields);
        matchModel.HeaderFieldsToMatch = results.HeaderFieldsToMatch;
        matchModel.LineFieldsToMatch = results.LineFieldsToMacth;

        // 3. Perform the three-way match
        new StandardMatchProcessor(new GRNProcessor().GRNMatch)
            .PerformMatches(matchModel, transactionCouple, matching);

        // 4. Normalise and save results
        matchModel.Invoices.ReverseNormiliseHeaderToTransaction();
        this.SaveData(matchModel);

        return matchModel;
    }
}
```

### Matching Algorithm Implementation

```csharp
public class StandardMatchProcessor
{
    public void PerformMatches(PurchaseOrderMatchModel matchModel, TransactionCouple transactionCouple, Matching matching)
    {
        var headerMatchingConfiguration = new HeaderMatchingConfiguration(matchModel.HeaderFieldsToMatch);
        var lineMatchingConfiguration = new LineMatchingConfiguration(matchModel.LineFieldsToMatch);

        // Create transaction match data for the match
        TransactionMatchDataCalculator.Accumulate(matchModel);

        var transactionMatchResult = new TransactionMatchResult(EnumTransactionStatus.MatchPending);
        var pendingTransactions = MatchBaseProcessor.GetPendingInvoiceTransactions(matchModel);

        var totalToMatch = matchModel.PurchaseOrders.Sum(po => po.TransactionHeader.Total ?? 0);
        var balance = totalToMatch;

        var rejected = false;
        string message = null;
        
        foreach (var transaction in pendingTransactions)
        {
            if (rejected)
            {
                transaction.TransactionHeader.TransactionStatus = EnumTransactionStatus.MatchRejected;
                transaction.TransactionHeader.Reason = "Previous transactions are rejected.";
                continue;
            }

            // Perform individual transaction matching
            var matchResult = this.MatchTransaction(
                matchModel,
                headerMatchingConfiguration,
                lineMatchingConfiguration,
                totalToMatch,
                ref balance,
                transaction);

            transactionMatchResult.AbsorbResult(matchResult);
            this.SetTransactionStatus(transaction, transactionMatchResult);

            rejected = rejected || transactionMatchResult.Status == EnumTransactionStatus.MatchRejected;
            if (rejected && string.IsNullOrEmpty(message))
            {
                message = transactionMatchResult.Message;
            }
        }

        // Apply final status to all transactions
        this.ApplyFinalMatchStatus(matchModel, transactionMatchResult);
    }

    private TransactionMatchResult MatchTransaction(
        PurchaseOrderMatchModel matchModel,
        HeaderMatchingConfiguration headerMatchingConfiguration,
        LineMatchingConfiguration lineMatchingConfiguration,
        decimal totalToMatch,
        ref decimal balance,
        TransactionCouple transaction)
    {
        transaction.TransactionHeader.TransactionStatus = EnumTransactionStatus.MatchPending;
        var result = new TransactionMatchResult(transaction.TransactionHeader.TransactionStatus);

        // 1. Header total matching
        var headerTotalMatchResult = this.headerMatchProcessor.HeaderTotalMatch(
            "Order", "Invoice", headerMatchingConfiguration, totalToMatch, transaction);
        result.AbsorbResult(headerTotalMatchResult);
        
        if (result.Status == EnumTransactionStatus.MatchRejected)
            return result;

        // 2. Line item matching
        var lineMatchResult = this.lineMatchProcessor.MatchingFieldChecks(
            lineMatchingConfiguration, 
            matchModel.PurchaseOrders.Select(po => po.TransactionHeader),
            new[] { transaction.TransactionHeader });
        result.AbsorbResult(lineMatchResult);

        if (result.Status == EnumTransactionStatus.MatchRejected)
            return result;

        // 3. Quantity and amount validation
        var quantityMatchResult = this.ValidateQuantityMatch(matchModel, transaction);
        result.AbsorbResult(quantityMatchResult);

        // 4. Update balance
        balance -= transaction.TransactionHeader.Total ?? 0;

        return result;
    }
}
```

## Validation Engine

### Business Rule Validation

```csharp
public class ValidationProcessor
{
    public EnumValidationStatus ValidateTransaction(
        Guid customerId,
        bool enableCustomerSuspensions,
        List<WorkflowData> workflowDatas,
        List<WorkflowField> workflowFields,
        List<FieldData> fieldData,
        List<ValidationRule> validationRules,
        TransactionModel transaction,
        string senderAddress,
        IValidationResultService update)
    {
        var originalTransactionStatus = transaction.Status;

        // 1. Expected workflow field validation
        new ExpectedWorkflowFieldValidation(this.dataLayer.ETrading.DataContext)
            .Validate(workflowDatas, transaction);

        // 2. Server-side validation
        var serverValidation = new ServerValidation(new CustomCodeChecker(this.executionFactory));
        var fieldDataValidationProcessor = new FieldDataValidation(this.lookUpProcessor, serverValidation);
        var validationRulesProcessor = new ValidationRules(serverValidation);

        // 3. Field data validation
        fieldDataValidationProcessor.Validate(customerId, this.correlationId, fieldData, transaction, workflowFields);
        
        // 4. Validation rules processing
        validationRulesProcessor.Validate(this.correlationId, transaction, validationRules, serverValidation);
        
        // 5. Address validation
        this.addressValidationProcessor.Validation(fieldData, transaction);

        // 6. Get validation data and determine result
        var validationData = GetValidationData(fieldData, validationRules, transaction.ValidationMessages);

        // 7. Assign transaction group
        transaction.AssignedTransactionGroupId = CustomerTransactionGroupProcessor.SetTransactionGroup(
            transaction.CustomerTransactionGroupId,
            transaction.AssignedTransactionGroupId,
            enableCustomerSuspensions,
            validationData);

        // 8. Determine final validation result
        update.DetermineResult(senderAddress, validationData, transaction, transaction.ValidationMessages);

        // 9. Send suspense emails if required
        this.SendSuspenseEmails(transaction, originalTransactionStatus);

        return transaction.ValidationStatus;
    }
}
```

### Validation Rules Configuration

```csharp
public class ValidationRule
{
    public Guid Id { get; set; }
    public Guid CustomerId { get; set; }
    public string RuleFolder { get; set; }
    public string FieldName { get; set; }
    public string ValidationExpression { get; set; }
    public string ErrorMessage { get; set; }
    public bool IsMandatory { get; set; }
    public int Priority { get; set; }
    public bool StopOnFailure { get; set; }
}

public class FieldDataValidation
{
    public void Validate(Guid customerId, string correlationId, List<FieldData> fieldData, TransactionModel transaction, List<WorkflowField> workflowFields)
    {
        foreach (var field in fieldData.Where(fd => fd.IsMandatory))
        {
            var transactionData = transaction.TransactionData
                .FirstOrDefault(td => td.Variable.Name == field.FieldName);

            if (transactionData == null || string.IsNullOrEmpty(transactionData.Value))
            {
                transaction.ValidationMessages.Add(new ValidationMessage
                {
                    FieldName = field.FieldName,
                    Message = $"Required field '{field.DisplayName}' is missing",
                    Severity = ValidationSeverity.Error
                });
            }
        }

        // Additional field-specific validation logic
        this.ValidateFieldFormats(fieldData, transaction);
        this.ValidateFieldRanges(fieldData, transaction);
        this.ValidateFieldDependencies(fieldData, transaction);
    }
}
```

## Transaction Lifecycle Management

### Complete Workflow Processing

```csharp
public class CompleteWorkFlow
{
    public void ProcessFromMatching(Guid? customerId)
    {
        customerId = customerId == Guid.Empty ? null : customerId;

        var ids = this.dataLayer.ETrading.TransactionRepository.GetAllFromMatchingActions(customerId);
        Logger.Trace("BulkProcess", $"ProcessFromMatching: {ids.Count} Transactions");

        this.ActionTransactions(ids, "ProcessFromMatching");
    }

    public void ProcessActions(Guid? customerId)
    {
        customerId = customerId == Guid.Empty ? null : customerId;

        var ids = this.dataLayer.ETrading.TransactionRepository.GetAllPendingActions(customerId);
        Logger.Trace("BulkProcess", $"ProcessActions: {ids.Count} Transactions");

        this.ActionTransactions(ids, "ProcessActions");
    }

    private void ActionTransactions(List<Guid> transactionIds, string processType)
    {
        foreach (var transactionId in transactionIds)
        {
            try
            {
                var transactionCouple = this.GetTransactionCouple(transactionId);
                var dataModel = this.BuildDataModel(transactionCouple);

                // Process through state machine
                this.transactionStateMachine.ProcessTransaction(dataModel, transactionCouple, false);

                Logger.Trace("BulkProcess", $"{processType}: Processed {transactionId}");
            }
            catch (Exception ex)
            {
                Logger.Error("BulkProcess", $"{processType}: Failed to process {transactionId}: {ex.Message}");
                
                // Update transaction with error status
                this.UpdateTransactionWithError(transactionId, ex.Message);
            }
        }
    }
}
```

### Error Handling and Recovery

```csharp
public class TransactionErrorHandler
{
    public void HandleProcessingError(TransactionCouple transactionCouple, Exception exception)
    {
        var transaction = transactionCouple.TransactionModel;
        
        // Log the error
        Logger.Error("TransactionProcessing", $"Error processing transaction {transaction.Id}: {exception.Message}");

        // Update transaction status
        transaction.Status = EnumTransactionStatus.FailedExtraction;
        transaction.Reason = exception.Message;

        // Create audit entry
        this.auditRepository.LogError(
            new Entity(transaction.Id, nameof(TransactionHeader)),
            Actor.System,
            exception.Message);

        // Determine if retry is appropriate
        if (this.ShouldRetry(exception))
        {
            this.ScheduleRetry(transaction.Id);
        }
        else
        {
            // Send to manual intervention queue
            this.SendToManualIntervention(transaction.Id, exception.Message);
        }
    }

    private bool ShouldRetry(Exception exception)
    {
        // Determine retry logic based on exception type
        return exception is TimeoutException || 
               exception is SqlException ||
               exception is HttpRequestException;
    }
}
```

## Performance Optimisation

### Bulk Processing Strategies

```csharp
public class BulkTransactionProcessor
{
    public async Task ProcessTransactionBatch(List<Guid> transactionIds, int batchSize = 100)
    {
        var batches = transactionIds.Chunk(batchSize);

        foreach (var batch in batches)
        {
            var tasks = batch.Select(async transactionId =>
            {
                try
                {
                    await this.ProcessSingleTransaction(transactionId);
                }
                catch (Exception ex)
                {
                    Logger.Error("BulkProcessing", $"Failed to process transaction {transactionId}: {ex.Message}");
                }
            });

            await Task.WhenAll(tasks);
            
            // Brief pause between batches to prevent overwhelming the system
            await Task.Delay(100);
        }
    }

    private async Task ProcessSingleTransaction(Guid transactionId)
    {
        using var scope = this.serviceProvider.CreateScope();
        var processor = scope.ServiceProvider.GetRequiredService<TransactionProcessor>();
        
        await processor.ProcessTransactionAsync(transactionId);
    }
}
```

### Caching and Performance

```csharp
public class CachedValidationProcessor
{
    private readonly IMemoryCache cache;
    private readonly ValidationProcessor validationProcessor;

    public async Task<ValidationResult> ValidateWithCache(TransactionModel transaction, Guid customerId)
    {
        var cacheKey = $"validation_rules_{customerId}_{transaction.DocumentType}";
        
        if (!cache.TryGetValue(cacheKey, out List<ValidationRule> validationRules))
        {
            validationRules = await this.GetValidationRules(customerId, transaction.DocumentType);
            
            var cacheOptions = new MemoryCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(30),
                SlidingExpiration = TimeSpan.FromMinutes(10)
            };
            
            cache.Set(cacheKey, validationRules, cacheOptions);
        }

        return await this.validationProcessor.ValidateAsync(transaction, validationRules);
    }
}
```

## Related Documents

- **[WorkflowEngine.md](./WorkflowEngine.md)** - Approval workflows and business rules
- **[DataModelsAndEntities.md](./DataModelsAndEntities.md)** - Core business entities and relationships
- **[BusinessLogicArchitecture.md](./BusinessLogicArchitecture.md)** - Business logic patterns and architecture
- **[AuditingAndLogging.md](./AuditingAndLogging.md)** - Audit trails and compliance logging
- **[SystemOverview.md](./SystemOverview.md)** - High-level system architecture

---

*This document provides comprehensive coverage of the eHub transaction processing system. Refer to the related documentation for detailed implementation guidance on specific components.*
