# eHub Troubleshooting Guide

## Purpose & Scope

This document provides comprehensive troubleshooting guidance for the eHub platform, including common issues, diagnostic procedures, error handling strategies, and recovery processes. It serves as the definitive guide for diagnosing and resolving issues across all eHub components.

## Prerequisites

- Understanding of eHub system architecture and components
- Knowledge of logging systems and diagnostic tools
- Familiarity with SQL Server and Azure services
- Basic understanding of .NET application troubleshooting

## Core Concepts

### Troubleshooting Philosophy

The eHub troubleshooting approach is built on several key principles:

1. **Systematic Diagnosis**: Structured approach to problem identification and resolution
2. **Comprehensive Logging**: Detailed logging for effective issue diagnosis
3. **Proactive Monitoring**: Early detection and prevention of issues
4. **Root Cause Analysis**: Focus on underlying causes rather than symptoms
5. **Knowledge Sharing**: Documented solutions for common issues

### Troubleshooting Architecture Overview

```
eHub Troubleshooting Architecture
├── Issue Detection
│   ├── Automated Monitoring - System health checks and alerts
│   ├── User Reports - Customer and user-reported issues
│   ├── Performance Monitoring - Performance degradation detection
│   └── Error Logging - Application and system error capture
├── Diagnostic Tools
│   ├── Log Analysis - Centralized log analysis and correlation
│   ├── Performance Profiling - Application and database profiling
│   ├── Health Checks - Component health verification
│   └── Debugging Tools - Development and production debugging
├── Issue Resolution
│   ├── Immediate Fixes - Quick resolution for critical issues
│   ├── Workarounds - Temporary solutions while permanent fixes are developed
│   ├── Root Cause Fixes - Permanent solutions addressing underlying causes
│   └── Prevention Measures - Process improvements to prevent recurrence
└── Knowledge Management
    ├── Issue Database - Searchable repository of known issues and solutions
    ├── Runbooks - Step-by-step resolution procedures
    ├── Escalation Procedures - When and how to escalate issues
    └── Post-Incident Reviews - Learning from incidents to improve processes
```

## Common Issues and Solutions

### Transaction Processing Issues

#### Issue: Transactions Stuck in Processing State

**Symptoms:**
- Transactions remain in "ReadyForProcessing" or "ValidationPending" status
- Background jobs appear to be running but not completing
- No error messages in logs

**Diagnostic Steps:**
```csharp
// Check transaction status distribution
SELECT TransactionStatus, COUNT(*) as Count
FROM [Datastore].[TransactionHeader]
WHERE TransactionDate >= DATEADD(day, -7, GETDATE())
GROUP BY TransactionStatus
ORDER BY Count DESC;

// Check for long-running transactions
SELECT Id, TransactionNumber, TransactionStatus, 
       DateDocumentAdded, DATEDIFF(minute, DateDocumentAdded, GETDATE()) as MinutesInProcessing
FROM [Datastore].[TransactionHeader]
WHERE TransactionStatus IN ('ReadyForProcessing', 'ValidationPending')
  AND DateDocumentAdded < DATEADD(hour, -2, GETDATE())
ORDER BY DateDocumentAdded;
```

**Common Causes and Solutions:**

1. **Hangfire Job Queue Backlog**
   ```csharp
   // Check Hangfire dashboard for queue status
   // Solution: Scale up background job servers or clear failed jobs
   
   // Clear failed jobs (use with caution)
   var monitor = JobStorage.Current.GetMonitoringApi();
   var failedJobs = monitor.FailedJobs(0, int.MaxValue);
   
   foreach (var job in failedJobs)
   {
       BackgroundJob.Requeue(job.Key);
   }
   ```

2. **Database Deadlocks**
   ```sql
   -- Check for deadlocks
   SELECT 
       session_id,
       blocking_session_id,
       wait_type,
       wait_time,
       wait_resource,
       text
   FROM sys.dm_exec_requests r
   CROSS APPLY sys.dm_exec_sql_text(r.sql_handle)
   WHERE blocking_session_id <> 0;
   ```

3. **Memory Issues**
   ```csharp
   // Monitor memory usage
   var process = Process.GetCurrentProcess();
   var memoryUsage = process.WorkingSet64 / (1024 * 1024); // MB
   
   if (memoryUsage > 2048) // 2GB threshold
   {
       // Force garbage collection
       GC.Collect();
       GC.WaitForPendingFinalizers();
       GC.Collect();
   }
   ```

#### Issue: PDF Extraction Failures

**Symptoms:**
- Documents stuck in "FailedExtraction" status
- Error messages related to PDF processing
- Missing or incorrect field extractions

**Diagnostic Approach:**
```mermaid
flowchart TD
    A[PDF Extraction Failure] --> B{Check Error Type}
    B -->|File Access Error| C[Verify File Exists in Azure Storage]
    B -->|Parsing Error| D[Check PDF Format and Corruption]
    B -->|Rule Error| E[Validate Business Rules]
    B -->|Memory Error| F[Check Available Memory]
    
    C --> C1[Check Azure Storage Connection]
    C --> C2[Verify File Permissions]
    
    D --> D1[Test PDF with External Tool]
    D --> D2[Check PDF Version Compatibility]
    
    E --> E1[Validate Prolog Rules Syntax]
    E --> E2[Test Rules with Sample Data]
    
    F --> F1[Monitor Memory Usage]
    F --> F2[Implement Memory Optimization]
    
    style A fill:#ffebee
    style C fill:#fff3e0
    style D fill:#e8f5e8
    style E fill:#e3f2fd
    style F fill:#fce4ec
```

**Resolution Steps:**
```csharp
public class PDFExtractionTroubleshooter
{
    public async Task<DiagnosticResult> DiagnosePDFExtractionIssue(Guid documentId)
    {
        var result = new DiagnosticResult();
        
        // 1. Verify document exists
        var document = await documentRepository.GetByIdAsync(documentId);
        if (document == null)
        {
            result.AddError("Document not found in database");
            return result;
        }
        
        // 2. Check file accessibility
        try
        {
            var fileExists = await azureStorage.CheckIfBlobExistsAsync(
                document.FullFilePath, 
                document.FileContainer);
            
            if (!fileExists)
            {
                result.AddError("File not found in Azure Storage");
                return result;
            }
        }
        catch (Exception ex)
        {
            result.AddError($"Azure Storage access error: {ex.Message}");
            return result;
        }
        
        // 3. Test PDF parsing
        try
        {
            var pdfStream = await azureReader.DownloadBlobAsync(document.FullFilePath);
            var pdfDocument = PdfDocument.Open(pdfStream);
            
            result.AddInfo($"PDF has {pdfDocument.NumberOfPages} pages");
            result.AddInfo($"PDF version: {pdfDocument.Version}");
            
            // Check for text content
            var hasText = pdfDocument.GetPages().Any(page => !string.IsNullOrEmpty(page.Text));
            if (!hasText)
            {
                result.AddWarning("PDF appears to be image-based, OCR may be required");
            }
        }
        catch (Exception ex)
        {
            result.AddError($"PDF parsing error: {ex.Message}");
        }
        
        // 4. Validate business rules
        try
        {
            var customer = await customerRepository.GetByIdAsync(document.CustomerId);
            var rules = await GetBusinessRulesAsync(customer.Id);
            
            var ruleValidator = new BusinessRuleValidator();
            var ruleValidation = ruleValidator.ValidateRules(rules);
            
            if (!ruleValidation.IsValid)
            {
                result.AddError($"Business rule validation failed: {ruleValidation.ErrorMessage}");
            }
        }
        catch (Exception ex)
        {
            result.AddError($"Business rule validation error: {ex.Message}");
        }
        
        return result;
    }
}
```

### Database Performance Issues

#### Issue: Slow Query Performance

**Symptoms:**
- API endpoints responding slowly
- Database timeout errors
- High CPU usage on database server

**Diagnostic Queries:**
```sql
-- Find slow-running queries
SELECT TOP 10
    qs.total_elapsed_time / qs.execution_count AS avg_elapsed_time,
    qs.total_logical_reads / qs.execution_count AS avg_logical_reads,
    qs.execution_count,
    SUBSTRING(qt.text, (qs.statement_start_offset/2)+1,
        ((CASE qs.statement_end_offset
            WHEN -1 THEN DATALENGTH(qt.text)
            ELSE qs.statement_end_offset
        END - qs.statement_start_offset)/2)+1) AS statement_text
FROM sys.dm_exec_query_stats qs
CROSS APPLY sys.dm_exec_sql_text(qs.sql_handle) qt
ORDER BY avg_elapsed_time DESC;

-- Check for missing indexes
SELECT 
    migs.avg_total_user_cost * (migs.avg_user_impact / 100.0) * (migs.user_seeks + migs.user_scans) AS improvement_measure,
    'CREATE INDEX [missing_index_' + CONVERT(varchar, mig.index_group_handle) + '_' + CONVERT(varchar, mid.index_handle)
    + '_' + LEFT(PARSENAME(mid.statement, 1), 20) + ']'
    + ' ON ' + mid.statement
    + ' (' + ISNULL(mid.equality_columns,'')
    + CASE WHEN mid.equality_columns IS NOT NULL AND mid.inequality_columns IS NOT NULL THEN ',' ELSE '' END
    + ISNULL(mid.inequality_columns, '')
    + ')'
    + ISNULL(' INCLUDE (' + mid.included_columns + ')', '') AS create_index_statement,
    migs.*,
    mid.database_id,
    mid.[object_id]
FROM sys.dm_db_missing_index_groups mig
INNER JOIN sys.dm_db_missing_index_group_stats migs ON migs.group_handle = mig.index_group_handle
INNER JOIN sys.dm_db_missing_index_details mid ON mig.index_handle = mid.index_handle
WHERE migs.avg_total_user_cost * (migs.avg_user_impact / 100.0) * (migs.user_seeks + migs.user_scans) > 10
ORDER BY improvement_measure DESC;
```

**Performance Optimization Steps:**
```csharp
public class DatabasePerformanceOptimizer
{
    public async Task OptimizeDatabasePerformanceAsync()
    {
        // 1. Update statistics
        await UpdateStatisticsAsync();
        
        // 2. Rebuild fragmented indexes
        await RebuildFragmentedIndexesAsync();
        
        // 3. Clear query plan cache if needed
        await ClearQueryPlanCacheAsync();
        
        // 4. Monitor and alert on performance
        await SetupPerformanceMonitoringAsync();
    }
    
    private async Task UpdateStatisticsAsync()
    {
        var sql = @"
            DECLARE @sql NVARCHAR(MAX) = '';
            SELECT @sql = @sql + 'UPDATE STATISTICS ' + QUOTENAME(SCHEMA_NAME(schema_id)) + '.' + QUOTENAME(name) + ';' + CHAR(13)
            FROM sys.tables;
            EXEC sp_executesql @sql;";
        
        await context.Database.ExecuteSqlRawAsync(sql);
    }
    
    private async Task RebuildFragmentedIndexesAsync()
    {
        var sql = @"
            DECLARE @sql NVARCHAR(MAX) = '';
            SELECT @sql = @sql + 
                'ALTER INDEX ' + QUOTENAME(i.name) + 
                ' ON ' + QUOTENAME(SCHEMA_NAME(t.schema_id)) + '.' + QUOTENAME(t.name) + 
                ' REBUILD;' + CHAR(13)
            FROM sys.indexes i
            INNER JOIN sys.tables t ON i.object_id = t.object_id
            INNER JOIN sys.dm_db_index_physical_stats(DB_ID(), NULL, NULL, NULL, 'LIMITED') ps 
                ON i.object_id = ps.object_id AND i.index_id = ps.index_id
            WHERE ps.avg_fragmentation_in_percent > 30
              AND i.type > 0;
            EXEC sp_executesql @sql;";
        
        await context.Database.ExecuteSqlRawAsync(sql);
    }
}
```

### Integration Issues

#### Issue: AS2 Communication Failures

**Symptoms:**
- AS2 messages not being sent or received
- Certificate validation errors
- Connection timeout errors

**Diagnostic Steps:**
```csharp
public class AS2DiagnosticService
{
    public async Task<AS2DiagnosticResult> DiagnoseAS2IssueAsync(string partnerId)
    {
        var result = new AS2DiagnosticResult();
        
        // 1. Check certificate validity
        var certificateCheck = await CheckCertificateValidityAsync(partnerId);
        result.CertificateStatus = certificateCheck;
        
        // 2. Test network connectivity
        var connectivityCheck = await TestNetworkConnectivityAsync(partnerId);
        result.ConnectivityStatus = connectivityCheck;
        
        // 3. Validate AS2 configuration
        var configCheck = await ValidateAS2ConfigurationAsync(partnerId);
        result.ConfigurationStatus = configCheck;
        
        // 4. Check recent message history
        var messageHistory = await GetRecentMessageHistoryAsync(partnerId);
        result.RecentMessages = messageHistory;
        
        return result;
    }
    
    private async Task<CertificateStatus> CheckCertificateValidityAsync(string partnerId)
    {
        try
        {
            var certificate = await GetPartnerCertificateAsync(partnerId);
            
            if (certificate.NotAfter < DateTime.UtcNow)
            {
                return CertificateStatus.Expired;
            }
            
            if (certificate.NotAfter < DateTime.UtcNow.AddDays(30))
            {
                return CertificateStatus.ExpiringSoon;
            }
            
            return CertificateStatus.Valid;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to check certificate for partner {PartnerId}", partnerId);
            return CertificateStatus.Error;
        }
    }
}
```

## Error Handling and Recovery

### Automatic Error Recovery

```csharp
public class AutomaticErrorRecoveryService
{
    public async Task ProcessErrorRecoveryAsync()
    {
        // 1. Identify recoverable errors
        var recoverableErrors = await GetRecoverableErrorsAsync();
        
        foreach (var error in recoverableErrors)
        {
            try
            {
                await AttemptRecoveryAsync(error);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to recover from error {ErrorId}", error.Id);
                await EscalateErrorAsync(error);
            }
        }
    }
    
    private async Task AttemptRecoveryAsync(ErrorRecord error)
    {
        switch (error.ErrorType)
        {
            case ErrorType.TransactionProcessing:
                await RecoverTransactionProcessingErrorAsync(error);
                break;
                
            case ErrorType.FileAccess:
                await RecoverFileAccessErrorAsync(error);
                break;
                
            case ErrorType.DatabaseConnection:
                await RecoverDatabaseConnectionErrorAsync(error);
                break;
                
            case ErrorType.ExternalService:
                await RecoverExternalServiceErrorAsync(error);
                break;
        }
    }
    
    private async Task RecoverTransactionProcessingErrorAsync(ErrorRecord error)
    {
        // Retry transaction processing with exponential backoff
        var retryPolicy = Policy
            .Handle<Exception>()
            .WaitAndRetryAsync(
                retryCount: 3,
                sleepDurationProvider: retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)),
                onRetry: (outcome, timespan, retryCount, context) =>
                {
                    logger.LogWarning("Retrying transaction processing. Attempt {RetryCount}", retryCount);
                });
        
        await retryPolicy.ExecuteAsync(async () =>
        {
            var transactionId = Guid.Parse(error.EntityId);
            await transactionProcessor.ProcessTransactionAsync(transactionId);
        });
    }
}
```

### Circuit Breaker Pattern

```mermaid
stateDiagram-v2
    [*] --> Closed
    Closed --> Open : Failure threshold reached
    Open --> HalfOpen : Timeout period elapsed
    HalfOpen --> Closed : Success
    HalfOpen --> Open : Failure
    
    note right of Closed : Normal operation<br/>Requests pass through
    note right of Open : Failing fast<br/>Requests rejected immediately
    note right of HalfOpen : Testing recovery<br/>Limited requests allowed
```

```csharp
public class CircuitBreakerService
{
    private readonly CircuitBreakerPolicy circuitBreaker;
    
    public CircuitBreakerService()
    {
        this.circuitBreaker = Policy
            .Handle<HttpRequestException>()
            .Or<TimeoutException>()
            .CircuitBreakerAsync(
                handledEventsAllowedBeforeBreaking: 5,
                durationOfBreak: TimeSpan.FromMinutes(1),
                onBreak: (exception, duration) =>
                {
                    logger.LogWarning("Circuit breaker opened for {Duration}", duration);
                },
                onReset: () =>
                {
                    logger.LogInformation("Circuit breaker reset");
                },
                onHalfOpen: () =>
                {
                    logger.LogInformation("Circuit breaker half-open");
                });
    }
    
    public async Task<T> ExecuteWithCircuitBreakerAsync<T>(Func<Task<T>> operation)
    {
        return await circuitBreaker.ExecuteAsync(operation);
    }
}
```

## Diagnostic Tools and Procedures

### Health Check Implementation

```csharp
public class ComprehensiveHealthCheck : IHealthCheck
{
    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        var healthData = new Dictionary<string, object>();
        var overallHealthy = true;
        var issues = new List<string>();
        
        // Database health
        try
        {
            await CheckDatabaseHealthAsync();
            healthData["database"] = "healthy";
        }
        catch (Exception ex)
        {
            overallHealthy = false;
            issues.Add($"Database: {ex.Message}");
            healthData["database"] = "unhealthy";
        }
        
        // Azure Storage health
        try
        {
            await CheckAzureStorageHealthAsync();
            healthData["storage"] = "healthy";
        }
        catch (Exception ex)
        {
            overallHealthy = false;
            issues.Add($"Storage: {ex.Message}");
            healthData["storage"] = "unhealthy";
        }
        
        // Background jobs health
        try
        {
            await CheckBackgroundJobsHealthAsync();
            healthData["background_jobs"] = "healthy";
        }
        catch (Exception ex)
        {
            overallHealthy = false;
            issues.Add($"Background Jobs: {ex.Message}");
            healthData["background_jobs"] = "unhealthy";
        }
        
        // Memory usage check
        var memoryUsage = GC.GetTotalMemory(false) / (1024 * 1024); // MB
        healthData["memory_usage_mb"] = memoryUsage;
        
        if (memoryUsage > 2048) // 2GB threshold
        {
            issues.Add($"High memory usage: {memoryUsage}MB");
        }
        
        return overallHealthy 
            ? HealthCheckResult.Healthy("All systems operational", healthData)
            : HealthCheckResult.Unhealthy(string.Join("; ", issues), data: healthData);
    }
}
```

### Log Analysis Tools

```csharp
public class LogAnalysisService
{
    public async Task<LogAnalysisResult> AnalyzeLogsAsync(DateTime fromDate, DateTime toDate, string logLevel = null)
    {
        var result = new LogAnalysisResult();
        
        // Analyze error patterns
        var errorPatterns = await AnalyzeErrorPatternsAsync(fromDate, toDate);
        result.ErrorPatterns = errorPatterns;
        
        // Analyze performance trends
        var performanceTrends = await AnalyzePerformanceTrendsAsync(fromDate, toDate);
        result.PerformanceTrends = performanceTrends;
        
        // Identify anomalies
        var anomalies = await IdentifyAnomaliesAsync(fromDate, toDate);
        result.Anomalies = anomalies;
        
        return result;
    }
    
    private async Task<List<ErrorPattern>> AnalyzeErrorPatternsAsync(DateTime fromDate, DateTime toDate)
    {
        // Query logs for error patterns
        var errorLogs = await GetErrorLogsAsync(fromDate, toDate);
        
        return errorLogs
            .GroupBy(log => new { log.ErrorType, log.Component })
            .Select(group => new ErrorPattern
            {
                ErrorType = group.Key.ErrorType,
                Component = group.Key.Component,
                Count = group.Count(),
                FirstOccurrence = group.Min(log => log.Timestamp),
                LastOccurrence = group.Max(log => log.Timestamp),
                SampleMessage = group.First().Message
            })
            .OrderByDescending(pattern => pattern.Count)
            .ToList();
    }
}
```

## Escalation Procedures

### Issue Severity Classification

```mermaid
graph TB
    subgraph "Severity Levels"
        S1[Severity 1 - Critical<br/>System Down<br/>Data Loss]
        S2[Severity 2 - High<br/>Major Feature Broken<br/>Performance Degraded]
        S3[Severity 3 - Medium<br/>Minor Feature Issue<br/>Workaround Available]
        S4[Severity 4 - Low<br/>Cosmetic Issue<br/>Enhancement Request]
    end
    
    subgraph "Response Times"
        S1 --> R1[Immediate Response<br/>15 minutes]
        S2 --> R2[Urgent Response<br/>2 hours]
        S3 --> R3[Standard Response<br/>24 hours]
        S4 --> R4[Planned Response<br/>Next Sprint]
    end
    
    subgraph "Escalation Path"
        R1 --> E1[On-Call Engineer<br/>Development Manager<br/>CTO]
        R2 --> E2[Development Team Lead<br/>Product Manager]
        R3 --> E3[Assigned Developer<br/>Team Lead]
        R4 --> E4[Product Backlog<br/>Sprint Planning]
    end
    
    style S1 fill:#ffebee
    style S2 fill:#fff3e0
    style S3 fill:#e8f5e8
    style S4 fill:#e3f2fd
```

### Escalation Automation

```csharp
public class EscalationService
{
    public async Task ProcessEscalationAsync(Issue issue)
    {
        var escalationLevel = DetermineEscalationLevel(issue);
        
        switch (escalationLevel)
        {
            case EscalationLevel.Immediate:
                await EscalateImmediatelyAsync(issue);
                break;
                
            case EscalationLevel.Urgent:
                await EscalateUrgentlyAsync(issue);
                break;
                
            case EscalationLevel.Standard:
                await EscalateStandardAsync(issue);
                break;
        }
    }
    
    private async Task EscalateImmediatelyAsync(Issue issue)
    {
        // Page on-call engineer
        await pagerService.PageOnCallEngineerAsync(issue);
        
        // Send SMS to development manager
        await smsService.SendSMSAsync(developmentManagerPhone, $"Critical issue: {issue.Title}");
        
        // Create incident in incident management system
        await incidentService.CreateCriticalIncidentAsync(issue);
        
        // Start war room if needed
        if (issue.Severity == IssueSeverity.Critical)
        {
            await warRoomService.StartWarRoomAsync(issue);
        }
    }
}
```

## Related Documents

- **[SystemOverview.md](./SystemOverview.md)** - High-level system architecture
- **[PerformanceAndScaling.md](./PerformanceAndScaling.md)** - Performance optimization strategies
- **[MaintenanceAndOperations.md](./MaintenanceAndOperations.md)** - Operational procedures
- **[TestingStrategy.md](./TestingStrategy.md)** - Testing and quality assurance
- **[AuditingAndLogging.md](./AuditingAndLogging.md)** - Logging and audit systems

---

*This document provides comprehensive troubleshooting guidance for the eHub platform. Refer to the related documentation for detailed implementation guidance on specific diagnostic and resolution procedures.*