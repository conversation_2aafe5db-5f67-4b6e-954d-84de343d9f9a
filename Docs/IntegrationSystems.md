# eHub Integration Systems

## Purpose & Scope

This document provides comprehensive documentation of the eHub integration systems, including AS2 protocol implementation, FTP/SFTP polling mechanisms, email processing systems, and external API integrations. It serves as the definitive guide for understanding and working with the various communication protocols and integration patterns that enable eHub to connect with external trading partners and systems.

## Prerequisites

- Understanding of B2B communication protocols (AS2, FTP, SMTP)
- Knowledge of electronic document interchange (EDI) concepts
- Familiarity with certificate-based security and encryption
- Basic understanding of scheduled polling and event-driven architectures

## Core Concepts

### Integration Architecture Philosophy

The eHub integration systems are built on several key principles:

1. **Protocol Diversity**: Support for multiple communication protocols to accommodate various trading partners
2. **Security First**: End-to-end encryption and certificate-based authentication
3. **Reliable Delivery**: Comprehensive retry logic and delivery confirmation
4. **Event-Driven Processing**: Asynchronous processing with background job coordination
5. **Monitoring and Alerting**: Real-time visibility into integration health and performance

### Integration Systems Overview

```mermaid
graph TB
    subgraph "External Systems"
        TP[Trading Partners] --> AS2[AS2 Protocol]
        TP --> FTP[FTP/SFTP Servers]
        TP --> EMAIL[Email Systems]
        ERP[ERP Systems] --> API[REST APIs]
        BANK[Banking Systems] --> SFTP[SFTP]
        GOV[Government Systems] --> AS2
    end
    
    subgraph "eHub Integration Layer"
        AS2 --> AS2P[AS2 Processor]
        FTP --> FTPP[FTP Poller]
        EMAIL --> EMP[Email Processor]
        API --> APIP[API Gateway]
        SFTP --> SFTPP[SFTP Processor]
    end
    
    subgraph "Internal Processing"
        AS2P --> DQ[Document Queue]
        FTPP --> DQ
        EMP --> DQ
        APIP --> DQ
        SFTPP --> DQ
        
        DQ --> DP[Document Processing Pipeline]
        DP --> EX[Export Engine]
        EX --> AS2P
        EX --> FTPP
        EX --> EMP
    end
    
    subgraph "Monitoring & Management"
        MON[Integration Monitor] --> AS2P
        MON --> FTPP
        MON --> EMP
        MON --> APIP
        
        ALT[Alert System] --> MON
        DASH[Dashboard] --> MON
    end
    
    style TP fill:#e3f2fd
    style DQ fill:#fff3e0
    style DP fill:#e8f5e8
    style ALT fill:#ffebee
```

## AS2 Protocol Implementation

### AS2 Communication Architecture

AS2 (Applicability Statement 2) provides secure, reliable business-to-business data exchange over the internet:

```mermaid
sequenceDiagram
    participant TP as Trading Partner
    participant AS2 as AS2 Gateway
    participant EH as eHub System
    participant CERT as Certificate Store
    
    TP->>AS2: AS2 Message (Encrypted)
    AS2->>CERT: Validate Certificate
    CERT-->>AS2: Certificate Valid
    AS2->>AS2: Decrypt Message
    AS2->>AS2: Verify Digital Signature
    AS2->>EH: Process Document
    EH->>EH: Store Document
    EH->>AS2: Generate MDN
    AS2->>AS2: Sign MDN
    AS2->>AS2: Encrypt MDN
    AS2-->>TP: MDN Response
    
    Note over TP,AS2: Secure, Non-repudiation
```

#### AS2 Message Processing

**Location**: `ECXIO.Core/AS2/`

```csharp
public class AS2MessageProcessor
{
    public async Task<AS2ProcessingResult> ProcessInboundMessageAsync(
        HttpRequest request,
        AS2Configuration config)
    {
        try
        {
            // Parse AS2 headers
            var headers = ParseAS2Headers(request.Headers);
            
            // Read message content
            var messageContent = await ReadMessageContentAsync(request.Body);
            
            // Validate message integrity
            var validation = await ValidateMessageIntegrityAsync(messageContent, headers);
            if (!validation.IsValid)
            {
                return AS2ProcessingResult.ValidationFailure(validation.Errors);
            }
            
            // Decrypt message if encrypted
            var decryptedContent = await DecryptMessageAsync(messageContent, headers, config);
            
            // Verify digital signature
            var signatureValidation = await VerifyDigitalSignatureAsync(decryptedContent, headers, config);
            if (!signatureValidation.IsValid)
            {
                return AS2ProcessingResult.SignatureFailure(signatureValidation.Error);
            }
            
            // Extract business document
            var document = await ExtractBusinessDocumentAsync(decryptedContent, headers);
            
            // Store document for processing
            var documentId = await StoreDocumentAsync(document, headers);
            
            // Generate MDN (Message Disposition Notification)
            var mdn = await GenerateMDNAsync(headers, documentId, config);
            
            // Queue document for processing
            await QueueDocumentProcessingAsync(documentId);
            
            return AS2ProcessingResult.Success(documentId, mdn);
        }
        catch (AS2Exception ex)
        {
            _logger.LogError(ex, "AS2 processing error for message {MessageId}", headers?.MessageId);
            var errorMdn = await GenerateErrorMDNAsync(headers, ex, config);
            return AS2ProcessingResult.Error(ex.Message, errorMdn);
        }
    }
}
```

### AS2 Security Implementation

#### Certificate Management

```csharp
public class AS2CertificateManager
{
    public async Task<CertificateValidationResult> ValidateCertificateAsync(
        X509Certificate2 certificate,
        AS2PartnerConfiguration partner)
    {
        var validationResults = new List<ValidationResult>();
        
        // Check certificate validity period
        if (DateTime.Now < certificate.NotBefore || DateTime.Now > certificate.NotAfter)
        {
            validationResults.Add(ValidationResult.Error("Certificate is expired or not yet valid"));
        }
        
        // Verify certificate chain
        var chain = new X509Chain();
        chain.ChainPolicy.RevocationMode = X509RevocationMode.Online;
        chain.ChainPolicy.RevocationFlag = X509RevocationFlag.ExcludeRoot;
        
        if (!chain.Build(certificate))
        {
            foreach (var status in chain.ChainStatus)
            {
                validationResults.Add(ValidationResult.Warning($"Certificate chain issue: {status.StatusInformation}"));
            }
        }
        
        // Check certificate purpose
        if (!HasKeyUsage(certificate, X509KeyUsageFlags.DigitalSignature | X509KeyUsageFlags.KeyEncipherment))
        {
            validationResults.Add(ValidationResult.Error("Certificate does not have required key usage"));
        }
        
        // Validate against partner configuration
        if (partner.RequiredCertificateThumbprint != null &&
            !string.Equals(certificate.Thumbprint, partner.RequiredCertificateThumbprint, StringComparison.OrdinalIgnoreCase))
        {
            validationResults.Add(ValidationResult.Error("Certificate thumbprint does not match partner configuration"));
        }
        
        return new CertificateValidationResult
        {
            IsValid = !validationResults.Any(r => r.IsError),
            Results = validationResults
        };
    }
}
```

#### Message Encryption and Signing

```csharp
public class AS2CryptographyService
{
    public async Task<byte[]> EncryptMessageAsync(
        byte[] messageContent,
        X509Certificate2 recipientCertificate,
        EncryptionAlgorithm algorithm = EncryptionAlgorithm.AES256)
    {
        var contentInfo = new ContentInfo(messageContent);
        var envelopedCms = new EnvelopedCms(contentInfo, new AlgorithmIdentifier(GetOid(algorithm)));
        
        var recipient = new CmsRecipient(SubjectIdentifierType.IssuerAndSerialNumber, recipientCertificate);
        envelopedCms.Encrypt(recipient);
        
        return envelopedCms.Encode();
    }
    
    public async Task<byte[]> SignMessageAsync(
        byte[] messageContent,
        X509Certificate2 signingCertificate,
        SigningAlgorithm algorithm = SigningAlgorithm.SHA256withRSA)
    {
        var contentInfo = new ContentInfo(messageContent);
        var signedCms = new SignedCms(contentInfo, detached: false);
        
        var signer = new CmsSigner(SubjectIdentifierType.IssuerAndSerialNumber, signingCertificate)
        {
            DigestAlgorithm = new Oid(GetSigningOid(algorithm)),
            IncludeOption = X509IncludeOption.WholeChain
        };
        
        signedCms.ComputeSignature(signer);
        
        return signedCms.Encode();
    }
    
    public async Task<DecryptionResult> DecryptMessageAsync(
        byte[] encryptedContent,
        X509Certificate2 recipientCertificate)
    {
        try
        {
            var envelopedCms = new EnvelopedCms();
            envelopedCms.Decode(encryptedContent);
            
            var collection = new X509Certificate2Collection(recipientCertificate);
            envelopedCms.Decrypt(collection);
            
            return new DecryptionResult
            {
                Success = true,
                DecryptedContent = envelopedCms.ContentInfo.Content,
                Algorithm = GetAlgorithmName(envelopedCms.ContentEncryptionAlgorithm.Oid)
            };
        }
        catch (CryptographicException ex)
        {
            return new DecryptionResult
            {
                Success = false,
                Error = $"Decryption failed: {ex.Message}"
            };
        }
    }
}
```

### MDN (Message Disposition Notification) Handling

```mermaid
graph TD
    subgraph "MDN Generation"
        RM[Received Message] --> MP[Message Processing]
        MP --> PS{Processing Success?}
        PS -->|Yes| SMD[Success MDN]
        PS -->|No| EMD[Error MDN]
        PS -->|Warning| WMD[Warning MDN]
    end
    
    subgraph "MDN Content"
        SMD --> SMDC[Success Details<br/>- Processing Status<br/>- Receipt Time<br/>- Message ID]
        EMD --> EMDC[Error Details<br/>- Error Code<br/>- Error Description<br/>- Failure Reason]
        WMD --> WMDC[Warning Details<br/>- Warning Code<br/>- Processing Notes<br/>- Partial Success]
    end
    
    subgraph "MDN Delivery"
        SMDC --> SEC[Sign & Encrypt]
        EMDC --> SEC
        WMDC --> SEC
        SEC --> SEND[Send to Partner]
    end
    
    style RM fill:#e3f2fd
    style SEND fill:#e8f5e8
    style EMD fill:#ffebee
```

#### MDN Generation Implementation

```csharp
public class MDNGenerator
{
    public async Task<MDN> GenerateMDNAsync(
        AS2Headers originalHeaders,
        ProcessingResult processingResult,
        AS2Configuration config)
    {
        var mdn = new MDN
        {
            MessageId = Guid.NewGuid().ToString(),
            OriginalMessageId = originalHeaders.MessageId,
            Timestamp = DateTime.UtcNow,
            From = config.LocalAS2Id,
            To = originalHeaders.From,
            Subject = $"MDN for message {originalHeaders.MessageId}"
        };
        
        // Build disposition notification content
        var disposition = BuildDispositionNotification(originalHeaders, processingResult);
        mdn.DispositionNotification = disposition;
        
        // Add human-readable report
        mdn.HumanReadableReport = BuildHumanReadableReport(processingResult);
        
        // Sign MDN if required
        if (originalHeaders.DispositionNotificationOptions?.RequiresSigning == true)
        {
            mdn.IsSigned = true;
            mdn.SignedContent = await SignMDNAsync(mdn, config.SigningCertificate);
        }
        
        // Encrypt MDN if required
        if (originalHeaders.DispositionNotificationOptions?.RequiresEncryption == true)
        {
            mdn.IsEncrypted = true;
            mdn.EncryptedContent = await EncryptMDNAsync(mdn, config.PartnerCertificate);
        }
        
        return mdn;
    }
    
    private DispositionNotification BuildDispositionNotification(
        AS2Headers originalHeaders,
        ProcessingResult result)
    {
        var disposition = result.Success switch
        {
            true when result.HasWarnings => "automatic-action/MDN-sent-automatically; processed/warning",
            true => "automatic-action/MDN-sent-automatically; processed",
            false => "automatic-action/MDN-sent-automatically; failed"
        };
        
        return new DispositionNotification
        {
            ReportingUA = "eHub AS2 Gateway v1.0",
            OriginalRecipient = originalHeaders.To,
            FinalRecipient = originalHeaders.To,
            OriginalMessageId = originalHeaders.MessageId,
            Disposition = disposition,
            ReceivedContentMIC = CalculateContentMIC(originalHeaders),
            ProcessingStatus = result.Success ? "processed" : "failed",
            ErrorDetails = result.Success ? null : string.Join("; ", result.Errors)
        };
    }
}
```

## FTP/SFTP Integration

### Automated File Polling System

The FTP/SFTP integration provides automated document collection from trading partner file servers:

```mermaid
graph TB
    subgraph "FTP Polling Configuration"
        PC[Polling Configuration] --> SC[Server Configuration]
        PC --> FC[Folder Configuration]
        PC --> TC[Timing Configuration]
        
        SC --> HOST[Host/Port/Credentials]
        FC --> IN[Inbound Folders]
        FC --> OUT[Outbound Folders]
        FC --> ARCH[Archive Folders]
        TC --> FREQ[Polling Frequency]
        TC --> RETRY[Retry Settings]
    end
    
    subgraph "Polling Process"
        START[Start Polling] --> CONN[Connect to Server]
        CONN --> LIST[List Files]
        LIST --> FILTER[Filter New Files]
        FILTER --> DOWN[Download Files]
        DOWN --> PROC[Process Files]
        PROC --> ARCH2[Archive Files]
        ARCH2 --> DISC[Disconnect]
        DISC --> WAIT[Wait for Next Poll]
        WAIT --> START
    end
    
    subgraph "Error Handling"
        CONN --> ERR{Connection Error?}
        DOWN --> ERR2{Download Error?}
        ERR -->|Yes| RETRY2[Retry Logic]
        ERR2 -->|Yes| RETRY2
        RETRY2 --> ALT[Alert if Max Retries]
    end
    
    style START fill:#e3f2fd
    style PROC fill:#e8f5e8
    style ALT fill:#ffebee
```

#### FTP Polling Implementation

**Location**: `ECXIO.Core/FTP/`

```csharp
public class FTPPollingService
{
    public async Task<PollingResult> PollFTPServerAsync(FTPConfiguration config)
    {
        var result = new PollingResult { ConfigurationId = config.Id };
        
        try
        {
            using var ftpClient = CreateFTPClient(config);
            await ftpClient.ConnectAsync();
            
            // Authenticate
            await ftpClient.AuthenticateAsync(config.Username, config.Password);
            
            // Change to inbound directory
            await ftpClient.ChangeDirectoryAsync(config.InboundDirectory);
            
            // List files
            var files = await ftpClient.ListFilesAsync();
            var newFiles = await FilterNewFilesAsync(files, config);
            
            result.FilesFound = files.Count;
            result.NewFiles = newFiles.Count;
            
            // Process each new file
            foreach (var file in newFiles)
            {
                try
                {
                    var fileResult = await ProcessFTPFileAsync(ftpClient, file, config);
                    result.FileResults.Add(fileResult);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to process FTP file {FileName}", file.Name);
                    result.FileResults.Add(FTPFileResult.Error(file.Name, ex.Message));
                }
            }
            
            await ftpClient.DisconnectAsync();
            
            result.Success = true;
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "FTP polling failed for server {Host}", config.Host);
            result.Success = false;
            result.Error = ex.Message;
            return result;
        }
    }
    
    private async Task<FTPFileResult> ProcessFTPFileAsync(
        IFTPClient ftpClient,
        FTPFileInfo file,
        FTPConfiguration config)
    {
        // Download file
        var content = await ftpClient.DownloadFileAsync(file.Name);
        
        // Store document
        var documentId = await StoreDocumentAsync(content, file.Name, config.CustomerId);
        
        // Queue for processing
        await QueueDocumentProcessingAsync(documentId);
        
        // Archive file on FTP server
        if (config.ArchiveProcessedFiles)
        {
            var archivePath = Path.Combine(config.ArchiveDirectory, file.Name);
            await ftpClient.MoveFileAsync(file.Name, archivePath);
        }
        else
        {
            await ftpClient.DeleteFileAsync(file.Name);
        }
        
        return FTPFileResult.Success(file.Name, documentId);
    }
}
```

### SFTP Security and Key Management

```csharp
public class SFTPConnectionManager
{
    public async Task<ISFTPClient> CreateSecureConnectionAsync(SFTPConfiguration config)
    {
        var connectionInfo = config.AuthenticationType switch
        {
            SFTPAuthType.Password => new PasswordConnectionInfo(
                config.Host, 
                config.Port, 
                config.Username, 
                config.Password),
                
            SFTPAuthType.PrivateKey => new PrivateKeyConnectionInfo(
                config.Host, 
                config.Port, 
                config.Username, 
                LoadPrivateKey(config.PrivateKeyPath, config.PrivateKeyPassphrase)),
                
            SFTPAuthType.KeyboardInteractive => new KeyboardInteractiveConnectionInfo(
                config.Host, 
                config.Port, 
                config.Username),
                
            _ => throw new NotSupportedException($"Authentication type {config.AuthenticationType} is not supported")
        };
        
        // Configure connection timeout and keep-alive
        connectionInfo.Timeout = TimeSpan.FromSeconds(30);
        connectionInfo.RetryAttempts = 3;
        
        var sftpClient = new SftpClient(connectionInfo);
        
        // Add host key verification
        sftpClient.HostKeyReceived += (sender, e) =>
        {
            e.CanTrust = VerifyHostKey(e.HostKey, e.HostKeyName, config.TrustedHostKeys);
        };
        
        await sftpClient.ConnectAsync();
        
        return new SFTPClientWrapper(sftpClient);
    }
    
    private PrivateKeyFile LoadPrivateKey(string keyPath, string passphrase)
    {
        if (string.IsNullOrEmpty(passphrase))
        {
            return new PrivateKeyFile(keyPath);
        }
        else
        {
            return new PrivateKeyFile(keyPath, passphrase);
        }
    }
    
    private bool VerifyHostKey(byte[] hostKey, string hostKeyName, List<TrustedHostKey> trustedKeys)
    {
        var hostKeyFingerprint = CalculateFingerprint(hostKey);
        
        return trustedKeys.Any(tk => 
            tk.Algorithm == hostKeyName && 
            tk.Fingerprint.Equals(hostKeyFingerprint, StringComparison.OrdinalIgnoreCase));
    }
}
```

### FTP File Delivery and Export

```csharp
public class FTPExportService
{
    public async Task<ExportResult> ExportToFTPAsync(
        ExportDocument document,
        FTPExportConfiguration config)
    {
        try
        {
            using var ftpClient = await CreateFTPClientAsync(config);
            
            // Generate filename with timestamp
            var fileName = GenerateExportFileName(document, config.FileNameTemplate);
            
            // Change to outbound directory
            await ftpClient.ChangeDirectoryAsync(config.OutboundDirectory);
            
            // Upload file
            using var contentStream = new MemoryStream(document.Content);
            await ftpClient.UploadFileAsync(fileName, contentStream);
            
            // Verify upload
            var uploadedFile = await ftpClient.GetFileInfoAsync(fileName);
            if (uploadedFile.Size != document.Content.Length)
            {
                throw new ExportException($"File size mismatch after upload. Expected: {document.Content.Length}, Actual: {uploadedFile.Size}");
            }
            
            // Send notification if configured
            if (config.SendNotificationEmail)
            {
                await SendExportNotificationAsync(document, fileName, config);
            }
            
            return ExportResult.Success(fileName, uploadedFile.Size);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "FTP export failed for document {DocumentId}", document.Id);
            return ExportResult.Failure(ex.Message);
        }
    }
}
```

## Email Integration System

### Email Inbox Monitoring

The email integration system provides automated processing of incoming email attachments:

```mermaid
sequenceDiagram
    participant ES as Email Server
    participant EMP as Email Processor
    participant DP as Document Processor
    participant DB as Database
    participant BG as Background Jobs
    
    Note over ES,EMP: Scheduled Polling (Every 5 minutes)
    
    EMP->>ES: Connect to Inbox
    ES-->>EMP: Connection Established
    EMP->>ES: Fetch Unread Messages
    ES-->>EMP: Email Messages
    
    loop For Each Email
        EMP->>EMP: Extract Attachments
        EMP->>EMP: Filter Document Types
        EMP->>DB: Store Documents
        EMP->>BG: Queue Processing Jobs
        EMP->>ES: Mark Email as Read
    end
    
    EMP->>ES: Disconnect
    
    Note over BG,DP: Asynchronous Processing
    BG->>DP: Process Documents
    DP-->>BG: Processing Complete
```

#### Email Processing Implementation

**Location**: `ECXIO.Core/Email/`

```csharp
public class EmailInboxProcessor
{
    public async Task<EmailProcessingResult> ProcessInboxAsync(EmailInboxConfiguration config)
    {
        var result = new EmailProcessingResult { ConfigurationId = config.Id };
        
        try
        {
            using var emailClient = CreateEmailClient(config);
            await emailClient.ConnectAsync();
            
            // Authenticate
            await emailClient.AuthenticateAsync(config.Username, config.Password);
            
            // Select inbox folder
            await emailClient.SelectFolderAsync(config.FolderName ?? "INBOX");
            
            // Get unread messages
            var messages = await emailClient.GetUnreadMessagesAsync();
            result.MessagesFound = messages.Count;
            
            foreach (var message in messages)
            {
                try
                {
                    var messageResult = await ProcessEmailMessageAsync(message, config);
                    result.MessageResults.Add(messageResult);
                    
                    // Mark as read if processing successful
                    if (messageResult.Success)
                    {
                        await emailClient.MarkAsReadAsync(message.Id);
                        
                        // Move to processed folder if configured
                        if (!string.IsNullOrEmpty(config.ProcessedFolderName))
                        {
                            await emailClient.MoveToFolderAsync(message.Id, config.ProcessedFolderName);
                        }
                    }
                    else
                    {
                        // Move to error folder if configured
                        if (!string.IsNullOrEmpty(config.ErrorFolderName))
                        {
                            await emailClient.MoveToFolderAsync(message.Id, config.ErrorFolderName);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to process email message {MessageId}", message.Id);
                    result.MessageResults.Add(EmailMessageResult.Error(message.Id, ex.Message));
                }
            }
            
            await emailClient.DisconnectAsync();
            
            result.Success = true;
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Email inbox processing failed for {Host}", config.Host);
            result.Success = false;
            result.Error = ex.Message;
            return result;
        }
    }
    
    private async Task<EmailMessageResult> ProcessEmailMessageAsync(
        EmailMessage message,
        EmailInboxConfiguration config)
    {
        var result = new EmailMessageResult { MessageId = message.Id };
        
        // Extract sender information
        var senderInfo = await IdentifySenderAsync(message.From, config.CustomerId);
        
        // Extract and process attachments
        var attachments = message.Attachments.Where(IsProcessableAttachment).ToList();
        result.AttachmentsFound = attachments.Count;
        
        foreach (var attachment in attachments)
        {
            try
            {
                // Store attachment as document
                var documentId = await StoreAttachmentAsync(attachment, senderInfo, config);
                
                // Queue for processing
                await QueueDocumentProcessingAsync(documentId, ProcessingOptions.EmailSource);
                
                result.ProcessedAttachments.Add(new ProcessedAttachment
                {
                    FileName = attachment.FileName,
                    DocumentId = documentId,
                    Success = true
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process attachment {FileName}", attachment.FileName);
                result.ProcessedAttachments.Add(new ProcessedAttachment
                {
                    FileName = attachment.FileName,
                    Success = false,
                    Error = ex.Message
                });
            }
        }
        
        result.Success = result.ProcessedAttachments.Any(a => a.Success);
        return result;
    }
}
```

### Email Template System

```csharp
public class EmailTemplateService
{
    public async Task<EmailMessage> GenerateEmailFromTemplateAsync(
        string templateId,
        object templateData,
        EmailConfiguration config)
    {
        var template = await _templateRepository.GetTemplateAsync(templateId);
        if (template == null)
        {
            throw new TemplateNotFoundException($"Email template '{templateId}' not found");
        }
        
        // Render template content
        var renderedSubject = await RenderTemplateAsync(template.SubjectTemplate, templateData);
        var renderedBody = await RenderTemplateAsync(template.BodyTemplate, templateData);
        
        var email = new EmailMessage
        {
            From = new EmailAddress(config.FromAddress, config.FromName),
            Subject = renderedSubject,
            Body = renderedBody,
            IsHtml = template.IsHtmlTemplate
        };
        
        // Add recipients based on template configuration
        if (template.ToAddresses?.Any() == true)
        {
            email.To.AddRange(template.ToAddresses.Select(addr => new EmailAddress(addr)));
        }
        
        if (template.CcAddresses?.Any() == true)
        {
            email.Cc.AddRange(template.CcAddresses.Select(addr => new EmailAddress(addr)));
        }
        
        // Add attachments if specified
        foreach (var attachmentConfig in template.Attachments ?? new List<EmailAttachmentConfig>())
        {
            var attachment = await GenerateAttachmentAsync(attachmentConfig, templateData);
            email.Attachments.Add(attachment);
        }
        
        return email;
    }
    
    private async Task<string> RenderTemplateAsync(string template, object data)
    {
        // Use a templating engine (e.g., Handlebars, Razor, etc.)
        var compiledTemplate = _templateEngine.Compile(template);
        return await compiledTemplate.RenderAsync(data);
    }
}
```

### Email Security and Spam Prevention

```csharp
public class EmailSecurityService
{
    public async Task<EmailSecurityResult> ValidateEmailSecurityAsync(EmailMessage message, EmailSecurityPolicy policy)
    {
        var results = new List<SecurityCheckResult>();
        
        // SPF (Sender Policy Framework) validation
        if (policy.ValidateSPF)
        {
            var spfResult = await ValidateSPFAsync(message.From.Address, message.OriginatingIP);
            results.Add(new SecurityCheckResult("SPF", spfResult.IsValid, spfResult.Details));
        }
        
        // DKIM (DomainKeys Identified Mail) validation
        if (policy.ValidateDKIM)
        {
            var dkimResult = await ValidateDKIMAsync(message);
            results.Add(new SecurityCheckResult("DKIM", dkimResult.IsValid, dkimResult.Details));
        }
        
        // DMARC validation
        if (policy.ValidateDMARC)
        {
            var dmarcResult = await ValidateDMARCAsync(message);
            results.Add(new SecurityCheckResult("DMARC", dmarcResult.IsValid, dmarcResult.Details));
        }
        
        // Attachment security scanning
        if (policy.ScanAttachments)
        {
            foreach (var attachment in message.Attachments)
            {
                var scanResult = await ScanAttachmentAsync(attachment);
                results.Add(new SecurityCheckResult($"AttachmentScan_{attachment.FileName}", scanResult.IsSafe, scanResult.Details));
            }
        }
        
        // Content filtering
        if (policy.ContentFiltering)
        {
            var contentResult = await FilterContentAsync(message.Body, message.Subject);
            results.Add(new SecurityCheckResult("ContentFilter", contentResult.IsAcceptable, contentResult.Details));
        }
        
        var overallSecurity = results.All(r => r.Passed);
        
        return new EmailSecurityResult
        {
            IsSecure = overallSecurity,
            SecurityChecks = results,
            RecommendedAction = overallSecurity ? SecurityAction.Accept : SecurityAction.Quarantine
        };
    }
}
```

## External API Integration

### REST API Client Framework

```csharp
public class ExternalAPIClient
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<ExternalAPIClient> _logger;
    
    public ExternalAPIClient(HttpClient httpClient, ILogger<ExternalAPIClient> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
    }
    
    public async Task<ApiResponse<T>> SendRequestAsync<T>(ApiRequest request)
    {
        try
        {
            // Add authentication headers
            await AddAuthenticationAsync(request);
            
            // Create HTTP request
            using var httpRequest = CreateHttpRequest(request);
            
            // Add request tracking
            var correlationId = Guid.NewGuid();
            httpRequest.Headers.Add("X-Correlation-Id", correlationId.ToString());
            
            _logger.LogInformation("Sending API request {Method} {Url} with correlation {CorrelationId}",
                request.Method, request.Url, correlationId);
            
            // Send request with retry policy
            var response = await SendWithRetryAsync(httpRequest);
            
            // Process response
            var apiResponse = await ProcessResponseAsync<T>(response, correlationId);
            
            _logger.LogInformation("API request completed with status {StatusCode} for correlation {CorrelationId}",
                response.StatusCode, correlationId);
            
            return apiResponse;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "API request failed for {Method} {Url}", request.Method, request.Url);
            return ApiResponse<T>.Error($"Request failed: {ex.Message}");
        }
    }
    
    private async Task<HttpResponseMessage> SendWithRetryAsync(HttpRequestMessage request)
    {
        var retryPolicy = Policy
            .Handle<HttpRequestException>()
            .Or<TaskCanceledException>()
            .WaitAndRetryAsync(
                retryCount: 3,
                sleepDurationProvider: retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)),
                onRetry: (outcome, timespan, retryCount, context) =>
                {
                    _logger.LogWarning("API request retry {RetryCount} in {Delay}ms due to {Exception}",
                        retryCount, timespan.TotalMilliseconds, outcome.Exception?.Message);
                });
        
        return await retryPolicy.ExecuteAsync(async () =>
        {
            var clonedRequest = await CloneRequestAsync(request);
            return await _httpClient.SendAsync(clonedRequest);
        });
    }
}
```

### Webhook Processing

```csharp
public class WebhookProcessor
{
    public async Task<WebhookResult> ProcessWebhookAsync(
        WebhookRequest request,
        WebhookConfiguration config)
    {
        try
        {
            // Validate webhook signature
            var signatureValidation = await ValidateWebhookSignatureAsync(request, config);
            if (!signatureValidation.IsValid)
            {
                return WebhookResult.Unauthorized("Invalid webhook signature");
            }
            
            // Parse webhook payload
            var payload = await ParseWebhookPayloadAsync(request.Body, request.ContentType);
            
            // Determine webhook type
            var webhookType = DetermineWebhookType(request.Headers, payload);
            
            // Process based on type
            var result = webhookType switch
            {
                WebhookType.DocumentReceived => await ProcessDocumentReceivedWebhookAsync(payload),
                WebhookType.ProcessingComplete => await ProcessingCompleteWebhookAsync(payload),
                WebhookType.ApprovalRequired => await ProcessApprovalRequiredWebhookAsync(payload),
                WebhookType.ExportComplete => await ProcessExportCompleteWebhookAsync(payload),
                _ => WebhookResult.Success("Webhook received but no processing required")
            };
            
            // Log webhook processing
            await LogWebhookAsync(request, result, config);
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Webhook processing failed for endpoint {Endpoint}", config.EndpointUrl);
            return WebhookResult.Error($"Processing failed: {ex.Message}");
        }
    }
}
```

## Integration Monitoring and Health Checks

### Comprehensive Monitoring System

```mermaid
graph TB
    subgraph "Health Monitoring"
        HC[Health Checker] --> AS2H[AS2 Health]
        HC --> FTPH[FTP Health]
        HC --> EH[Email Health]
        HC --> APIH[API Health]
    end
    
    subgraph "Performance Metrics"
        PM[Performance Monitor] --> TPT[Throughput Metrics]
        PM --> LAT[Latency Metrics]
        PM --> ERR[Error Rates]
        PM --> AVL[Availability Metrics]
    end
    
    subgraph "Alerting System"
        AS[Alert System] --> TH[Threshold Alerts]
        AS --> FA[Failure Alerts]
        AS --> PA[Performance Alerts]
        AS --> SA[Security Alerts]
    end
    
    subgraph "Dashboard"
        DASH[Integration Dashboard] --> RT[Real-time Status]
        DASH --> HIS[Historical Trends]
        DASH --> AL[Active Alerts]
        DASH --> CFG[Configuration Status]
    end
    
    HC --> AS
    PM --> AS
    AS --> DASH
    
    style HC fill:#e3f2fd
    style AS fill:#fff3e0
    style DASH fill:#e8f5e8
```

#### Integration Health Monitoring

```csharp
public class IntegrationHealthMonitor
{
    public async Task<IntegrationHealthReport> GenerateHealthReportAsync()
    {
        var report = new IntegrationHealthReport
        {
            Timestamp = DateTime.UtcNow,
            OverallStatus = HealthStatus.Unknown
        };
        
        // Check AS2 integration health
        report.AS2Health = await CheckAS2HealthAsync();
        
        // Check FTP integration health
        report.FTPHealth = await CheckFTPHealthAsync();
        
        // Check email integration health
        report.EmailHealth = await CheckEmailHealthAsync();
        
        // Check API integration health
        report.APIHealth = await CheckAPIHealthAsync();
        
        // Determine overall health
        var allHealthChecks = new[] { report.AS2Health, report.FTPHealth, report.EmailHealth, report.APIHealth };
        report.OverallStatus = allHealthChecks.All(h => h.Status == HealthStatus.Healthy)
            ? HealthStatus.Healthy
            : allHealthChecks.Any(h => h.Status == HealthStatus.Critical)
                ? HealthStatus.Critical
                : HealthStatus.Degraded;
        
        return report;
    }
    
    private async Task<ComponentHealth> CheckAS2HealthAsync()
    {
        try
        {
            var activeConnections = await GetActiveAS2ConnectionsAsync();
            var recentErrors = await GetRecentAS2ErrorsAsync(TimeSpan.FromHours(1));
            var certificateStatus = await CheckAS2CertificatesAsync();
            
            var status = HealthStatus.Healthy;
            var issues = new List<string>();
            
            if (recentErrors.Count > 10)
            {
                status = HealthStatus.Degraded;
                issues.Add($"High error rate: {recentErrors.Count} errors in the last hour");
            }
            
            if (certificateStatus.HasExpiringSoon)
            {
                status = HealthStatus.Degraded;
                issues.Add("AS2 certificates expiring soon");
            }
            
            if (certificateStatus.HasExpired)
            {
                status = HealthStatus.Critical;
                issues.Add("AS2 certificates have expired");
            }
            
            return new ComponentHealth
            {
                ComponentName = "AS2 Integration",
                Status = status,
                LastChecked = DateTime.UtcNow,
                Issues = issues,
                Metrics = new Dictionary<string, object>
                {
                    ["ActiveConnections"] = activeConnections,
                    ["RecentErrors"] = recentErrors.Count,
                    ["CertificatesExpiringSoon"] = certificateStatus.ExpiringSoonCount
                }
            };
        }
        catch (Exception ex)
        {
            return ComponentHealth.Critical("AS2 Integration", $"Health check failed: {ex.Message}");
        }
    }
}
```

### Performance Metrics Collection

```csharp
public class IntegrationMetricsCollector
{
    public async Task CollectMetricsAsync()
    {
        var timestamp = DateTime.UtcNow;
        
        // Collect AS2 metrics
        var as2Metrics = await CollectAS2MetricsAsync();
        await RecordMetricsAsync("AS2", as2Metrics, timestamp);
        
        // Collect FTP metrics
        var ftpMetrics = await CollectFTPMetricsAsync();
        await RecordMetricsAsync("FTP", ftpMetrics, timestamp);
        
        // Collect email metrics
        var emailMetrics = await CollectEmailMetricsAsync();
        await RecordMetricsAsync("Email", emailMetrics, timestamp);
        
        // Collect API metrics
        var apiMetrics = await CollectAPIMetricsAsync();
        await RecordMetricsAsync("API", apiMetrics, timestamp);
    }
    
    private async Task<Dictionary<string, double>> CollectAS2MetricsAsync()
    {
        var endTime = DateTime.UtcNow;
        var startTime = endTime.AddHours(-1);
        
        var metrics = new Dictionary<string, double>();
        
        // Message throughput
        var inboundMessages = await CountAS2MessagesAsync(startTime, endTime, MessageDirection.Inbound);
        var outboundMessages = await CountAS2MessagesAsync(startTime, endTime, MessageDirection.Outbound);
        metrics["InboundMessagesPerHour"] = inboundMessages;
        metrics["OutboundMessagesPerHour"] = outboundMessages;
        
        // Success rates
        var successfulInbound = await CountSuccessfulAS2MessagesAsync(startTime, endTime, MessageDirection.Inbound);
        var successfulOutbound = await CountSuccessfulAS2MessagesAsync(startTime, endTime, MessageDirection.Outbound);
        metrics["InboundSuccessRate"] = inboundMessages > 0 ? (double)successfulInbound / inboundMessages * 100 : 100;
        metrics["OutboundSuccessRate"] = outboundMessages > 0 ? (double)successfulOutbound / outboundMessages * 100 : 100;
        
        // Average processing time
        var avgProcessingTime = await GetAverageAS2ProcessingTimeAsync(startTime, endTime);
        metrics["AverageProcessingTimeMs"] = avgProcessingTime.TotalMilliseconds;
        
        return metrics;
    }
}
```

## Related Documents

- **[DocumentProcessingPipeline.md](./DocumentProcessingPipeline.md)** - How integrations feed the processing pipeline
- **[BackgroundJobProcessing.md](./BackgroundJobProcessing.md)** - Background job coordination for integrations
- **[SecurityArchitecture.md](./SecurityArchitecture.md)** - Security aspects of external integrations
- **[APIArchitecture.md](./APIArchitecture.md)** - Internal API design patterns
- **[MonitoringAndAlerting.md](./MonitoringAndAlerting.md)** - Comprehensive monitoring strategies

---

*This integration systems documentation provides comprehensive guidance for understanding and working with the sophisticated communication protocols and integration patterns in eHub. The AS2 protocol implementation, FTP/SFTP polling mechanisms, and email processing systems make this a robust platform for enterprise B2B document exchange and trading partner connectivity.*