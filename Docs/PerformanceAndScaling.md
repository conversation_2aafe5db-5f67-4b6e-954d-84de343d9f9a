# eHub Performance and Scaling

## Purpose & Scope

This document provides comprehensive documentation of the eHub performance optimization and scaling strategies, including database performance tuning, caching implementations, memory management, and horizontal scaling approaches. It serves as the definitive guide for optimizing and scaling the eHub platform.

## Prerequisites

- Understanding of .NET performance optimization techniques
- Knowledge of SQL Server performance tuning
- Familiarity with Hangfire background job processing
- Basic understanding of caching strategies and memory management

## Core Concepts

### Performance Philosophy

The eHub performance and scaling strategy is built on several key principles:

1. **Performance by Design**: Built-in performance considerations from architecture to implementation
2. **Proactive Monitoring**: Continuous performance monitoring and alerting
3. **Scalable Architecture**: Horizontal and vertical scaling capabilities
4. **Efficient Resource Utilisation**: Optimised memory, CPU, and I/O usage
5. **Caching Strategy**: Multi-layer caching for frequently accessed data

### Performance Architecture Overview

```
eHub Performance Architecture
├── Database Performance
│   ├── Dual-Key Strategy - GUID/Integer performance optimization
│   ├── Index Optimization - Strategic indexing for query performance
│   ├── Connection Pooling - Efficient database connection management
│   └── Query Optimization - Optimised LINQ and SQL queries
├── Caching Strategy
│   ├── Memory Caching - In-memory data caching
│   ├── Distributed Caching - Redis for multi-instance caching
│   ├── File System Caching - Azure Storage caching
│   └── Query Result Caching - Database query result caching
├── Background Job Performance
│   ├── Queue Management - Efficient job queue processing
│   ├── Parallel Processing - Multi-threaded job execution
│   ├── Resource Throttling - CPU and memory usage control
│   └── Job Prioritisation - Critical path job prioritisation
└── Scaling Strategies
    ├── Horizontal Scaling - Multi-instance deployment
    ├── Vertical Scaling - Resource scaling per instance
    ├── Database Scaling - Read replicas and partitioning
    └── Storage Scaling - Azure Storage scaling and CDN
```

## Database Performance Optimization

### Revolutionary Dual-Key Performance Strategy

```csharp
// Every major entity implements dual-key pattern for optimal SQL Server performance
[Table("TransactionHeader", Schema = "Datastore")]
public class TransactionHeader
{
    // Business Primary Key (Non-Clustered) - GUID for distributed systems
    [Key]
    public Guid Id { get; set; }
    
    // Performance Clustered Key - Integer for SQL Server optimization
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int ClusteredId { get; set; }
    
    // Multi-tenancy with performance indexing
    [Required]
    public Guid TenantId { get; set; }
    
    // Business fields...
}

// Entity Framework configuration for optimal performance
protected override void OnModelCreating(ModelBuilder modelBuilder)
{
    modelBuilder.Entity<TransactionHeader>(entity =>
    {
        // Dual-key configuration
        entity.HasKey(p => p.Id);
        entity.HasIndex(p => p.ClusteredId).IsClustered(true);
        entity.Property(p => p.Id).HasDefaultValueSql("NEWID()");
        
        // Performance indexing strategy
        entity.HasIndex(p => p.TenantId).IsClustered(false);
        entity.HasIndex(p => p.TransactionDate).IsClustered(false);
        entity.HasIndex(p => new { p.TenantId, p.TransactionDate }).IsClustered(false);
        entity.HasIndex(p => new { p.CustomerId, p.TransactionStatus }).IsClustered(false);
        
        // Defensive relationship management
        entity.HasOne(p => p.Customer)
              .WithMany()
              .HasForeignKey(p => p.CustomerId)
              .OnDelete(DeleteBehavior.Restrict);
    });
}
```

### Database Connection Optimization

```csharp
public class OptimizedDbContext : DbContext
{
    public OptimizedDbContext(DbContextOptions<OptimizedDbContext> options)
        : base(options)
    {
        // Disable lazy loading for performance
        this.ChangeTracker.LazyLoadingEnabled = false;
        
        // Optimize change tracking
        this.ChangeTracker.AutoDetectChangesEnabled = false;
        this.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        optionsBuilder.UseSqlServer(connectionString, options =>
        {
            // Connection resilience
            options.EnableRetryOnFailure(
                maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(5),
                errorNumbersToAdd: null);
            
            // Command timeout for long-running operations
            options.CommandTimeout((int)TimeSpan.FromMinutes(5).TotalSeconds);
        });
        
        // Performance optimizations
        optionsBuilder.EnableSensitiveDataLogging(false);
        optionsBuilder.EnableServiceProviderCaching(true);
        optionsBuilder.EnableDetailedErrors(false);
    }

    // Performance-optimized save method
    public int SaveChangesAndClearTracking()
    {
        this.ChangeTracker.DetectChanges();
        var result = this.SaveChanges();
        this.ChangeTracker.Clear(); // Detach all entities for memory efficiency
        return result;
    }
}
```

### Query Performance Optimization

```mermaid
graph TB
    subgraph "Query Optimization Strategy"
        QO[Query Optimization] --> IP[Index Planning]
        QO --> QS[Query Simplification]
        QO --> PC[Projection Optimization]
        QO --> FE[Filtering Early]
    end
    
    subgraph "Index Strategy"
        IP --> CI[Clustered Indexes]
        IP --> NCI[Non-Clustered Indexes]
        IP --> CCI[Composite Indexes]
        IP --> FTI[Filtered Indexes]
    end
    
    subgraph "Query Patterns"
        QS --> SP[Stored Procedures]
        QS --> CTE[Common Table Expressions]
        QS --> WC[Window Functions]
        QS --> BQ[Batch Queries]
    end
    
    subgraph "Performance Monitoring"
        PM[Performance Monitor] --> QP[Query Plans]
        PM --> IS[Index Statistics]
        PM --> WT[Wait Times]
        PM --> BL[Blocking Analysis]
    end
    
    style QO fill:#e3f2fd
    style IP fill:#fff3e0
    style PM fill:#e8f5e8
```

```csharp
public class OptimizedTransactionRepository
{
    public async Task<PagedResult<TransactionSummary>> GetTransactionSummariesAsync(
        Guid tenantId, 
        DateTime fromDate, 
        DateTime toDate, 
        int pageSize = 50, 
        int pageNumber = 1)
    {
        // Optimized query with projection and filtering
        var query = context.TransactionHeaders
            .AsNoTracking() // No change tracking for read-only operations
            .Where(t => t.TenantId == tenantId) // Tenant isolation first
            .Where(t => t.TransactionDate >= fromDate && t.TransactionDate <= toDate) // Date range filtering
            .Select(t => new TransactionSummary // Project only required fields
            {
                Id = t.Id,
                TransactionNumber = t.TransactionNumber,
                TransactionDate = t.TransactionDate,
                Total = t.Total,
                Status = t.TransactionStatus,
                CustomerName = t.Customer.Description
            })
            .OrderByDescending(t => t.TransactionDate); // Use indexed column for ordering

        var totalCount = await query.CountAsync();
        var items = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        return new PagedResult<TransactionSummary>
        {
            Items = items,
            TotalCount = totalCount,
            PageSize = pageSize,
            PageNumber = pageNumber
        };
    }

    // Bulk operations for performance
    public async Task<int> BulkUpdateTransactionStatusAsync(
        List<Guid> transactionIds, 
        EnumTransactionStatus newStatus)
    {
        // Use raw SQL for bulk operations
        var sql = @"
            UPDATE [Datastore].[TransactionHeader] 
            SET [TransactionStatus] = @status, [LastModified] = GETUTCDATE()
            WHERE [Id] IN ({0})";

        var parameters = transactionIds.Select((id, index) => 
            new SqlParameter($"@id{index}", id)).ToArray();
        
        var parameterNames = string.Join(",", parameters.Select(p => p.ParameterName));
        var formattedSql = string.Format(sql, parameterNames);

        var allParameters = parameters.Concat(new[] { new SqlParameter("@status", newStatus) }).ToArray();
        
        return await context.Database.ExecuteSqlRawAsync(formattedSql, allParameters);
    }
}
```

## Caching Strategy Implementation

### Multi-Layer Caching Architecture

```csharp
public class CachingService
{
    private readonly IMemoryCache memoryCache;
    private readonly IDistributedCache distributedCache;
    private readonly ILogger<CachingService> logger;

    public CachingService(
        IMemoryCache memoryCache,
        IDistributedCache distributedCache,
        ILogger<CachingService> logger)
    {
        this.memoryCache = memoryCache;
        this.distributedCache = distributedCache;
        this.logger = logger;
    }

    public async Task<T> GetOrSetAsync<T>(
        string key,
        Func<Task<T>> getItem,
        TimeSpan? memoryCacheDuration = null,
        TimeSpan? distributedCacheDuration = null) where T : class
    {
        // Level 1: Memory cache (fastest)
        if (memoryCache.TryGetValue(key, out T cachedItem))
        {
            logger.LogDebug("Cache hit (Memory): {CacheKey}", key);
            return cachedItem;
        }

        // Level 2: Distributed cache (Redis)
        var distributedCachedItem = await GetFromDistributedCacheAsync<T>(key);
        if (distributedCachedItem != null)
        {
            logger.LogDebug("Cache hit (Distributed): {CacheKey}", key);
            
            // Store in memory cache for faster subsequent access
            var memoryOptions = new MemoryCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = memoryCacheDuration ?? TimeSpan.FromMinutes(5),
                Priority = CacheItemPriority.Normal,
                Size = 1
            };
            memoryCache.Set(key, distributedCachedItem, memoryOptions);
            
            return distributedCachedItem;
        }

        // Level 3: Data source (database, API, etc.)
        logger.LogDebug("Cache miss: {CacheKey}", key);
        var item = await getItem();
        
        if (item != null)
        {
            // Store in both caches
            await SetInBothCachesAsync(key, item, memoryCacheDuration, distributedCacheDuration);
        }

        return item;
    }

    private async Task SetInBothCachesAsync<T>(
        string key, 
        T item, 
        TimeSpan? memoryCacheDuration, 
        TimeSpan? distributedCacheDuration) where T : class
    {
        // Memory cache
        var memoryOptions = new MemoryCacheEntryOptions
        {
            AbsoluteExpirationRelativeToNow = memoryCacheDuration ?? TimeSpan.FromMinutes(5),
            Priority = CacheItemPriority.Normal,
            Size = 1
        };
        memoryCache.Set(key, item, memoryOptions);

        // Distributed cache
        var distributedOptions = new DistributedCacheEntryOptions
        {
            AbsoluteExpirationRelativeToNow = distributedCacheDuration ?? TimeSpan.FromHours(1)
        };
        
        var serializedItem = JsonSerializer.Serialize(item);
        await distributedCache.SetStringAsync(key, serializedItem, distributedOptions);
    }
}
```

### Azure Storage Caching

```csharp
public class CachedAzureReader : IAzureReader
{
    private readonly AzureReader azureReader;
    private readonly IMemoryCache cache;

    public async Task<string> ReadAllTextWithCacheAsync(string url)
    {
        var cacheKey = $"azure_file_{url.GetHashCode()}";
        
        if (cache.TryGetValue(cacheKey, out string cachedContent))
        {
            return cachedContent;
        }

        var content = await azureReader.ReadAllTextAsync(url);
        
        var cacheOptions = new MemoryCacheEntryOptions
        {
            SlidingExpiration = TimeSpan.FromMinutes(30),
            AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(2),
            Priority = CacheItemPriority.Normal,
            Size = content.Length / 1024 // Size in KB
        };
        
        cache.Set(cacheKey, content, cacheOptions);
        return content;
    }

    public MemoryStream GetMemoryStreamWithCache(string pathName)
    {
        var cacheKey = $"azure_stream_{pathName.GetHashCode()}";
        
        if (cache.TryGetValue(cacheKey, out byte[] cachedBytes))
        {
            return new MemoryStream(cachedBytes);
        }

        var stream = azureReader.GetMemoryStream(pathName);
        var bytes = stream.ToArray();
        
        // Cache smaller files only (< 10MB)
        if (bytes.Length < 10 * 1024 * 1024)
        {
            var cacheOptions = new MemoryCacheEntryOptions
            {
                SlidingExpiration = TimeSpan.FromMinutes(15),
                Priority = CacheItemPriority.Low,
                Size = bytes.Length / 1024
            };
            
            cache.Set(cacheKey, bytes, cacheOptions);
        }

        return new MemoryStream(bytes);
    }
}
```

## Background Job Performance Optimization

### Hangfire Performance Configuration

```csharp
public class HangfirePerformanceConfiguration
{
    public static void ConfigureHangfire(IServiceCollection services, string connectionString)
    {
        services.AddHangfire(configuration => configuration
            .SetDataCompatibilityLevel(CompatibilityLevel.Version_170)
            .UseSimpleAssemblyNameTypeSerializer()
            .UseRecommendedSerializerSettings()
            .UseSqlServerStorage(connectionString, new SqlServerStorageOptions
            {
                // Performance optimizations
                TransactionTimeout = TimeSpan.FromMinutes(3),
                CommandBatchMaxTimeout = TimeSpan.FromMinutes(30),
                SlidingInvisibilityTimeout = TimeSpan.FromMinutes(5),
                QueuePollInterval = TimeSpan.Zero, // Instant polling
                UseRecommendedIsolationLevel = true,
                UsePageLocksOnDequeue = true,
                DisableGlobalLocks = true,
                
                // Connection pooling
                PrepareSchemaIfNecessary = false,
                EnableHeavyMigrations = false,
                
                // Job storage optimization
                JobExpirationCheckInterval = TimeSpan.FromHours(1),
                CountersAggregateInterval = TimeSpan.FromMinutes(5),
                DashboardJobListLimit = 50000
            }));

        // Configure background job server with performance settings
        services.AddHangfireServer(options =>
        {
            options.WorkerCount = Environment.ProcessorCount * 2; // Optimal worker count
            options.Queues = new[] { "critical", "default", "low-priority" }; // Queue prioritization
            options.ServerTimeout = TimeSpan.FromMinutes(10);
            options.SchedulePollingInterval = TimeSpan.FromSeconds(1);
            options.HeartbeatInterval = TimeSpan.FromSeconds(30);
            options.ServerCheckInterval = TimeSpan.FromMinutes(1);
        });
    }
}
```

### Parallel Job Processing

```csharp
public class ParallelJobProcessor
{
    private readonly SemaphoreSlim semaphore;
    private readonly ILogger<ParallelJobProcessor> logger;

    public ParallelJobProcessor(ILogger<ParallelJobProcessor> logger)
    {
        // Limit concurrent jobs to prevent resource exhaustion
        var maxConcurrency = Environment.ProcessorCount;
        this.semaphore = new SemaphoreSlim(maxConcurrency, maxConcurrency);
        this.logger = logger;
    }

    [Queue("parallel_processing")]
    [AutomaticRetry(Attempts = 3, DelaysInSeconds = new[] { 60, 300, 600 })]
    public async Task ProcessTransactionBatchAsync(List<Guid> transactionIds)
    {
        var batchSize = 10; // Process in smaller batches
        var batches = transactionIds.Chunk(batchSize);

        foreach (var batch in batches)
        {
            var tasks = batch.Select(async transactionId =>
            {
                await semaphore.WaitAsync();
                try
                {
                    await ProcessSingleTransactionAsync(transactionId);
                }
                finally
                {
                    semaphore.Release();
                }
            });

            await Task.WhenAll(tasks);
            
            // Brief pause between batches to prevent overwhelming the system
            await Task.Delay(100);
        }
    }

    private async Task ProcessSingleTransactionAsync(Guid transactionId)
    {
        using var scope = serviceProvider.CreateScope();
        var processor = scope.ServiceProvider.GetRequiredService<ITransactionProcessor>();
        
        try
        {
            await processor.ProcessTransactionAsync(transactionId);
            logger.LogDebug("Successfully processed transaction {TransactionId}", transactionId);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to process transaction {TransactionId}", transactionId);
            throw; // Re-throw for Hangfire retry mechanism
        }
    }
}
```

## Memory Management and Optimization

### Memory-Efficient Entity Processing

```csharp
public class MemoryEfficientProcessor
{
    public async Task ProcessLargeDatasetAsync(Guid customerId)
    {
        const int batchSize = 1000;
        var processedCount = 0;
        var hasMoreData = true;

        while (hasMoreData)
        {
            using var scope = serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<DataContext>();
            
            // Process in batches to manage memory usage
            var batch = await context.TransactionHeaders
                .AsNoTracking() // No change tracking for read-only operations
                .Where(t => t.CustomerId == customerId)
                .Where(t => t.TransactionStatus == EnumTransactionStatus.ReadyForProcessing)
                .OrderBy(t => t.ClusteredId) // Use clustered index for efficient ordering
                .Skip(processedCount)
                .Take(batchSize)
                .Select(t => new { t.Id, t.TransactionNumber }) // Project only required fields
                .ToListAsync();

            if (!batch.Any())
            {
                hasMoreData = false;
                continue;
            }

            // Process batch
            foreach (var item in batch)
            {
                await ProcessTransactionAsync(item.Id);
            }

            processedCount += batch.Count;
            
            // Force garbage collection after each batch
            if (processedCount % (batchSize * 10) == 0)
            {
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
            }
        }
    }

    // Dispose pattern for proper resource cleanup
    public class DisposableProcessor : IDisposable
    {
        private bool disposed = false;
        private readonly List<IDisposable> resources = new List<IDisposable>();

        public void AddResource(IDisposable resource)
        {
            resources.Add(resource);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!disposed)
            {
                if (disposing)
                {
                    foreach (var resource in resources)
                    {
                        resource?.Dispose();
                    }
                    resources.Clear();
                }
                disposed = true;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
    }
}
```

## Scaling Strategies

### Horizontal Scaling Architecture

```mermaid
graph TB
    subgraph "Load Balancer"
        LB[Azure Load Balancer]
    end
    
    subgraph "Web Tier - Multiple Instances"
        API1[API Instance 1]
        API2[API Instance 2]
        API3[API Instance 3]
        WEB1[Web Instance 1]
        WEB2[Web Instance 2]
    end
    
    subgraph "Background Processing Tier"
        HF1[Hangfire Server 1<br/>Critical Queue]
        HF2[Hangfire Server 2<br/>Default Queue]
        HF3[Hangfire Server 3<br/>Low Priority Queue]
        HF4[Hangfire Server 4<br/>FTP Processing]
    end
    
    subgraph "Data Tier"
        PDB[(Primary Database<br/>Read/Write)]
        RDB1[(Read Replica 1)]
        RDB2[(Read Replica 2)]
        REDIS[(Redis Cache<br/>Distributed)]
    end
    
    subgraph "Storage Tier"
        AS[Azure Storage<br/>Hot Tier]
        ASC[Azure Storage<br/>Cool Tier]
        CDN[Azure CDN]
    end
    
    LB --> API1
    LB --> API2
    LB --> API3
    LB --> WEB1
    LB --> WEB2
    
    API1 --> PDB
    API2 --> RDB1
    API3 --> RDB2
    WEB1 --> RDB1
    WEB2 --> RDB2
    
    API1 --> REDIS
    API2 --> REDIS
    API3 --> REDIS
    
    HF1 --> PDB
    HF2 --> PDB
    HF3 --> RDB1
    HF4 --> PDB
    
    API1 --> AS
    API2 --> AS
    API3 --> AS
    WEB1 --> CDN
    WEB2 --> CDN
    
    style LB fill:#e3f2fd
    style PDB fill:#fff3e0
    style REDIS fill:#e8f5e8
```

### Auto-Scaling Configuration

```csharp
public class AutoScalingService
{
    public async Task MonitorAndScaleAsync()
    {
        var metrics = await GetPerformanceMetricsAsync();
        
        // CPU-based scaling
        if (metrics.AverageCPUUsage > 80)
        {
            await ScaleOutAsync("CPU usage high");
        }
        else if (metrics.AverageCPUUsage < 20 && metrics.InstanceCount > 2)
        {
            await ScaleInAsync("CPU usage low");
        }
        
        // Queue-based scaling for background jobs
        if (metrics.PendingJobCount > 1000)
        {
            await ScaleBackgroundProcessorsAsync("High job queue");
        }
        
        // Memory-based scaling
        if (metrics.AverageMemoryUsage > 85)
        {
            await ScaleOutAsync("Memory usage high");
        }
        
        // Database connection scaling
        if (metrics.DatabaseConnectionUsage > 90)
        {
            await ScaleDatabaseConnectionsAsync();
        }
    }

    private async Task ScaleOutAsync(string reason)
    {
        logger.LogInformation("Scaling out: {Reason}", reason);
        
        // Add new instance
        await azureResourceManager.CreateVirtualMachineScaleSetInstanceAsync();
        
        // Update load balancer
        await azureLoadBalancer.AddBackendPoolMemberAsync();
        
        // Warm up new instance
        await WarmUpNewInstanceAsync();
    }

    private async Task ScaleBackgroundProcessorsAsync(string reason)
    {
        logger.LogInformation("Scaling background processors: {Reason}", reason);
        
        // Add new Hangfire server
        var newServer = new BackgroundJobServer(new BackgroundJobServerOptions
        {
            WorkerCount = Environment.ProcessorCount,
            Queues = new[] { "default", "low-priority" },
            ServerName = $"hangfire-{Environment.MachineName}-{DateTime.UtcNow.Ticks}"
        });
        
        // Register for cleanup
        backgroundJobServers.Add(newServer);
    }
}
```

## Performance Monitoring and Metrics

### Real-Time Performance Monitoring

```csharp
public class PerformanceMonitoringService
{
    private readonly IMetricsCollector metricsCollector;
    private readonly ILogger<PerformanceMonitoringService> logger;

    public async Task CollectPerformanceMetricsAsync()
    {
        // Database performance metrics
        var dbMetrics = await CollectDatabaseMetricsAsync();
        await metricsCollector.RecordAsync("database.query_time", dbMetrics.AverageQueryTime);
        await metricsCollector.RecordAsync("database.connection_count", dbMetrics.ActiveConnections);
        await metricsCollector.RecordAsync("database.deadlock_count", dbMetrics.DeadlockCount);

        // Application performance metrics
        var appMetrics = await CollectApplicationMetricsAsync();
        await metricsCollector.RecordAsync("application.memory_usage", appMetrics.MemoryUsage);
        await metricsCollector.RecordAsync("application.cpu_usage", appMetrics.CPUUsage);
        await metricsCollector.RecordAsync("application.request_rate", appMetrics.RequestsPerSecond);

        // Background job metrics
        var jobMetrics = await CollectJobMetricsAsync();
        await metricsCollector.RecordAsync("jobs.pending_count", jobMetrics.PendingJobs);
        await metricsCollector.RecordAsync("jobs.processing_time", jobMetrics.AverageProcessingTime);
        await metricsCollector.RecordAsync("jobs.failure_rate", jobMetrics.FailureRate);

        // Cache performance metrics
        var cacheMetrics = await CollectCacheMetricsAsync();
        await metricsCollector.RecordAsync("cache.hit_rate", cacheMetrics.HitRate);
        await metricsCollector.RecordAsync("cache.memory_usage", cacheMetrics.MemoryUsage);
        await metricsCollector.RecordAsync("cache.eviction_rate", cacheMetrics.EvictionRate);
    }

    private async Task<DatabaseMetrics> CollectDatabaseMetricsAsync()
    {
        using var connection = new SqlConnection(connectionString);
        await connection.OpenAsync();

        var queryTime = await MeasureQueryPerformanceAsync(connection);
        var connectionCount = await GetActiveConnectionCountAsync(connection);
        var deadlockCount = await GetDeadlockCountAsync(connection);

        return new DatabaseMetrics
        {
            AverageQueryTime = queryTime,
            ActiveConnections = connectionCount,
            DeadlockCount = deadlockCount
        };
    }
}
```

### Performance Alerting

```csharp
public class PerformanceAlertingService
{
    public async Task CheckPerformanceThresholdsAsync()
    {
        var metrics = await GetCurrentMetricsAsync();
        
        // Critical performance alerts
        if (metrics.DatabaseQueryTime > TimeSpan.FromSeconds(5))
        {
            await SendCriticalAlertAsync("Database query time exceeded 5 seconds", metrics);
        }
        
        if (metrics.MemoryUsage > 90)
        {
            await SendCriticalAlertAsync("Memory usage exceeded 90%", metrics);
        }
        
        if (metrics.JobFailureRate > 10)
        {
            await SendWarningAlertAsync("Job failure rate exceeded 10%", metrics);
        }
        
        // Performance degradation alerts
        if (metrics.ResponseTime > TimeSpan.FromSeconds(2))
        {
            await SendWarningAlertAsync("API response time degraded", metrics);
        }
        
        if (metrics.CacheHitRate < 80)
        {
            await SendInfoAlertAsync("Cache hit rate below optimal", metrics);
        }
    }
}
```

## Related Documents

- **[DatabaseArchitecture.md](./DatabaseArchitecture.md)** - Database design and optimization
- **[BackgroundJobProcessing.md](./BackgroundJobProcessing.md)** - Hangfire job processing
- **[TestingStrategy.md](./TestingStrategy.md)** - Performance testing approaches
- **[TroubleshootingGuide.md](./TroubleshootingGuide.md)** - Performance troubleshooting
- **[MaintenanceAndOperations.md](./MaintenanceAndOperations.md)** - Operational monitoring

---

*This document provides comprehensive coverage of the eHub performance and scaling strategies. Refer to the related documentation for detailed implementation guidance on specific optimization techniques.*
