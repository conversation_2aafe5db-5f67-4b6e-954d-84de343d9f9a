# eHub Developer Quick Start Guide

## Purpose & Scope

This guide provides new developers with everything needed to set up a local development environment and start contributing to the eHub platform effectively. It covers environment setup, database configuration, build processes, and your first development tasks.

```mermaid
graph TD
    A[New Developer] --> B[Environment Setup]
    B --> C[Database Configuration]
    C --> D[Build & Test]
    D --> E[First Development Tasks]
    E --> F[Productive Developer]
    
    B --> B1[Install Prerequisites]
    B --> B2[Clone Repository]
    B --> B3[Configure Storage]
    
    C --> C1[SQL Server Setup]
    C --> C2[Run Migrations]
    C --> C3[Seed Data]
    
    D --> D1[Build Solutions]
    D --> D2[Run Tests]
    D --> D3[Start APIs]
```

## Prerequisites

### Required Software
- **Visual Studio 2022** (Community, Professional, or Enterprise)
- **.NET 7.0 SDK** or later
- **SQL Server 2019** or later (or SQL Server Express/LocalDB)
- **Git** for version control
- **Azure Storage Emulator** or **Azurite** for local development

### Recommended Tools
- **SQL Server Management Studio (SSMS)** for database management
- **Postman** or **Insomnia** for API testing
- **Azure Storage Explorer** for blob storage management
- **LINQPad** for quick database queries and testing

### Required Knowledge
- C# and .NET development experience
- Entity Framework Core basics
- REST API concepts
- Basic SQL Server knowledge

## Core Concepts

### eHub Architecture Mental Model

Think of eHub as a **document processing factory** with these key stages:

1. **Intake** - Documents arrive via email, FTP, or API
2. **Processing** - PDF extraction, data validation, field mapping
3. **Matching** - Three-way matching with existing transactions
4. **Approval** - Workflow-based approval processes
5. **Export** - Integration with external ERP/accounting systems

### Key Solution Structure
```
eHub/
├── eHub.sln              # Main solution (start here)
├── ETrading.sln          # Core trading APIs
├── DataStore.sln         # Data layer
├── ECXAuth.sln           # Authentication
├── ECXIO.Core.sln        # I/O operations
└── [7 more solutions]    # Specialized services
```

## Environment Setup

### Step 1: Clone the Repository

```bash
# Clone the repository
git clone [repository-url]
cd eHub

# Switch to develop branch (main development branch)
git checkout develop
```

### Step 2: Database Setup

#### Option A: SQL Server LocalDB (Recommended for Development)

```bash
# Install Entity Framework tools globally
dotnet tool install --global dotnet-ef

# Navigate to DataStore project
cd DataStore

# Update database with latest migrations
dotnet ef database update --startup-project ../ETradingAPI

# Navigate to ECXAuth project for auth database
cd ../ECXAuth
dotnet ef database update

# Navigate to ECXAudit project for audit database
cd ../ECXAudit
dotnet ef database update
```

#### Option B: Full SQL Server

1. Create three databases:
   - `eHub_DataStore`
   - `eHub_Auth`
   - `eHub_Audit`

2. Update connection strings in `appsettings.Development.json`:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=eHub_DataStore;Trusted_Connection=true;MultipleActiveResultSets=true;",
    "AuthConnection": "Server=(localdb)\\mssqllocaldb;Database=eHub_Auth;Trusted_Connection=true;",
    "AuditConnection": "Server=(localdb)\\mssqllocaldb;Database=eHub_Audit;Trusted_Connection=true;"
  }
}
```

### Step 3: Azure Storage Configuration

#### Option A: Azure Storage Emulator (Azurite)

```bash
# Install Azurite globally
npm install -g azurite

# Start Azurite
azurite --silent --location c:\azurite --debug c:\azurite\debug.log
```

#### Option B: Azure Storage Account

Create an Azure Storage Account and update `appsettings.Development.json`:

```json
{
  "AzureStorage": {
    "ConnectionString": "DefaultEndpointsProtocol=https;AccountName=yourstorageaccount;AccountKey=yourkey;EndpointSuffix=core.windows.net",
    "ContainerName": "documents-dev"
  }
}
```

### Step 4: Build and Test

```bash
# Restore NuGet packages for main solution
dotnet restore eHub.sln

# Build the main solution
dotnet build eHub.sln --configuration Debug

# Run tests to verify setup
dotnet test eHubTests/ETradingTests.csproj
dotnet test ETradingAPITests/ETradingAPITests.csproj
```

## First Development Tasks

### Task 1: Run the Main API

1. **Set Startup Project**: Right-click `ETradingAPI` → "Set as Startup Project"

2. **Update Launch Settings**: Edit `ETradingAPI/Properties/launchSettings.json`:

```json
{
  "profiles": {
    "ETradingAPI": {
      "commandName": "Project",
      "dotnetRunMessages": true,
      "launchBrowser": true,
      "launchUrl": "swagger",
      "applicationUrl": "https://localhost:7001;http://localhost:5001",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    }
  }
}
```

3. **Start the API**: Press F5 or run:

```bash
cd ETradingAPI
dotnet run
```

4. **Verify API**: Navigate to `https://localhost:7001/swagger` to see the API documentation

### Task 2: Create Your First Customer

Use the Swagger UI to create a test customer:

```json
{
  "customerName": "Test Customer Ltd",
  "customerCode": "TEST001",
  "isActive": true,
  "emailAddress": "<EMAIL>"
}
```

### Task 3: Upload a Test Document

1. **Prepare Test PDF**: Use any invoice PDF or create a simple test document

2. **Use Document Upload Endpoint**: POST to `/api/Document/upload`

3. **Monitor Processing**: Check the Hangfire dashboard (if running) at `https://localhost:7002`

## Development Workflow

### Daily Development Process

1. **Pull Latest Changes**:
```bash
git pull origin develop
```

2. **Update Database** (if migrations added):
```bash
cd DataStore
dotnet ef database update --startup-project ../ETradingAPI
```

3. **Build and Test**:
```bash
dotnet build eHub.sln
dotnet test
```

4. **Start Development**:
```bash
# Terminal 1: Main API
cd ETradingAPI
dotnet run

# Terminal 2: Background Services (optional)
cd Hangfire
dotnet run

# Terminal 3: I/O API (if needed)
cd ECXIO.Api
dotnet run
```

### Working with Solutions

The eHub platform has multiple solutions. Here's when to use each:

| Solution | Use When | Key Projects |
|----------|----------|-------------|
| `eHub.sln` | General development, core business logic | ETradingAPI, eHub, PDFNative |
| `ETrading.sln` | API development, transaction processing | ETradingAPI, ETradingPortal |
| `DataStore.sln` | Database model changes, data layer work | DataStore, DataStoreTests |
| `ECXAuth.sln` | Authentication and authorization features | ECXAuth |
| `ECXIO.Core.sln` | Integration work, file processing | ECXIO.Core, ECXIO.Api |
| `BackgroundServices.sln` | Background job development | ECXBackgroundService, Hangfire |

### Code Navigation Tips

#### Key Directories to Know

```
eHub/
├── ETradingAPI/Controllers/        # REST API endpoints
├── eHub/BusinessLogic/            # Core business logic
├── DataStore/Database/            # Entity Framework models
├── DataStore/Mapping/             # Object mapping logic
├── PDFNative/                     # PDF processing engine
├── ECXAuth/                       # Authentication services
├── ECXIO.Core/                    # Integration services
├── Hangfire/                      # Background job host
└── *Tests/                        # Test projects
```

#### Finding Functionality

| To Find | Look In |
|---------|---------|
| API endpoints | `ETradingAPI/Controllers/` |
| Business logic | `eHub/BusinessLogic/` |
| Database models | `DataStore/Database/` and `DataStore/Models/` |
| PDF processing | `PDFNative/` |
| Background jobs | `Hangfire/` and `ECXBackgroundService/` |
| File I/O | `ECXIO.Core/` and `ECXFileSystem/` |
| Authentication | `ECXAuth/` |

## Common Development Patterns

### Adding a New API Endpoint

1. **Create Controller Method**:
```csharp
// ETradingAPI/Controllers/YourController.cs
[HttpPost]
[Route("api/[controller]/your-endpoint")]
public async Task<IActionResult> YourEndpoint([FromBody] YourRequest request)
{
    // Implementation
    return Ok(result);
}
```

2. **Add Business Logic**:
```csharp
// eHub/BusinessLogic/YourBusinessLogic.cs
public class YourBusinessLogic
{
    public async Task<YourResult> ProcessAsync(YourRequest request)
    {
        // Business logic implementation
    }
}
```

3. **Add Tests**:
```csharp
// ETradingAPITests/Controllers/YourControllerTests.cs
[TestMethod]
public async Task YourEndpoint_ValidRequest_ReturnsSuccess()
{
    // Test implementation
}
```

### Adding a Database Entity

1. **Create Entity**:
```csharp
// DataStore/Database/YourEntity.cs
public class YourEntity
{
    public int Id { get; set; }
    public string Name { get; set; }
    // Other properties
}
```

2. **Add to DbContext**:
```csharp
// DataStore/Database/DataStoreContext.cs
public DbSet<YourEntity> YourEntities { get; set; }
```

3. **Create Migration**:
```bash
cd DataStore
dotnet ef migrations add AddYourEntity --startup-project ../ETradingAPI
dotnet ef database update --startup-project ../ETradingAPI
```

### Adding a Background Job

1. **Create Job Class**:
```csharp
// ECXBackgroundService/Jobs/YourJob.cs
public class YourJob
{
    public async Task ExecuteAsync()
    {
        // Job implementation
    }
}
```

2. **Register Job**:
```csharp
// Hangfire/Startup.cs or appropriate registration location
RecurringJob.AddOrUpdate<YourJob>("your-job", x => x.ExecuteAsync(), "0 */5 * * * *");
```

## Debugging and Troubleshooting

### Common Issues and Solutions

#### 1. Database Connection Issues
```
Error: Cannot connect to database
```
**Solution**: 
- Verify SQL Server is running
- Check connection strings in `appsettings.Development.json`
- Ensure databases exist and migrations are applied

#### 2. Missing Dependencies
```
Error: Could not load assembly 'SomeAssembly'
```
**Solution**:
```bash
dotnet restore eHub.sln
dotnet build eHub.sln
```

#### 3. Azure Storage Issues
```
Error: Azure Storage connection failed
```
**Solution**:
- Ensure Azurite is running for local development
- Verify Azure Storage connection string
- Check container permissions

#### 4. PDF Processing Errors
```
Error: PDF extraction failed
```
**Solution**:
- Verify PDF is not password protected
- Check file permissions
- Review PDF processing logs

### Debugging Best Practices

1. **Use Logging**: Check console output and log files
2. **Swagger Testing**: Use Swagger UI for API endpoint testing
3. **Database Queries**: Use SSMS to verify data changes
4. **Hangfire Dashboard**: Monitor background job status
5. **Breakpoint Debugging**: Use Visual Studio debugger effectively

## Testing Your Setup

### Verification Checklist

- [ ] Main API starts without errors (`ETradingAPI`)
- [ ] Swagger UI loads at `https://localhost:7001/swagger`
- [ ] Database connections successful (check logs)
- [ ] Can create a test customer via API
- [ ] Background services start (if running Hangfire)
- [ ] All unit tests pass

### Sample API Test

Use Postman or curl to test the API:

```bash
# Test API health
curl -X GET "https://localhost:7001/api/health" -H "accept: text/plain"

# Create test customer
curl -X POST "https://localhost:7001/api/Customer" \
  -H "accept: text/plain" \
  -H "Content-Type: application/json" \
  -d "{\"customerName\":\"Test Customer\",\"customerCode\":\"TEST001\"}"
```

## Next Steps

### Learning Path

1. **Explore the API**: Use Swagger to understand available endpoints
2. **Review Business Logic**: Study core classes in `eHub/BusinessLogic/`
3. **Understand Data Models**: Examine entities in `DataStore/Database/`
4. **Follow a Document**: Trace a document through the processing pipeline
5. **Create a Feature**: Implement a small feature end-to-end

### Recommended Reading

- **[SystemOverview.md](./SystemOverview.md)** - Understand the big picture
- **[DatabaseArchitecture.md](./DatabaseArchitecture.md)** - Database design details
- **[APIArchitecture.md](./APIArchitecture.md)** - API patterns and conventions
- **[BuildAndDeployment.md](./BuildAndDeployment.md)** - Build and deployment processes

### Getting Help

1. **Documentation**: Start with this documentation set
2. **Code Comments**: Look for XML documentation in the code
3. **Tests**: Review test files for usage examples
4. **Team**: Ask team members for guidance on complex areas

## Best Practices

### Code Standards
- Follow existing code patterns and conventions
- Write unit tests for new functionality
- Use meaningful variable and method names
- Add XML documentation to public methods
- Follow SOLID principles

### Git Workflow
- Work on feature branches from `develop`
- Write clear commit messages
- Keep commits focused and atomic
- Test before committing
- Create pull requests for code review

### Performance Considerations
- Use async/await for I/O operations
- Implement proper caching where appropriate
- Optimize database queries
- Monitor memory usage in PDF processing
- Use background jobs for long-running operations

---

*This quick start guide should have you up and running with eHub development. Refer to the specialized documentation for deeper dives into specific areas.*