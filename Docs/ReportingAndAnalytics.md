# eHub Reporting and Analytics

## Purpose & Scope

This document provides comprehensive documentation of the eHub reporting and analytics system, including dashboard functionality, report generation, business intelligence, and data visualisation capabilities. It serves as the definitive guide for understanding and implementing reporting solutions in the eHub system.

## Prerequisites

- Understanding of business intelligence and reporting concepts
- Knowledge of Stimulsoft reporting framework
- Familiarity with eHub data models and business processes
- Basic understanding of dashboard design and KPI metrics

## Core Concepts

### Reporting Architecture Philosophy

The eHub reporting and analytics system is built on several key principles:

1. **Self-Service Analytics**: Empowering users with intuitive reporting tools
2. **Real-Time Insights**: Live data connectivity for up-to-date reporting
3. **Multi-Tenant Security**: Secure data isolation across customer tenants
4. **Flexible Visualisation**: Rich dashboard and report customisation capabilities
5. **Performance Optimised**: Efficient data retrieval and caching strategies

### Reporting and Analytics Overview

```
eHub Reporting and Analytics Architecture
├── Dashboard Framework
│   ├── KPI Dashboards - Key performance indicator visualisation
│   ├── Operational Dashboards - Real-time operational metrics
│   ├── Executive Dashboards - High-level business summaries
│   └── Custom Dashboards - User-defined dashboard configurations
├── Report Generation
│   ├── Standard Reports - Pre-built business reports
│   ├── Custom Reports - User-designed report templates
│   ├── Scheduled Reports - Automated report generation and distribution
│   └── Ad-Hoc Reports - On-demand report creation
├── Data Visualisation
│   ├── Charts and Graphs - Statistical data representation
│   ├── Tables and Grids - Detailed data presentation
│   ├── Maps and Geospatial - Location-based analytics
│   └── Interactive Controls - Dynamic filtering and drill-down
└── Analytics Engine
    ├── Data Aggregation - Statistical calculations and summaries
    ├── Trend Analysis - Historical data analysis and forecasting
    ├── Comparative Analysis - Period-over-period comparisons
    └── Exception Reporting - Automated anomaly detection
```

## Dashboard Framework

### Stimulsoft Dashboard Integration

```csharp
public class DashboardViewController : Controller
{
    private readonly IApiService apiService;
    private readonly IConfiguration configuration;
    private readonly UserService userService;

    public IActionResult GetReport(string id)
    {
        var report = StiReport.CreateNewDashboard();

        if (string.IsNullOrEmpty(id))
        {
            // Load default dashboard
            var dashboardModel = this.apiService.GetJson<Dashboard>($"/Dashboard/GetDefaultDashboard");
            report.LoadFromJson(dashboardModel.DashboardJson);
        }
        else 
        {
            // Load specific dashboard
            var dashboardModel = this.apiService.GetJson<Dashboard>($"/Dashboard/GetDashboard?id={id}");
            report.LoadFromJson(dashboardModel.DashboardJson);
        }

        // Add database connections
        new StimulsoftAddDatabase(this.configuration).AddDatabase(report, this.userService);

        return StiNetCoreViewer.GetReportResult(this, report);
    }

    public IActionResult ViewerEvent()
    {
        return StiNetCoreViewer.ViewerEventResult(this);
    }

    public IActionResult Design(string id)
    {
        return this.RedirectToAction("Dashboards", "Design", new { id });
    }
}
```

### Dashboard Configuration Model

```csharp
[Table("Dashboard", Schema = "Reporting")]
public class Dashboard
{
    [Key]
    public Guid Id { get; set; }
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int ClusteredId { get; set; }
    
    // Multi-tenancy
    [Required]
    public Guid TenantId { get; set; }
    
    // Dashboard metadata
    [Required]
    public string Name { get; set; }
    public string Description { get; set; }
    public string Category { get; set; }
    public string Icon { get; set; }
    public string Colour { get; set; }
    
    // Dashboard configuration
    [Required]
    public string DashboardJson { get; set; }
    public bool IsDefault { get; set; }
    public bool IsPublic { get; set; }
    public bool IsActive { get; set; }
    
    // Access control
    public EnumRoleType RoleType { get; set; }
    public bool IsAdminDashboard { get; set; }
    public bool IsCustomerDashboard { get; set; }
    public bool IsSupplierDashboard { get; set; }
    public bool IsBuyingGroupDashboard { get; set; }
    
    // Database connections
    public string MarketPlaceConnection { get; set; }
    public string ETradingConnection { get; set; }
    public string ETradingReportsUserConnection { get; set; }
    
    // Audit fields
    public DateTime CreatedDate { get; set; }
    public DateTime? ModifiedDate { get; set; }
    public Guid CreatedBy { get; set; }
    public Guid? ModifiedBy { get; set; }
    
    // Relationships
    public ICollection<DashboardAccessLink> DashboardAccessLinks { get; set; }
}

public enum EnumRoleType
{
    Admin,
    Customer,
    Supplier,
    BuyingGroup,
    Member
}
```

### KPI Dashboard Templates

The eHub system includes several pre-built KPI dashboard templates for different business scenarios:

#### 1. **Retail KPI Dashboard**
- Sales performance metrics
- Inventory turnover analysis
- Customer satisfaction indicators
- Profit margin tracking

#### 2. **Manufacturing Dashboard**
- Production efficiency metrics
- Quality control indicators
- Supply chain performance
- Equipment utilisation rates

#### 3. **Healthcare KPI Dashboard**
- Patient satisfaction metrics
- Treatment outcome indicators
- Resource utilisation analysis
- Compliance tracking

#### 4. **Emergency Services KPI**
- Response time metrics
- Resource allocation efficiency
- Incident resolution rates
- Performance benchmarking

#### 5. **Education Dashboard**
- Student performance metrics
- Resource utilisation indicators
- Attendance tracking
- Academic outcome analysis

## Report Management System

### Report Entity Structure

```csharp
[Table("Report", Schema = "Reporting")]
public class Report
{
    [Key]
    public Guid Id { get; set; }
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int ClusteredId { get; set; }
    
    // Multi-tenancy
    [Required]
    public Guid TenantId { get; set; }
    
    // Report metadata
    [Required]
    public string Description { get; set; }
    public string Colour { get; set; }
    public string Icon { get; set; }
    public Guid ReportGroupId { get; set; }
    
    // Report configuration
    [Required]
    public string ReportJson { get; set; }
    public EnumRoleType RoleType { get; set; }
    
    // Access control flags
    public bool IsAdminReport { get; set; }
    public bool IsBuyingGroupReport { get; set; }
    public bool IsSupplierReport { get; set; }
    public bool IsCustomerReport { get; set; }
    
    // Database connections
    public string MarketPlaceConnection { get; set; }
    public string ETradingConnection { get; set; }
    public string ETradingReportsUserConnection { get; set; }
    
    // Relationships
    [ForeignKey(nameof(ReportGroupId))]
    public ReportGroup ReportGroup { get; set; }
    
    public ICollection<ReportAccessLink> ReportAccessLinks { get; set; }
    public ICollection<ScheduledReport> ScheduledReports { get; set; }
}

[Table("ReportGroup", Schema = "Reporting")]
public class ReportGroup
{
    [Key]
    public Guid Id { get; set; }
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int ClusteredId { get; set; }
    
    [Required]
    public Guid TenantId { get; set; }
    [Required]
    public string Description { get; set; }
    public string Colour { get; set; }
    public string Icon { get; set; }
    public int SortOrder { get; set; }
    public bool IsActive { get; set; }
    
    // Relationships
    public ICollection<Report> Reports { get; set; }
}
```

### Report Service Implementation

```csharp
public class ReportService
{
    public async Task<GridResultModel<ReportModel>> GetReportsAsync(Guid reportGroupId, ReportGroupPagerGridModel reportPagerGridModel)
    {
        var allCustomers = Guid.Parse("00000000-0000-0000-0000-000000000001");
        var allBuyingGroups = Guid.Parse("00000000-0000-0000-0000-000000000002");
        var allSuppliers = Guid.Parse("00000000-0000-0000-0000-000000000003");

        var q = this.context.Reports
            .Where(r => r.ReportGroupId == reportGroupId)
            .Where(r => 
                r.ReportAccessLinks.Any(s => s.CustomerId == this.reportingUserModel.CustomerId) ||
                r.ReportAccessLinks.Any(s => s.BuyingGroupId == this.reportingUserModel.BuyingGroupId) ||
                r.ReportAccessLinks.Any(s => s.SupplierId == this.reportingUserModel.SupplierId) ||
                r.ReportAccessLinks.Any(s => s.CustomerId == allCustomers) ||
                r.ReportAccessLinks.Any(s => s.BuyingGroupId == allBuyingGroups) ||
                r.ReportAccessLinks.Any(s => s.SupplierId == allSuppliers)
            )
            .AsQueryable();

        var query = q.Select(r => new ReportModel
        {
            Id = r.Id,
            Description = r.Description,
            Colour = r.Colour,
            Icon = r.Icon,
            ReportGroupId = r.ReportGroupId,
            ReportJson = r.ReportJson,
            RoleType = (int)r.RoleType,
            TenantId = r.TenantId,
            IsAdminReport = r.IsAdminReport,
            IsBuyingGroupReport = r.IsBuyingGroupReport,
            IsSupplierReport = r.IsSupplierReport,
            IsCustomerReport = r.IsCustomerReport,
        }).AsQueryable();

        return await ComponentOperations.CreateGridAsync(query, reportPagerGridModel.PagerGridParameters);
    }

    public async Task<ReportModel> GetReportAsync(Guid id, Guid? reportGroupId)
    {
        var report = await this.context.Reports
            .Select(rg => new ReportModel
            {
                Id = rg.Id,
                Description = rg.Description,
                Colour = rg.Colour,
                Icon = rg.Icon,
                ReportGroupId = rg.ReportGroupId,
                ReportJson = rg.ReportJson,
                RoleType = (int)rg.RoleType,
                TenantId = rg.TenantId,
                MarketPlaceConnection = rg.MarketPlaceConnection,
                ETradingConnection = rg.ETradingConnection,
                ETradingReportsUserConnection = rg.ETradingReportsUserConnection,
                IsAdminReport = rg.IsAdminReport,
                IsBuyingGroupReport = rg.IsBuyingGroupReport,
                IsCustomerReport = rg.IsCustomerReport,
                IsSupplierReport = rg.IsSupplierReport,
            })
            .FirstOrDefaultAsync(rg => rg.Id == id);

        if (report == null)
        {
            // Create new report template
            report = new ReportModel 
            { 
                Id = Guid.Empty, 
                ReportGroupId = reportGroupId, 
                Icon = "IconAccount",
                Colour = "#007bff"
            };
        }

        return report;
    }

    public async Task<FormResponse<ReportModel>> SaveReportAsync(ReportModel reportModel)
    {
        var formResponse = new FormResponse<ReportModel>(true);

        // Validation
        if (string.IsNullOrEmpty(reportModel.Description))
        {
            formResponse.AddError("Please enter a Description", new FieldIdentifier("Description"));
        }

        if (string.IsNullOrEmpty(reportModel.ReportJson))
        {
            formResponse.AddError("Report configuration is required", new FieldIdentifier("ReportJson"));
        }

        if (!formResponse.Success)
            return formResponse;

        var report = await this.context.Reports.FirstOrDefaultAsync(rg => rg.Id == reportModel.Id);

        if (report == null)
        {
            // Create new report
            report = new Report
            {
                Id = Guid.NewGuid(),
                TenantId = reportModel.TenantId,
                ReportGroupId = reportModel.ReportGroupId.Value,
                CreatedDate = DateTime.UtcNow,
                CreatedBy = this.reportingUserModel.UserId
            };
            this.context.Reports.Add(report);
        }
        else
        {
            // Update existing report
            report.ModifiedDate = DateTime.UtcNow;
            report.ModifiedBy = this.reportingUserModel.UserId;
        }

        // Update report properties
        report.Description = reportModel.Description;
        report.Colour = reportModel.Colour;
        report.Icon = reportModel.Icon;
        report.ReportJson = reportModel.ReportJson;
        report.RoleType = (EnumRoleType)reportModel.RoleType;
        report.IsAdminReport = reportModel.IsAdminReport;
        report.IsBuyingGroupReport = reportModel.IsBuyingGroupReport;
        report.IsCustomerReport = reportModel.IsCustomerReport;
        report.IsSupplierReport = reportModel.IsSupplierReport;
        report.MarketPlaceConnection = reportModel.MarketPlaceConnection;
        report.ETradingConnection = reportModel.ETradingConnection;
        report.ETradingReportsUserConnection = reportModel.ETradingReportsUserConnection;

        await this.context.SaveChangesAsync();

        formResponse.Data = report.ToReportModel();
        return formResponse;
    }
}
```

## Data Visualisation Components

### Stimulsoft Integration Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        RV[Report Viewer]
        DD[Dashboard Designer]
        RD[Report Designer]
    end
    
    subgraph "Stimulsoft Framework"
        SE[Stimulsoft Engine]
        SV[StiNetCoreViewer]
        SD[StiNetCoreDesigner]
    end
    
    subgraph "Data Layer"
        DC[Data Connections]
        DS[Data Sources]
        DT[Data Transformations]
    end
    
    subgraph "Database Layer"
        ETD[ETrading Database]
        RPD[Reports Database]
        MPD[MarketPlace Database]
    end
    
    RV --> SV
    DD --> SD
    RD --> SD
    SV --> SE
    SD --> SE
    SE --> DC
    DC --> DS
    DS --> DT
    DT --> ETD
    DT --> RPD
    DT --> MPD
```

### Database Connection Management

```csharp
public class StimulsoftAddDatabase
{
    private readonly IConfiguration configuration;

    public StimulsoftAddDatabase(IConfiguration configuration)
    {
        this.configuration = configuration;
    }

    public void AddDatabase(StiReport report, UserService userService)
    {
        // Clear existing connections
        report.Dictionary.Databases.Clear();

        // Add ETrading database connection
        var eTradingDatabase = new StiSqlDatabase("ETrading", this.GetETradingConnectionString(userService));
        report.Dictionary.Databases.Add(eTradingDatabase);

        // Add Reports database connection
        var reportsDatabase = new StiSqlDatabase("Reports", this.GetReportsConnectionString(userService));
        report.Dictionary.Databases.Add(reportsDatabase);

        // Add MarketPlace database connection (if applicable)
        if (userService.HasMarketPlaceAccess)
        {
            var marketPlaceDatabase = new StiSqlDatabase("MarketPlace", this.GetMarketPlaceConnectionString(userService));
            report.Dictionary.Databases.Add(marketPlaceDatabase);
        }

        // Configure data security
        this.ConfigureDataSecurity(report, userService);
    }

    private string GetETradingConnectionString(UserService userService)
    {
        var baseConnectionString = this.configuration.GetConnectionString("ETrading");
        
        // Add user-specific security context
        return $"{baseConnectionString};Application Name=eHub-Reporting-{userService.UserId};";
    }

    private void ConfigureDataSecurity(StiReport report, UserService userService)
    {
        // Add tenant isolation parameters
        report.Dictionary.Variables.Add("TenantId", userService.TenantId.ToString());
        report.Dictionary.Variables.Add("CustomerId", userService.CustomerId?.ToString() ?? "");
        report.Dictionary.Variables.Add("SupplierId", userService.SupplierId?.ToString() ?? "");
        report.Dictionary.Variables.Add("BuyingGroupId", userService.BuyingGroupId?.ToString() ?? "");
        
        // Add role-based access parameters
        report.Dictionary.Variables.Add("IsAdmin", userService.IsAdmin.ToString());
        report.Dictionary.Variables.Add("IsCustomer", userService.IsCustomer.ToString());
        report.Dictionary.Variables.Add("IsSupplier", userService.IsSupplier.ToString());
        report.Dictionary.Variables.Add("IsMember", userService.IsMember.ToString());
    }
}
```

## Scheduled Reporting System

### Scheduled Report Configuration

```csharp
[Table("ScheduledReport", Schema = "Reporting")]
public class ScheduledReport
{
    [Key]
    public Guid Id { get; set; }
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int ClusteredId { get; set; }
    
    [Required]
    public Guid ReportId { get; set; }
    [Required]
    public Guid TenantId { get; set; }
    
    // Schedule configuration
    [Required]
    public string Name { get; set; }
    public string Description { get; set; }
    public EnumScheduleFrequency Frequency { get; set; }
    public string CronExpression { get; set; }
    public DateTime? NextRunDate { get; set; }
    public DateTime? LastRunDate { get; set; }
    
    // Output configuration
    public EnumReportFormat OutputFormat { get; set; }
    public string OutputPath { get; set; }
    public string EmailRecipients { get; set; }
    public string EmailSubject { get; set; }
    public string EmailBody { get; set; }
    
    // Status and control
    public bool IsActive { get; set; }
    public bool IsRunning { get; set; }
    public string LastRunStatus { get; set; }
    public string LastRunError { get; set; }
    
    // Relationships
    [ForeignKey(nameof(ReportId))]
    public Report Report { get; set; }
    
    public ICollection<ScheduledReportExecution> Executions { get; set; }
}

public enum EnumScheduleFrequency
{
    Daily,
    Weekly,
    Monthly,
    Quarterly,
    Annually,
    Custom
}

public enum EnumReportFormat
{
    PDF,
    Excel,
    Word,
    CSV,
    JSON,
    XML
}
```

### Report Scheduling Service

```csharp
public class ReportSchedulingService
{
    private readonly IBackgroundTaskQueue taskQueue;
    private readonly ReportService reportService;
    private readonly EmailService emailService;

    public async Task ScheduleReportAsync(ScheduledReport scheduledReport)
    {
        // Calculate next run date based on frequency
        scheduledReport.NextRunDate = this.CalculateNextRunDate(scheduledReport);
        
        // Queue the report for execution
        await this.taskQueue.QueueBackgroundWorkItemAsync(async token =>
        {
            await this.ExecuteScheduledReportAsync(scheduledReport.Id);
        });
    }

    public async Task ExecuteScheduledReportAsync(Guid scheduledReportId)
    {
        var scheduledReport = await this.context.ScheduledReports
            .Include(sr => sr.Report)
            .FirstOrDefaultAsync(sr => sr.Id == scheduledReportId);

        if (scheduledReport == null || !scheduledReport.IsActive)
            return;

        try
        {
            // Mark as running
            scheduledReport.IsRunning = true;
            scheduledReport.LastRunDate = DateTime.UtcNow;
            await this.context.SaveChangesAsync();

            // Generate the report
            var reportData = await this.GenerateReportAsync(scheduledReport.Report);
            
            // Export to specified format
            var exportedReport = await this.ExportReportAsync(reportData, scheduledReport.OutputFormat);
            
            // Save to output path if specified
            if (!string.IsNullOrEmpty(scheduledReport.OutputPath))
            {
                await this.SaveReportToPathAsync(exportedReport, scheduledReport.OutputPath);
            }
            
            // Email if recipients specified
            if (!string.IsNullOrEmpty(scheduledReport.EmailRecipients))
            {
                await this.EmailReportAsync(scheduledReport, exportedReport);
            }

            // Update execution status
            scheduledReport.LastRunStatus = "Success";
            scheduledReport.NextRunDate = this.CalculateNextRunDate(scheduledReport);
            
            // Log successful execution
            await this.LogReportExecutionAsync(scheduledReport, "Success", null);
        }
        catch (Exception ex)
        {
            // Update error status
            scheduledReport.LastRunStatus = "Failed";
            scheduledReport.LastRunError = ex.Message;
            
            // Log failed execution
            await this.LogReportExecutionAsync(scheduledReport, "Failed", ex.Message);
        }
        finally
        {
            // Mark as not running
            scheduledReport.IsRunning = false;
            await this.context.SaveChangesAsync();
        }
    }

    private DateTime CalculateNextRunDate(ScheduledReport scheduledReport)
    {
        var baseDate = scheduledReport.LastRunDate ?? DateTime.UtcNow;
        
        return scheduledReport.Frequency switch
        {
            EnumScheduleFrequency.Daily => baseDate.AddDays(1),
            EnumScheduleFrequency.Weekly => baseDate.AddDays(7),
            EnumScheduleFrequency.Monthly => baseDate.AddMonths(1),
            EnumScheduleFrequency.Quarterly => baseDate.AddMonths(3),
            EnumScheduleFrequency.Annually => baseDate.AddYears(1),
            EnumScheduleFrequency.Custom => this.CalculateFromCronExpression(scheduledReport.CronExpression, baseDate),
            _ => baseDate.AddDays(1)
        };
    }
}
```

## Analytics and Business Intelligence

### KPI Calculation Engine

```csharp
public class KPICalculationService
{
    public async Task<KPIResult> CalculateTransactionKPIsAsync(Guid customerId, DateTime fromDate, DateTime toDate)
    {
        var transactions = await this.context.TransactionHeaders
            .Where(t => t.CustomerId == customerId)
            .Where(t => t.TransactionDate >= fromDate && t.TransactionDate <= toDate)
            .ToListAsync();

        return new KPIResult
        {
            TotalTransactions = transactions.Count,
            TotalValue = transactions.Sum(t => t.Total ?? 0),
            AverageValue = transactions.Average(t => t.Total ?? 0),
            ProcessingTime = this.CalculateAverageProcessingTime(transactions),
            SuccessRate = this.CalculateSuccessRate(transactions),
            ExceptionRate = this.CalculateExceptionRate(transactions),
            TopSuppliers = this.GetTopSuppliers(transactions, 10),
            TrendData = this.CalculateTrendData(transactions, fromDate, toDate)
        };
    }

    private TimeSpan CalculateAverageProcessingTime(List<TransactionHeader> transactions)
    {
        var processedTransactions = transactions
            .Where(t => t.DateDocumentAdded.HasValue && t.ExportDate.HasValue)
            .ToList();

        if (!processedTransactions.Any())
            return TimeSpan.Zero;

        var totalProcessingTime = processedTransactions
            .Sum(t => (t.ExportDate.Value - t.DateDocumentAdded.Value).TotalMinutes);

        return TimeSpan.FromMinutes(totalProcessingTime / processedTransactions.Count);
    }

    private decimal CalculateSuccessRate(List<TransactionHeader> transactions)
    {
        if (!transactions.Any())
            return 0;

        var successfulTransactions = transactions.Count(t => t.TransactionStatus == "Completed");
        return (decimal)successfulTransactions / transactions.Count * 100;
    }
}
```

### Performance Monitoring

```csharp
public class ReportingPerformanceMonitor
{
    private readonly IMemoryCache cache;
    private readonly ILogger<ReportingPerformanceMonitor> logger;

    public async Task<T> ExecuteWithPerformanceMonitoringAsync<T>(string operationName, Func<Task<T>> operation)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            var result = await operation();
            stopwatch.Stop();
            
            this.logger.LogInformation("Operation {OperationName} completed in {ElapsedMilliseconds}ms", 
                operationName, stopwatch.ElapsedMilliseconds);
            
            // Cache performance metrics
            this.CachePerformanceMetric(operationName, stopwatch.ElapsedMilliseconds);
            
            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            
            this.logger.LogError(ex, "Operation {OperationName} failed after {ElapsedMilliseconds}ms", 
                operationName, stopwatch.ElapsedMilliseconds);
            
            throw;
        }
    }

    private void CachePerformanceMetric(string operationName, long elapsedMilliseconds)
    {
        var cacheKey = $"perf_metric_{operationName}";
        var metrics = this.cache.GetOrCreate(cacheKey, factory => new List<long>());
        
        metrics.Add(elapsedMilliseconds);
        
        // Keep only last 100 measurements
        if (metrics.Count > 100)
        {
            metrics.RemoveAt(0);
        }
        
        var cacheOptions = new MemoryCacheEntryOptions
        {
            SlidingExpiration = TimeSpan.FromHours(1)
        };
        
        this.cache.Set(cacheKey, metrics, cacheOptions);
    }
}
```

## Multi-Tenant Reporting Security

### Access Control Implementation

```csharp
public class ReportingSecurityService
{
    public async Task<bool> ValidateReportAccessAsync(Guid reportId, UserService userService)
    {
        var report = await this.context.Reports
            .Include(r => r.ReportAccessLinks)
            .FirstOrDefaultAsync(r => r.Id == reportId);

        if (report == null)
            return false;

        // Check tenant isolation
        if (report.TenantId != userService.TenantId)
            return false;

        // Check role-based access
        if (!this.ValidateRoleAccess(report, userService))
            return false;

        // Check specific access links
        return this.ValidateAccessLinks(report.ReportAccessLinks, userService);
    }

    private bool ValidateRoleAccess(Report report, UserService userService)
    {
        return report.RoleType switch
        {
            EnumRoleType.Admin => userService.IsAdmin,
            EnumRoleType.Customer => userService.IsCustomer,
            EnumRoleType.Supplier => userService.IsSupplier,
            EnumRoleType.BuyingGroup => userService.IsBuyingGroup,
            EnumRoleType.Member => userService.IsMember,
            _ => false
        };
    }

    private bool ValidateAccessLinks(ICollection<ReportAccessLink> accessLinks, UserService userService)
    {
        if (!accessLinks.Any())
            return true; // No specific restrictions

        return accessLinks.Any(link =>
            (link.CustomerId.HasValue && link.CustomerId == userService.CustomerId) ||
            (link.SupplierId.HasValue && link.SupplierId == userService.SupplierId) ||
            (link.BuyingGroupId.HasValue && link.BuyingGroupId == userService.BuyingGroupId));
    }
}
```

## Related Documents

- **[SystemOverview.md](./SystemOverview.md)** - High-level system architecture
- **[DatabaseArchitecture.md](./DatabaseArchitecture.md)** - Database design and entities
- **[SecurityArchitecture.md](./SecurityArchitecture.md)** - Security implementation details
- **[CustomerAndSupplierManagement.md](./CustomerAndSupplierManagement.md)** - Customer configuration and management
- **[TransactionProcessing.md](./TransactionProcessing.md)** - Transaction processing and business data

---

*This document provides comprehensive coverage of the eHub reporting and analytics system. Refer to the related documentation for detailed implementation guidance on specific components.*
