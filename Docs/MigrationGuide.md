# eHub Migration Guide

## Purpose & Scope

This document provides comprehensive guidance for database migrations and data migration procedures in the eHub electronic trading platform. It covers Entity Framework migrations, database schema changes, data transformation procedures, version control strategies, rollback mechanisms, and best practices for production deployments. This guide is essential for system administrators, DevOps teams, and developers responsible for maintaining database integrity during system evolution.

## Prerequisites

- **Entity Framework Core Knowledge**: Understanding of EF Core migrations and database contexts
- **SQL Server Administration**: Familiarity with SQL Server management and T-SQL
- **Version Control**: Knowledge of Git branching and merge strategies
- **Database Backup/Restore**: Understanding of SQL Server backup and recovery procedures
- **Production Deployment**: Experience with production deployment processes and change management

## Core Concepts

### Migration Architecture Overview

eHub implements a multi-context migration strategy to manage the complex database architecture across multiple bounded contexts:

```mermaid
graph TB
    subgraph "Migration Contexts"
        MAIN[MainContext<br/>Core Trading Data]
        DATASTORE[DataStoreContext<br/>Transaction Storage]
        AUTH[ECXAuth Context<br/>Authentication Data]
        AUDIT[ECXAudit Context<br/>Audit Trails]
        IO[ECXIO Context<br/>Integration Data]
        BG[Background Context<br/>Job Management]
        REPORT[Reporting Context<br/>Analytics Data]
    end
    
    subgraph "Migration Process"
        DEV[Development Migration]
        TEST[Testing & Validation]
        SCRIPT[Script Generation]
        PROD[Production Deployment]
    end
    
    subgraph "Migration Types"
        SCHEMA[Schema Changes<br/>Tables, Columns, Indexes]
        DATA[Data Migrations<br/>Transformations, Seeding]
        PROC[Stored Procedures<br/>Functions, Views]
        SEED[Reference Data<br/>Configuration, Lookups]
    end
    
    MAIN --> DEV
    DATASTORE --> DEV
    AUTH --> DEV
    AUDIT --> DEV
    IO --> DEV
    BG --> DEV
    REPORT --> DEV
    
    DEV --> TEST
    TEST --> SCRIPT
    SCRIPT --> PROD
    
    SCHEMA --> DEV
    DATA --> DEV
    PROC --> DEV
    SEED --> DEV
```

### Migration Strategy Principles

#### 1. Multi-Context Coordination
- **Ordered Execution**: Migrations execute in dependency order
- **Context Isolation**: Each context manages its own schema
- **Cross-Context References**: Careful handling of foreign key relationships
- **Atomic Operations**: All contexts migrate successfully or rollback

#### 2. Backwards Compatibility
- **Additive Changes**: Prefer adding new structures over modifying existing
- **Graceful Degradation**: Ensure older application versions can still function
- **Data Preservation**: Never lose data during migrations
- **Rollback Safety**: All migrations must be reversible

#### 3. Production Safety
- **Backup First**: Always backup before migration
- **Maintenance Windows**: Schedule migrations during low-usage periods
- **Validation**: Comprehensive testing before production deployment
- **Monitoring**: Real-time monitoring during migration execution

## Entity Framework Migration Management

### Development Migration Workflow

#### Creating New Migrations

```bash
# Navigate to the appropriate project directory
cd DataStore

# Add a new migration with descriptive name
dotnet ef migrations add AddCustomerPreferencesTable --startup-project ../ETradingAPI

# Review the generated migration files
# - YYYYMMDDHHMMSS_AddCustomerPreferencesTable.cs
# - YYYYMMDDHHMMSS_AddCustomerPreferencesTable.Designer.cs

# Apply migration to development database
dotnet ef database update --startup-project ../ETradingAPI
```

#### Migration Naming Conventions

```
Pattern: [Action][EntityName][Description]
Examples:
- Add[TableName]Table
- Update[ColumnName]Column
- Remove[IndexName]Index
- Migrate[DataType]Data
- Fix[IssueDescription]Issue

Good Examples:
- AddCustomerPreferencesTable
- UpdateTransactionStatusColumn
- RemoveObsoleteIndexes
- MigrateEmailTemplateData
- FixDuplicateCustomerCodes

Poor Examples:
- Changes
- Update
- Fix
- NewStuff
```

#### Multi-Context Migration Example

```csharp
// Example migration affecting multiple contexts
public partial class AddCustomerIntegrationSettings : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        // 1. Add new table to Customer schema
        migrationBuilder.CreateTable(
            name: "IntegrationSettings",
            schema: "Customer",
            columns: table => new
            {
                Id = table.Column<Guid>(nullable: false),
                CustomerId = table.Column<Guid>(nullable: false),
                IntegrationType = table.Column<string>(maxLength: 50, nullable: false),
                Configuration = table.Column<string>(type: "nvarchar(max)", nullable: true),
                IsEnabled = table.Column<bool>(nullable: false, defaultValue: true),
                CreatedDate = table.Column<DateTime>(nullable: false, defaultValueSql: "GETUTCDATE()"),
                ModifiedDate = table.Column<DateTime>(nullable: false, defaultValueSql: "GETUTCDATE()")
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_IntegrationSettings", x => x.Id);
                table.ForeignKey(
                    name: "FK_IntegrationSettings_CustomerDetail_CustomerId",
                    column: x => x.CustomerId,
                    principalSchema: "Customer",
                    principalTable: "MainDetail",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
            });

        // 2. Create indexes for performance
        migrationBuilder.CreateIndex(
            name: "IX_IntegrationSettings_CustomerId",
            schema: "Customer",
            table: "IntegrationSettings",
            column: "CustomerId");

        migrationBuilder.CreateIndex(
            name: "IX_IntegrationSettings_IntegrationType",
            schema: "Customer",
            table: "IntegrationSettings",
            column: "IntegrationType");

        // 3. Seed default data
        migrationBuilder.Sql(@"
            INSERT INTO [Customer].[IntegrationSettings] 
            ([Id], [CustomerId], [IntegrationType], [Configuration], [IsEnabled])
            SELECT 
                NEWID(),
                [Id],
                'AS2',
                '{}',
                0
            FROM [Customer].[MainDetail]
            WHERE [IsActive] = 1
        ");
    }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropTable(
            name: "IntegrationSettings",
            schema: "Customer");
    }
}
```

### Advanced Migration Patterns

#### Data Transformation Migrations

```csharp
public partial class MigrateTransactionStatusEnum : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        // 1. Add new column with new enum values
        migrationBuilder.AddColumn<int>(
            name: "StatusNew",
            schema: "eTrading",
            table: "Transaction",
            nullable: true);

        // 2. Migrate existing data
        migrationBuilder.Sql(@"
            UPDATE [eTrading].[Transaction] 
            SET [StatusNew] = CASE 
                WHEN [Status] = 0 THEN 1  -- Draft -> Pending
                WHEN [Status] = 1 THEN 2  -- Processing -> InProgress
                WHEN [Status] = 2 THEN 3  -- Completed -> Approved
                WHEN [Status] = 3 THEN 4  -- Failed -> Rejected
                ELSE 1  -- Default to Pending
            END
        ");

        // 3. Make new column non-nullable
        migrationBuilder.AlterColumn<int>(
            name: "StatusNew",
            schema: "eTrading",
            table: "Transaction",
            nullable: false);

        // 4. Drop old column
        migrationBuilder.DropColumn(
            name: "Status",
            schema: "eTrading",
            table: "Transaction");

        // 5. Rename new column
        migrationBuilder.RenameColumn(
            name: "StatusNew",
            schema: "eTrading",
            table: "Transaction",
            newName: "Status");
    }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
        // Reverse the process
        migrationBuilder.RenameColumn(
            name: "Status",
            schema: "eTrading",
            table: "Transaction",
            newName: "StatusNew");

        migrationBuilder.AddColumn<int>(
            name: "Status",
            schema: "eTrading",
            table: "Transaction",
            nullable: true);

        migrationBuilder.Sql(@"
            UPDATE [eTrading].[Transaction] 
            SET [Status] = CASE 
                WHEN [StatusNew] = 1 THEN 0  -- Pending -> Draft
                WHEN [StatusNew] = 2 THEN 1  -- InProgress -> Processing
                WHEN [StatusNew] = 3 THEN 2  -- Approved -> Completed
                WHEN [StatusNew] = 4 THEN 3  -- Rejected -> Failed
                ELSE 0  -- Default to Draft
            END
        ");

        migrationBuilder.AlterColumn<int>(
            name: "Status",
            schema: "eTrading",
            table: "Transaction",
            nullable: false);

        migrationBuilder.DropColumn(
            name: "StatusNew",
            schema: "eTrading",
            table: "Transaction");
    }
}
```

#### Stored Procedure Migrations

```csharp
public partial class UpdateReportingStoredProcedures : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        // Load SQL script from embedded resource
        var assembly = Assembly.GetExecutingAssembly();
        var resourceName = assembly.GetManifestResourceNames()
            .First(s => s.EndsWith("UpdateTransactionReportingProcedure_Up.sql"));

        using (var stream = assembly.GetManifestResourceStream(resourceName))
        using (var reader = new StreamReader(stream))
        {
            var sql = reader.ReadToEnd();
            migrationBuilder.Sql(sql);
        }
    }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
        var assembly = Assembly.GetExecutingAssembly();
        var resourceName = assembly.GetManifestResourceNames()
            .First(s => s.EndsWith("UpdateTransactionReportingProcedure_Down.sql"));

        using (var stream = assembly.GetManifestResourceStream(resourceName))
        using (var reader = new StreamReader(stream))
        {
            var sql = reader.ReadToEnd();
            migrationBuilder.Sql(sql);
        }
    }
}
```

## Production Migration Procedures

### Pre-Migration Checklist

#### 1. Environment Preparation
```bash
# Verify database connectivity
sqlcmd -S production-server -E -Q "SELECT @@VERSION"

# Check available disk space
sqlcmd -S production-server -E -Q "
    SELECT 
        DB_NAME() as DatabaseName,
        (size * 8.0 / 1024) as SizeMB,
        (FILEPROPERTY(name, 'SpaceUsed') * 8.0 / 1024) as UsedMB,
        ((size - FILEPROPERTY(name, 'SpaceUsed')) * 8.0 / 1024) as FreeMB
    FROM sys.database_files
"

# Verify backup location accessibility
dir \\backup-server\sql-backups\
```

#### 2. Migration Script Generation
```bash
# Generate migration scripts for each context
dotnet ef migrations script --startup-project ETradingAPI --context MainContext --output ./deploy/scripts/main-context-migration.sql

dotnet ef migrations script --startup-project ETradingAPI --context DataStoreContext --output ./deploy/scripts/datastore-migration.sql

dotnet ef migrations script --startup-project ETradingAPI --context ECXAuthContext --output ./deploy/scripts/auth-migration.sql

dotnet ef migrations script --startup-project ETradingAPI --context ECXAuditContext --output ./deploy/scripts/audit-migration.sql

dotnet ef migrations script --startup-project ETradingAPI --context ECXIOContext --output ./deploy/scripts/io-migration.sql

dotnet ef migrations script --startup-project ETradingAPI --context BackgroundContext --output ./deploy/scripts/background-migration.sql

dotnet ef migrations script --startup-project ETradingAPI --context ReportingContext --output ./deploy/scripts/reporting-migration.sql
```

#### 3. Migration Validation
```bash
# Validate migration scripts syntax
sqlcmd -S test-server -d eHub_Test -i main-context-migration.sql -o validation-output.txt

# Check for potential issues
grep -i "error\|warning\|failed" validation-output.txt

# Verify migration on test environment
dotnet ef database update --startup-project ETradingAPI --connection-string "Server=test-server;Database=eHub_Test;..."
```

### Production Migration Execution

#### Migration Deployment Script
```powershell
# Production Migration Deployment Script
param(
    [Parameter(Mandatory=$true)]
    [string]$ServerName,
    
    [Parameter(Mandatory=$true)]
    [string]$DatabaseName,
    
    [Parameter(Mandatory=$false)]
    [string]$BackupLocation = "C:\Backup\"
)

$ErrorActionPreference = "Stop"
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$backupFile = "$BackupLocation$DatabaseName`_pre_migration_$timestamp.bak"

try {
    Write-Host "Starting migration deployment for $DatabaseName on $ServerName" -ForegroundColor Green
    
    # 1. Create pre-migration backup
    Write-Host "Creating pre-migration backup..." -ForegroundColor Yellow
    sqlcmd -S $ServerName -E -Q "BACKUP DATABASE [$DatabaseName] TO DISK = '$backupFile' WITH COMPRESSION, CHECKSUM"
    
    if ($LASTEXITCODE -ne 0) {
        throw "Backup failed with exit code $LASTEXITCODE"
    }
    
    # 2. Verify backup
    Write-Host "Verifying backup integrity..." -ForegroundColor Yellow
    sqlcmd -S $ServerName -E -Q "RESTORE VERIFYONLY FROM DISK = '$backupFile'"
    
    if ($LASTEXITCODE -ne 0) {
        throw "Backup verification failed with exit code $LASTEXITCODE"
    }
    
    # 3. Execute migrations in order
    $migrationScripts = @(
        "auth-migration.sql",
        "audit-migration.sql", 
        "background-migration.sql",
        "io-migration.sql",
        "datastore-migration.sql",
        "main-context-migration.sql",
        "reporting-migration.sql"
    )
    
    foreach ($script in $migrationScripts) {
        if (Test-Path "./deploy/scripts/$script") {
            Write-Host "Executing $script..." -ForegroundColor Yellow
            sqlcmd -S $ServerName -d $DatabaseName -i "./deploy/scripts/$script" -o "./logs/$script.log"
            
            if ($LASTEXITCODE -ne 0) {
                throw "Migration script $script failed with exit code $LASTEXITCODE"
            }
        }
    }
    
    # 4. Verify migration success
    Write-Host "Verifying migration completion..." -ForegroundColor Yellow
    dotnet ef database update --startup-project ETradingAPI --connection-string "Server=$ServerName;Database=$DatabaseName;Integrated Security=true;" --dry-run
    
    # 5. Run post-migration validation
    Write-Host "Running post-migration validation..." -ForegroundColor Yellow
    sqlcmd -S $ServerName -d $DatabaseName -i "./deploy/scripts/post-migration-validation.sql" -o "./logs/validation.log"
    
    Write-Host "Migration completed successfully!" -ForegroundColor Green
    
} catch {
    Write-Host "Migration failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Backup available at: $backupFile" -ForegroundColor Yellow
    
    # Optionally trigger rollback
    $rollback = Read-Host "Do you want to rollback to the pre-migration backup? (y/N)"
    if ($rollback -eq "y" -or $rollback -eq "Y") {
        Write-Host "Rolling back to pre-migration backup..." -ForegroundColor Yellow
        sqlcmd -S $ServerName -E -Q "RESTORE DATABASE [$DatabaseName] FROM DISK = '$backupFile' WITH REPLACE"
    }
    
    exit 1
}
```

### Automated Migration Tool

```csharp
public class MigrationManager
{
    private readonly IConfiguration configuration;
    private readonly ILogger<MigrationManager> logger;
    
    public MigrationManager(IConfiguration configuration, ILogger<MigrationManager> logger)
    {
        this.configuration = configuration;
        this.logger = logger;
    }
    
    public async Task<bool> ExecuteAllMigrationsAsync()
    {
        var connectionString = configuration.GetConnectionString("ETrading");
        var contexts = GetMigrationContexts(connectionString);
        
        foreach (var context in contexts)
        {
            try
            {
                logger.LogInformation($"Migrating {context.GetType().Name}...");
                
                using (context)
                {
                    await context.Database.MigrateAsync();
                    
                    // Execute post-migration seeding if applicable
                    if (context is ISeededContext seededContext)
                    {
                        await seededContext.SeedDataAsync();
                    }
                }
                
                logger.LogInformation($"Successfully migrated {context.GetType().Name}");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Failed to migrate {context.GetType().Name}");
                return false;
            }
        }
        
        return true;
    }
    
    private List<DbContext> GetMigrationContexts(string connectionString)
    {
        return new List<DbContext>
        {
            new ECXAuth.Database.DatabaseContext(connectionString),
            new ECXAudit.Database.DatabaseContext(connectionString),
            new ECXBackgroundService.Database.DatabaseContextBackGround(connectionString, true),
            new ECXIO.Core.Database.DatabaseContext(connectionString, true),
            new DataStore.Database.DataStoreContext(connectionString, true),
            new ETrading.Database.MainContext(connectionString, true),
            new Reporting.Database.DatabaseContext(connectionString)
        };
    }
}
```

## Data Migration Strategies

### Large Dataset Migration

#### Chunked Data Migration
```csharp
public class ChunkedDataMigrator
{
    private const int BATCH_SIZE = 1000;
    
    public async Task MigrateCustomerDataAsync(DbContext context)
    {
        var totalRecords = await context.Database
            .SqlQueryRaw<int>("SELECT COUNT(*) FROM [Customer].[MainDetail]")
            .FirstAsync();
            
        var batches = (int)Math.Ceiling((double)totalRecords / BATCH_SIZE);
        
        for (int batch = 0; batch < batches; batch++)
        {
            var offset = batch * BATCH_SIZE;
            
            await context.Database.ExecuteSqlRawAsync(@"
                UPDATE [Customer].[MainDetail] 
                SET [ModernizedField] = [LegacyField] + '_MIGRATED'
                WHERE [Id] IN (
                    SELECT [Id] 
                    FROM [Customer].[MainDetail] 
                    WHERE [ModernizedField] IS NULL
                    ORDER BY [Id]
                    OFFSET {0} ROWS 
                    FETCH NEXT {1} ROWS ONLY
                )", offset, BATCH_SIZE);
                
            // Log progress
            var progress = (double)(batch + 1) / batches * 100;
            Console.WriteLine($"Migration progress: {progress:F1}%");
            
            // Small delay to prevent overwhelming the database
            await Task.Delay(100);
        }
    }
}
```

#### Zero-Downtime Migration Pattern
```csharp
public class ZeroDowntimeMigrator
{
    public async Task MigrateWithZeroDowntimeAsync()
    {
        // Phase 1: Add new columns/tables alongside existing ones
        await AddNewStructuresAsync();
        
        // Phase 2: Start dual-write to both old and new structures
        await EnableDualWriteAsync();
        
        // Phase 3: Backfill historical data
        await BackfillHistoricalDataAsync();
        
        // Phase 4: Switch reads to new structure
        await SwitchReadsToNewStructureAsync();
        
        // Phase 5: Stop writing to old structure
        await DisableOldStructureWritesAsync();
        
        // Phase 6: Remove old structure (in future migration)
        // await RemoveOldStructuresAsync();
    }
}
```

## Rollback Procedures

### Automatic Rollback Triggers

```sql
-- Post-migration validation script
DECLARE @ErrorCount INT = 0;

-- Check for data integrity issues
IF NOT EXISTS (SELECT 1 FROM [Customer].[MainDetail] WHERE [IsActive] = 1)
BEGIN
    SET @ErrorCount = @ErrorCount + 1;
    PRINT 'ERROR: No active customers found after migration';
END

-- Check for orphaned records
IF EXISTS (
    SELECT 1 FROM [eTrading].[Transaction] t
    LEFT JOIN [Customer].[MainDetail] c ON t.CustomerId = c.Id
    WHERE c.Id IS NULL
)
BEGIN
    SET @ErrorCount = @ErrorCount + 1;
    PRINT 'ERROR: Orphaned transactions found after migration';
END

-- Check for performance regression
DECLARE @AvgResponseTime FLOAT;
SELECT @AvgResponseTime = AVG(DATEDIFF(ms, StartTime, EndTime))
FROM [Audit].[PerformanceLog]
WHERE LogDate > DATEADD(minute, -10, GETUTCDATE());

IF @AvgResponseTime > 5000  -- 5 second threshold
BEGIN
    SET @ErrorCount = @ErrorCount + 1;
    PRINT 'ERROR: Performance regression detected after migration';
END

-- Return error count for automated processing
SELECT @ErrorCount as ErrorCount;

IF @ErrorCount > 0
BEGIN
    PRINT 'Migration validation failed. Consider rollback.';
    -- Could trigger automatic rollback here
END
ELSE
BEGIN
    PRINT 'Migration validation passed successfully.';
END
```

### Manual Rollback Procedures

```bash
#!/bin/bash
# Manual rollback script

SERVER_NAME="production-server"
DATABASE_NAME="eHub_Production"
BACKUP_FILE="$1"

if [ -z "$BACKUP_FILE" ]; then
    echo "Usage: $0 <backup-file-path>"
    exit 1
fi

echo "WARNING: This will rollback the database to a previous state."
echo "Database: $DATABASE_NAME on $SERVER_NAME"
echo "Backup file: $BACKUP_FILE"
read -p "Are you sure you want to proceed? (yes/no): " confirm

if [ "$confirm" != "yes" ]; then
    echo "Rollback cancelled."
    exit 0
fi

echo "Starting rollback process..."

# 1. Stop application services
echo "Stopping application services..."
systemctl stop ehub-api
systemctl stop ehub-hangfire

# 2. Set database to single user mode
echo "Setting database to single user mode..."
sqlcmd -S $SERVER_NAME -E -Q "ALTER DATABASE [$DATABASE_NAME] SET SINGLE_USER WITH ROLLBACK IMMEDIATE"

# 3. Restore from backup
echo "Restoring database from backup..."
sqlcmd -S $SERVER_NAME -E -Q "RESTORE DATABASE [$DATABASE_NAME] FROM DISK = '$BACKUP_FILE' WITH REPLACE"

if [ $? -ne 0 ]; then
    echo "ERROR: Database restore failed!"
    exit 1
fi

# 4. Set database back to multi user mode
echo "Setting database to multi user mode..."
sqlcmd -S $SERVER_NAME -E -Q "ALTER DATABASE [$DATABASE_NAME] SET MULTI_USER"

# 5. Start application services
echo "Starting application services..."
systemctl start ehub-api
systemctl start ehub-hangfire

# 6. Verify functionality
echo "Verifying system functionality..."
curl -f https://api.ehub.com/health

if [ $? -eq 0 ]; then
    echo "Rollback completed successfully!"
else
    echo "WARNING: Health check failed after rollback. Manual intervention required."
fi
```

## Version Control and Branching

### Migration Branching Strategy

```mermaid
gitGraph
    commit id: "Initial"
    branch feature/customer-preferences
    checkout feature/customer-preferences
    commit id: "Add migration"
    commit id: "Test migration"
    checkout main
    commit id: "Other changes"
    merge feature/customer-preferences
    commit id: "Deploy to staging"
    branch hotfix/urgent-fix
    checkout hotfix/urgent-fix
    commit id: "Hotfix migration"
    checkout main
    merge hotfix/urgent-fix
    commit id: "Deploy to production"
```

### Migration Conflict Resolution

```bash
# When migration conflicts occur during merge
git status
# Shows: both modified: ETradingEF/Database/Migrations/...

# 1. Remove conflicting migration files
rm ETradingEF/Database/Migrations/20240115_ConflictingMigration.cs
rm ETradingEF/Database/Migrations/20240115_ConflictingMigration.Designer.cs

# 2. Reset database to last known good state
dotnet ef database update PreviousGoodMigration --startup-project ETradingAPI

# 3. Create new migration that combines changes
dotnet ef migrations add CombinedChanges --startup-project ETradingAPI

# 4. Test the combined migration
dotnet ef database update --startup-project ETradingAPI

# 5. Commit the resolved migration
git add .
git commit -m "Resolve migration conflict: combine customer and workflow changes"
```

## Best Practices

### Migration Development

#### 1. Migration Design Principles
- **Atomic Operations**: Each migration should be a complete, atomic change
- **Idempotent**: Migrations should be safe to run multiple times
- **Backwards Compatible**: Avoid breaking changes when possible
- **Performance Conscious**: Consider impact on large tables
- **Well Documented**: Include clear comments explaining complex changes

#### 2. Testing Strategy
```csharp
[Test]
public async Task Migration_ShouldPreserveDataIntegrity()
{
    // Arrange: Set up test data before migration
    using var context = new TestDbContext();
    var testCustomer = new Customer { Name = "Test Customer", IsActive = true };
    context.Customers.Add(testCustomer);
    await context.SaveChangesAsync();
    
    // Act: Apply migration
    await context.Database.MigrateAsync();
    
    // Assert: Verify data integrity
    var migratedCustomer = await context.Customers.FindAsync(testCustomer.Id);
    Assert.That(migratedCustomer, Is.Not.Null);
    Assert.That(migratedCustomer.Name, Is.EqualTo("Test Customer"));
    Assert.That(migratedCustomer.IsActive, Is.True);
}
```

#### 3. Performance Considerations
```sql
-- Use appropriate indexing during large data migrations
CREATE NONCLUSTERED INDEX IX_Migration_Temp 
ON [Customer].[MainDetail] ([LegacyField])
INCLUDE ([Id], [Name])

-- Perform migration in batches
DECLARE @BatchSize INT = 1000;
DECLARE @RowsAffected INT = 1;

WHILE @RowsAffected > 0
BEGIN
    UPDATE TOP (@BatchSize) [Customer].[MainDetail]
    SET [NewField] = [LegacyField] + '_MIGRATED'
    WHERE [NewField] IS NULL;
    
    SET @RowsAffected = @@ROWCOUNT;
    
    -- Small delay to prevent blocking
    WAITFOR DELAY '00:00:01';
END

-- Remove temporary index
DROP INDEX IX_Migration_Temp ON [Customer].[MainDetail]
```

### Production Deployment

#### 1. Change Management Process
- **Migration Review**: All migrations must be peer-reviewed
- **Testing Requirements**: Migrations must pass automated and manual testing
- **Approval Process**: Production migrations require approval from senior developers
- **Documentation**: All migrations must be documented with business justification
- **Rollback Plan**: Every migration must have a tested rollback procedure

#### 2. Monitoring and Alerting
```csharp
public class MigrationMonitor
{
    public async Task MonitorMigrationAsync(string migrationName)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            // Execute migration with monitoring
            await ExecuteMigrationWithMonitoringAsync(migrationName);
            
            stopwatch.Stop();
            
            // Log successful completion
            Logger.Information("Migration {MigrationName} completed in {Duration}ms", 
                migrationName, stopwatch.ElapsedMilliseconds);
                
            // Send success notification
            await NotificationService.SendSuccessNotificationAsync(migrationName, stopwatch.Elapsed);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            
            // Log failure
            Logger.Error(ex, "Migration {MigrationName} failed after {Duration}ms", 
                migrationName, stopwatch.ElapsedMilliseconds);
                
            // Send failure alert
            await AlertService.SendCriticalAlertAsync($"Migration {migrationName} failed", ex);
            
            throw;
        }
    }
}
```

## Related Documents

- **[DatabaseArchitecture.md](./DatabaseArchitecture.md)** - Database design and architecture patterns
- **[BuildAndDeployment.md](./BuildAndDeployment.md)** - Build processes and deployment procedures
- **[MaintenanceAndOperations.md](./MaintenanceAndOperations.md)** - Operational procedures and monitoring
- **[BackupAndRecovery.md](./BackupAndRecovery.md)** - Data protection and disaster recovery procedures
- **[DeveloperQuickStart.md](./DeveloperQuickStart.md)** - Development environment setup and workflows
- **[TroubleshootingGuide.md](./TroubleshootingGuide.md)** - Common issues and resolution procedures

---

*This migration guide provides comprehensive guidance for managing database changes in the eHub platform. Proper migration management ensures system reliability, data integrity, and smooth evolution of the platform while maintaining production stability.*
