# eHub File System and Storage Architecture

## Purpose & Scope

This document provides comprehensive documentation of the eHub file system and storage architecture, including Azure Storage integration, file management systems, document handling processes, and storage security patterns. It serves as the definitive guide for understanding and working with the eHub storage layer.

## Prerequisites

- Understanding of Azure Storage services and blob storage
- Knowledge of file system operations and document management
- Familiarity with .NET file I/O patterns
- Basic understanding of eHub document processing workflows

## Core Concepts

### Storage Architecture Philosophy

The eHub storage architecture is built on several key principles:

1. **Cloud-First Design**: Azure Storage as the primary storage backend
2. **Container-Based Organisation**: Logical separation of document types and customers
3. **Scalable File Management**: Handles millions of documents with enterprise-grade reliability
4. **Security by Design**: Encrypted storage with access control and audit trails
5. **Performance Optimisation**: Efficient file operations with caching and streaming

### Storage Overview

```
eHub Storage Architecture
├── Azure Blob Storage (Primary)
│   ├── Document Containers - Customer-specific document storage
│   ├── Certificate Containers - Security certificates and keys
│   ├── Output Containers - Processed document outputs
│   └── Archive Containers - Long-term document retention
├── File System Abstraction Layer
│   ├── AzureStorage - Core Azure operations
│   ├── AzureReader - File reading operations
│   ├── AzureWriter - File writing operations
│   └── File Management - Upload, download, move, delete
└── Document Processing Pipeline
    ├── Ingestion - Multi-source document intake
    ├── Processing - Document transformation and extraction
    ├── Storage - Persistent document storage
    └── Retrieval - Efficient document access
```

## Azure Storage Integration

### Core Storage Services

**Primary Storage Account Configuration**:
```json
{
  "sku": {
    "name": "Standard_RAGRS",
    "tier": "Standard"
  },
  "kind": "StorageV2",
  "properties": {
    "minimumTlsVersion": "TLS1_2",
    "allowBlobPublicAccess": false,
    "supportsHttpsTrafficOnly": true,
    "encryption": {
      "services": {
        "blob": { "enabled": true },
        "file": { "enabled": true }
      }
    },
    "accessTier": "Hot"
  }
}
```

**Key Storage Features**:
- **Geo-Redundant Storage**: `Standard_RAGRS` for high availability
- **Security**: TLS 1.2 minimum, HTTPS-only traffic, private blob access
- **Performance**: Hot access tier for frequently accessed documents
- **Encryption**: Server-side encryption for data at rest

### Storage Account Architecture

```mermaid
graph TB
    subgraph "Azure Storage Accounts"
        PSA[Primary Storage Account<br/>ehub]
        FSA[FTP Storage Account<br/>etradingftp]
        CSA[Certificate Storage<br/>certificates]
    end
    
    subgraph "Container Structure"
        PSA --> DC[Document Containers]
        PSA --> OC[Output Containers]
        PSA --> AC[Archive Containers]
        FSA --> FC[FTP Containers]
        CSA --> CC[Certificate Containers]
    end
    
    subgraph "Access Patterns"
        DC --> DR[Document Reading]
        DC --> DW[Document Writing]
        OC --> OR[Output Reading]
        OC --> OW[Output Writing]
    end
    
    subgraph "Security Layer"
        DR --> SAS[SAS Tokens]
        DW --> SAS
        OR --> SAS
        OW --> SAS
        SAS --> AAD[Azure AD Integration]
    end
```

## File Management Systems

### AzureStorage Core Class

**Location**: `ECXFileSystem/AzureStorage.cs`

**Primary Responsibilities**:
- Blob storage operations (upload, download, delete, move)
- Container management and initialisation
- File existence checking and metadata operations
- Stream-based file operations for large documents

**Key Methods**:
```csharp
public class AzureStorage : IAzureStorage
{
    // Upload operations
    Task UploadFromByteArrayAsync(byte[] bytes, string contentType, string containerName, string filename)
    Task UploadFileToBlobAsyn(Stream file, string contentType, string containerName, string filename)
    
    // Download operations
    Task<Stream> DownloadBlobAsync(string filePath)
    Task<Stream> DownloadAppendBlobAsync(string filePath)
    
    // Management operations
    Task<bool> CheckIfBlobExistsAsync(string filePath, string containerName)
    Task DeleteBlob(string containerName, string fileName)
    Task MoveBlob(string sourcePath, string destinationPath)
    
    // Container operations
    Task<List<CloudBlob>> GetAllBlobsInContainer(string containerName, string prefix)
}
```

### File System Abstraction Layer

#### AzureReader - File Reading Operations

**Location**: `ECXFileSystem/Reader/AzureReader.cs`

**Capabilities**:
- Text file reading with encoding support
- Binary file streaming for large documents
- Directory listing and file enumeration
- Caching layer for frequently accessed files
- Asynchronous operations for performance

**Usage Patterns**:
```csharp
public class AzureReader : IReader
{
    // Text operations
    Task<string> ReadAllTextAsync(string pathName)
    Task<string> ReadAllTextWithCacheAsync(string url)
    
    // Binary operations
    MemoryStream GetMemoryStream(string pathName)
    Task<byte[]> ReadAllBytesAsync(string pathName)
    
    // Directory operations
    Task<IReadOnlyCollection<string>> ListFilesAsync(string directory)
    IFileInfo GetFileInfo(string filePath)
}
```

#### AzureWriter - File Writing Operations

**Location**: `ECXFileSystem/Writer/AzureWriter.cs`

**Capabilities**:
- Text and binary file writing
- Stream-based operations for large files
- Atomic file operations with error handling
- Content type detection and setting
- Path normalisation and container management

**Usage Patterns**:
```csharp
public class AzureWriter : IWriter
{
    // Text operations
    Task WriteAsync(string filePath, string content)
    void Write(string filePath, string content)
    
    // Binary operations
    Task WriteAsync(string filePath, MemoryStream memoryStream)
    void WriteNotAsync(string filePath, FileStream fileStream)
    
    // File management
    Task MoveFile(string sourcePath, string destinationPath)
    void DeleteFile(string filePath)
}
```

## Document Handling Processes

### Document Ingestion Pipeline

```mermaid
flowchart TD
    subgraph "Document Sources"
        EMAIL[Email Attachments]
        FTP[FTP/SFTP Upload]
        AS2[AS2 Protocol]
        WEB[Web Upload]
        API[API Integration]
    end
    
    subgraph "Ingestion Layer"
        EMAIL --> DI[Document Ingestion]
        FTP --> DI
        AS2 --> DI
        WEB --> DI
        API --> DI
    end
    
    subgraph "Storage Processing"
        DI --> VF[Validate File]
        VF --> GC[Generate Container Path]
        GC --> UF[Upload File]
        UF --> UM[Update Metadata]
    end
    
    subgraph "Document Storage"
        UM --> DS[Document Store]
        DS --> IDX[Index Creation]
        IDX --> AUD[Audit Logging]
    end
    
    subgraph "Processing Queue"
        AUD --> BG[Background Processing]
        BG --> EXT[Document Extraction]
        EXT --> VAL[Validation]
        VAL --> WF[Workflow Assignment]
    end
```

### Container Organisation Strategy

**Container Naming Convention**:
```
{environment}-{customer}-{document-type}-{year}

Examples:
- prod-customer1-invoices-2024
- uat-customer2-orders-2024
- dev-testcustomer-receipts-2024
```

**Container Structure**:
```
Container Root
├── input/           # Incoming documents
│   ├── pending/     # Awaiting processing
│   ├── processing/  # Currently being processed
│   └── failed/      # Processing failures
├── processed/       # Successfully processed documents
│   ├── extracted/   # Data extraction complete
│   ├── validated/   # Business rule validation complete
│   └── matched/     # Document matching complete
├── output/          # Export-ready documents
│   ├── approved/    # Workflow approved
│   ├── exported/    # Successfully exported
│   └── archived/    # Long-term storage
└── metadata/        # Document metadata and indexes
    ├── indexes/     # Search indexes
    ├── thumbnails/  # Document previews
    └── audit/       # Processing audit trails
```

### File Upload and Download Mechanisms

#### Upload Process Flow

```csharp
public async Task<DocumentUploadResult> UploadDocument(
    Stream documentStream, 
    string fileName, 
    string customerId, 
    string documentType)
{
    // 1. Validate input parameters
    ValidateUploadParameters(documentStream, fileName, customerId);
    
    // 2. Generate unique correlation ID
    var correlationId = Guid.NewGuid().ToString();
    
    // 3. Determine container and path
    var containerName = GetContainerName(customerId, documentType);
    var blobPath = GenerateBlobPath(correlationId, fileName);
    
    // 4. Upload to Azure Storage
    var azureStorage = new AzureStorage(connectionString);
    await azureStorage.UploadFileToBlobAsyn(
        documentStream, 
        GetContentType(fileName), 
        containerName, 
        blobPath);
    
    // 5. Create database record
    var document = new Document(
        containerName, 
        Guid.Parse(customerId), 
        $"{containerName}/{blobPath}", 
        correlationId, 
        null, 
        receiverId);
    
    // 6. Queue for processing
    await QueueDocumentForProcessing(document);
    
    return new DocumentUploadResult 
    { 
        Success = true, 
        CorrelationId = correlationId,
        DocumentId = document.Id
    };
}
```

#### Download Process Flow

```csharp
public async Task<DocumentDownloadResult> DownloadDocument(Guid documentId)
{
    // 1. Retrieve document metadata
    var document = await documentRepository.GetByIdAsync(documentId);
    if (document == null) 
        return DocumentDownloadResult.NotFound();
    
    // 2. Check access permissions
    await ValidateDocumentAccess(document, currentUser);
    
    // 3. Download from Azure Storage
    var azureReader = new AzureReader(connectionString);
    var documentStream = await azureReader.GetMemoryStreamAsync(document.FullFilePath);
    
    // 4. Log access for audit
    await auditRepository.LogDocumentAccess(document, currentUser);
    
    return new DocumentDownloadResult
    {
        Success = true,
        FileName = document.FileName,
        ContentType = GetContentType(document.FileName),
        DocumentStream = documentStream
    };
}
```

## Storage Security and Access Patterns

### Security Architecture

```mermaid
graph TB
    subgraph "Security Layers"
        APP[Application Layer]
        AUTH[Authentication Layer]
        AUTHZ[Authorisation Layer]
        STORAGE[Storage Layer]
    end
    
    subgraph "Access Control"
        APP --> RBAC[Role-Based Access Control]
        RBAC --> TENANT[Multi-Tenant Isolation]
        TENANT --> SAS[SAS Token Generation]
    end
    
    subgraph "Storage Security"
        SAS --> ENCRYPT[Encryption at Rest]
        ENCRYPT --> TLS[TLS in Transit]
        TLS --> AUDIT[Access Auditing]
    end
    
    subgraph "Compliance"
        AUDIT --> GDPR[GDPR Compliance]
        GDPR --> SOX[SOX Compliance]
        SOX --> RETENTION[Data Retention]
    end
```

### Access Control Implementation

**Multi-Tenant Data Isolation**:
```csharp
public class SecureDocumentAccess
{
    public async Task<bool> ValidateDocumentAccess(Document document, User user)
    {
        // 1. Verify user belongs to document's tenant
        if (document.TenantId != user.TenantId)
            return false;
        
        // 2. Check role-based permissions
        var hasPermission = await permissionService.HasDocumentPermission(
            user.Id, 
            document.Id, 
            DocumentPermission.Read);
        
        // 3. Validate customer-specific access
        var hasCustomerAccess = await customerService.HasCustomerAccess(
            user.Id, 
            document.CustomerId);
        
        return hasPermission && hasCustomerAccess;
    }
}
```

**SAS Token Management**:
```csharp
public class SASTokenManager
{
    public string GenerateDocumentAccessToken(Document document, TimeSpan expiry)
    {
        var sasBuilder = new BlobSasBuilder
        {
            BlobContainerName = document.FileContainer,
            BlobName = GetBlobName(document.FullFilePath),
            Resource = "b", // Blob resource
            ExpiresOn = DateTimeOffset.UtcNow.Add(expiry)
        };
        
        // Grant read permissions only
        sasBuilder.SetPermissions(BlobSasPermissions.Read);
        
        return sasBuilder.ToSasQueryParameters(storageSharedKeyCredential).ToString();
    }
}
```

### Performance Optimisation

**Caching Strategy**:
```csharp
public class CachedDocumentReader
{
    private readonly MemoryCache cache = new MemoryCache(new MemoryCacheOptions
    {
        SizeLimit = 100 // Limit cache size
    });
    
    public async Task<string> ReadDocumentWithCache(string documentPath)
    {
        if (cache.TryGetValue(documentPath, out string cachedContent))
        {
            return cachedContent;
        }
        
        var content = await azureReader.ReadAllTextAsync(documentPath);
        
        var cacheOptions = new MemoryCacheEntryOptions
        {
            Size = 1,
            SlidingExpiration = TimeSpan.FromMinutes(30),
            Priority = CacheItemPriority.Normal
        };
        
        cache.Set(documentPath, content, cacheOptions);
        return content;
    }
}
```

## Related Documents

- **[SystemOverview.md](./SystemOverview.md)** - High-level system architecture
- **[DatabaseArchitecture.md](./DatabaseArchitecture.md)** - Database design and entities
- **[SecurityArchitecture.md](./SecurityArchitecture.md)** - Security implementation details
- **[DocumentProcessingPipeline.md](./DocumentProcessingPipeline.md)** - Document processing workflows
- **[AuditingAndLogging.md](./AuditingAndLogging.md)** - Audit trails and compliance

---

*This document provides comprehensive coverage of the eHub file system and storage architecture. Refer to the related documentation for detailed implementation guidance on specific components.*
