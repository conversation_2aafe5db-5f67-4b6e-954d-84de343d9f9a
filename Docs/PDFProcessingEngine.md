# eHub PDF Processing Engine

## Purpose & Scope

This document provides comprehensive documentation of the eHub native PDF processing engine, including PDF parsing capabilities, text extraction algorithms, font handling, coordinate-based field extraction, and document analysis features. It serves as the definitive guide for understanding and working with the sophisticated PDF processing capabilities that power document automation in the eHub platform.

## Prerequisites

- Understanding of PDF file structure and specifications
- Knowledge of character encoding and font systems
- Familiarity with coordinate-based layout systems
- Basic understanding of document processing workflows

## Core Concepts

### PDF Processing Philosophy

The eHub PDF processing engine is built on several key principles:

1. **Native Processing**: Custom PDF parser with no external dependencies for core functionality
2. **Coordinate Precision**: Exact coordinate-based field extraction and positioning
3. **Font Intelligence**: Advanced font handling with character mapping and encoding support
4. **Performance Optimised**: Stream-based processing for large document handling
5. **Business-Focused**: Designed specifically for invoice and trading document processing

### PDF Engine Overview

```mermaid
graph TB
    subgraph "PDF Input Processing"
        PDF[PDF Document] --> PS[PDF Stream Parser]
        PS --> SA[Structure Analysis]
        SA --> FE[Font Extraction]
        SA --> CE[Content Extraction]
    end
    
    subgraph "Content Analysis"
        FE --> FM[Font Mapping]
        CE --> TE[Text Extraction]
        FM --> TE
        TE --> CB[Coordinate Bounds]
        CB --> TD[Table Detection]
    end
    
    subgraph "Field Extraction"
        TD --> FI[Field Identification]
        CB --> FI
        FI --> VE[Value Extraction]
        VE --> BV[Business Validation]
    end
    
    subgraph "Output Generation"
        BV --> SE[Structured Extract]
        SE --> DM[Document Model]
        DM --> API[API Response]
    end
    
    style PDF fill:#e1f5fe
    style API fill:#e8f5e8
```

## PDF Parser Architecture

### Core PDF Processing Components

**Location**: `PDFNative/`

The PDF processing engine consists of several interconnected components that work together to provide comprehensive document analysis:

```csharp
public class PDFProcessor
{
    public async Task<PDFProcessingResult> ProcessPDFAsync(
        byte[] pdfData, 
        ProcessingOptions options)
    {
        // 1. Parse PDF structure
        var structure = await ParsePDFStructureAsync(pdfData);
        
        // 2. Analyse fonts and encoding
        var fontMap = await BuildFontMappingAsync(structure);
        
        // 3. Extract text with coordinates
        var textBlocks = await ExtractTextWithCoordinatesAsync(structure, fontMap);
        
        // 4. Detect and extract tables
        var tables = await DetectAndExtractTablesAsync(textBlocks);
        
        // 5. Identify business fields
        var fields = await IdentifyBusinessFieldsAsync(textBlocks, tables);
        
        // 6. Generate structured output
        var businessData = await ExtractBusinessDataAsync(fields);
        
        return new PDFProcessingResult
        {
            Structure = structure,
            FontMap = fontMap,
            TextBlocks = textBlocks,
            Tables = tables,
            Fields = fields,
            BusinessData = businessData,
            ProcessingMetadata = CreateMetadata(options)
        };
    }
}
```

### PDF Structure Analysis

```mermaid
sequenceDiagram
    participant C as Client
    participant PP as PDF Parser
    participant SA as Structure Analyser
    participant FE as Font Extractor
    participant TE as Text Extractor
    
    C->>PP: Process PDF Document
    PP->>SA: Analyse PDF Structure
    SA->>SA: Parse PDF Objects
    SA->>SA: Build Object Tree
    SA-->>PP: Structure Map
    
    PP->>FE: Extract Font Information
    FE->>FE: Parse Font Dictionaries
    FE->>FE: Build Character Maps
    FE-->>PP: Font Mapping
    
    PP->>TE: Extract Text Content
    TE->>TE: Process Text Objects
    TE->>TE: Apply Font Mapping
    TE->>TE: Calculate Coordinates
    TE-->>PP: Text Blocks with Positions
    
    PP-->>C: Processing Result
```

#### PDF Object Structure Analysis

```csharp
public class PDFStructureAnalyser
{
    public async Task<PDFStructure> AnalysePDFStructureAsync(byte[] pdfData)
    {
        using var stream = new MemoryStream(pdfData);
        using var reader = new PDFStreamReader(stream);
        
        // Parse PDF header and version
        var header = await ParsePDFHeaderAsync(reader);
        
        // Read cross-reference table
        var xrefTable = await ParseXRefTableAsync(reader);
        
        // Parse object tree
        var objects = await ParsePDFObjectsAsync(reader, xrefTable);
        
        // Build document structure
        var structure = new PDFStructure
        {
            Header = header,
            Objects = objects,
            Pages = await ExtractPagesAsync(objects),
            Fonts = await ExtractFontsAsync(objects),
            Resources = await ExtractResourcesAsync(objects)
        };
        
        return structure;
    }
}
```

## Font Handling and Character Mapping

### Advanced Font Processing

The PDF engine includes sophisticated font handling capabilities to ensure accurate text extraction across different font types and encodings:

```mermaid
graph TD
    subgraph "Font Analysis"
        FD[Font Dictionary] --> FT[Font Type Detection]
        FT --> TE1[Type 1 Font]
        FT --> TTF[TrueType Font]
        FT --> T3[Type 3 Font]
        FT --> CID[CID Font]
    end
    
    subgraph "Encoding Resolution"
        TE1 --> E1[Standard Encoding]
        TTF --> E2[Unicode Encoding]
        T3 --> E3[Custom Encoding]
        CID --> E4[CMap Encoding]
    end
    
    subgraph "Character Mapping"
        E1 --> CM[Character Map]
        E2 --> CM
        E3 --> CM
        E4 --> CM
        CM --> TC[Text Conversion]
    end
    
    style FD fill:#fff3e0
    style TC fill:#e8f5e8
```

#### Font Mapping Implementation

```csharp
public class FontMapper
{
    public async Task<FontMap> BuildFontMappingAsync(PDFStructure structure)
    {
        var fontMap = new FontMap();
        
        foreach (var font in structure.Fonts)
        {
            var mapping = font.Type switch
            {
                FontType.Type1 => await ProcessType1FontAsync(font),
                FontType.TrueType => await ProcessTrueTypeFontAsync(font),
                FontType.Type3 => await ProcessType3FontAsync(font),
                FontType.CIDFont => await ProcessCIDFontAsync(font),
                _ => CreateDefaultMappingAsync(font)
            };
            
            fontMap.AddMapping(font.Id, mapping);
        }
        
        return fontMap;
    }
    
    private async Task<CharacterMapping> ProcessType1FontAsync(PDFFont font)
    {
        // Standard Type 1 font processing
        var encoding = await ResolveEncodingAsync(font.Encoding);
        var glyphMap = await BuildGlyphMapAsync(font.FontDescriptor);
        
        return new CharacterMapping
        {
            FontId = font.Id,
            Encoding = encoding,
            GlyphMap = glyphMap,
            CharacterWidths = font.Widths
        };
    }
}
```

### Character Encoding Resolution

```csharp
public class EncodingResolver
{
    private static readonly Dictionary<string, Encoding> StandardEncodings = new()
    {
        ["StandardEncoding"] = Encoding.GetEncoding("iso-8859-1"),
        ["MacRomanEncoding"] = Encoding.GetEncoding("macintosh"),
        ["WinAnsiEncoding"] = Encoding.GetEncoding("windows-1252"),
        ["PDFDocEncoding"] = new PDFDocumentEncoding()
    };
    
    public async Task<TextEncoding> ResolveEncodingAsync(PDFEncoding pdfEncoding)
    {
        if (pdfEncoding.IsStandard)
        {
            return StandardEncodings[pdfEncoding.Name];
        }
        
        if (pdfEncoding.HasDifferences)
        {
            return await BuildCustomEncodingAsync(pdfEncoding);
        }
        
        // Unicode handling for modern fonts
        if (pdfEncoding.IsUnicode)
        {
            return new UnicodeEncoding(pdfEncoding.ByteOrder == ByteOrder.BigEndian);
        }
        
        return StandardEncodings["StandardEncoding"]; // Fallback
    }
}
```

## Coordinate-Based Text Extraction

### Precise Text Positioning

The PDF engine extracts text with exact coordinate information, enabling precise field identification and table detection:

```mermaid
graph LR
    subgraph "Text Extraction Process"
        TO[Text Objects] --> TP[Text Positioning]
        TP --> CTM[Coordinate Transform]
        CTM --> TB[Text Blocks]
        TB --> BB[Bounding Boxes]
    end
    
    subgraph "Coordinate System"
        BB --> PC[Page Coordinates]
        PC --> RC[Relative Coordinates]
        RC --> FC[Field Coordinates]
    end
    
    subgraph "Text Organisation"
        FC --> TL[Text Lines]
        TL --> TP1[Text Paragraphs]
        TP1 --> TD1[Table Detection]
    end
    
    style TO fill:#e3f2fd
    style TD1 fill:#e8f5e8
```

#### Text Block Extraction

```csharp
public class TextExtractor
{
    public async Task<List<TextBlock>> ExtractTextBlocksAsync(
        PDFPage page, 
        FontMap fontMap)
    {
        var textBlocks = new List<TextBlock>();
        var currentTransform = Matrix.Identity;
        
        foreach (var textObject in page.TextObjects)
        {
            // Apply text state transformations
            currentTransform = ApplyTextMatrix(textObject.TextMatrix, currentTransform);
            
            // Extract individual characters with positions
            var characters = await ExtractCharactersAsync(textObject, fontMap, currentTransform);
            
            // Group characters into words and lines
            var words = GroupCharactersIntoWords(characters);
            var lines = GroupWordsIntoLines(words);
            
            // Create text blocks from lines
            foreach (var line in lines)
            {
                textBlocks.Add(new TextBlock
                {
                    Text = string.Join(" ", line.Words.Select(w => w.Text)),
                    BoundingBox = CalculateBoundingBox(line.Words),
                    Font = line.Words.First().Font,
                    FontSize = line.Words.First().FontSize,
                    Confidence = CalculateConfidence(line)
                });
            }
        }
        
        return textBlocks.OrderBy(tb => tb.BoundingBox.Y)
                        .ThenBy(tb => tb.BoundingBox.X)
                        .ToList();
    }
}
```

### Coordinate Transformation Mathematics

```csharp
public class CoordinateTransformer
{
    public Point TransformCoordinate(Point originalPoint, Matrix transformMatrix)
    {
        // Apply PDF coordinate transformation matrix
        // [x' y' 1] = [x y 1] * [a c e]
        //                      [b d f]
        //                      [0 0 1]
        
        var x = originalPoint.X * transformMatrix.A + 
                originalPoint.Y * transformMatrix.C + 
                transformMatrix.E;
                
        var y = originalPoint.X * transformMatrix.B + 
                originalPoint.Y * transformMatrix.D + 
                transformMatrix.F;
        
        return new Point((float)x, (float)y);
    }
    
    public Rectangle TransformRectangle(Rectangle rect, Matrix matrix)
    {
        var topLeft = TransformCoordinate(new Point(rect.X, rect.Y), matrix);
        var bottomRight = TransformCoordinate(new Point(rect.Right, rect.Bottom), matrix);
        
        return new Rectangle(
            Math.Min(topLeft.X, bottomRight.X),
            Math.Min(topLeft.Y, bottomRight.Y),
            Math.Abs(bottomRight.X - topLeft.X),
            Math.Abs(bottomRight.Y - topLeft.Y)
        );
    }
}
```

## Table Detection and Extraction

### Intelligent Table Recognition

The PDF engine includes sophisticated table detection algorithms that identify tabular data without relying on explicit table markup:

```mermaid
flowchart TD
    TB[Text Blocks] --> CA[Column Analysis]
    TB --> RA[Row Analysis]
    
    CA --> CD[Column Detection]
    RA --> RD[Row Detection]
    
    CD --> AG[Alignment Grid]
    RD --> AG
    
    AG --> TC[Table Candidate]
    TC --> TV[Table Validation]
    
    TV --> VT{Valid Table?}
    VT -->|Yes| TE[Table Extraction]
    VT -->|No| NTE[Non-Tabular Text]
    
    TE --> TM[Table Model]
    NTE --> TM
    
    style TB fill:#e3f2fd
    style TM fill:#e8f5e8
```

#### Table Detection Algorithm

```csharp
public class TableDetector
{
    public async Task<List<TableCandidate>> DetectTablesAsync(List<TextBlock> textBlocks)
    {
        var tables = new List<TableCandidate>();
        
        // 1. Analyse horizontal alignment patterns
        var columns = await DetectColumnsAsync(textBlocks);
        
        // 2. Analyse vertical alignment patterns
        var rows = await DetectRowsAsync(textBlocks);
        
        // 3. Create alignment grid
        var grid = CreateAlignmentGrid(columns, rows);
        
        // 4. Identify table candidates
        var candidates = await IdentifyTableCandidatesAsync(grid, textBlocks);
        
        // 5. Validate table structure
        foreach (var candidate in candidates)
        {
            if (await ValidateTableStructureAsync(candidate))
            {
                tables.Add(candidate);
            }
        }
        
        return tables;
    }
    
    private async Task<List<ColumnInfo>> DetectColumnsAsync(List<TextBlock> textBlocks)
    {
        // Group text blocks by X coordinate with tolerance
        var tolerance = 5.0f; // Points
        var columnGroups = textBlocks
            .GroupBy(tb => Math.Round(tb.BoundingBox.X / tolerance) * tolerance)
            .Where(g => g.Count() >= 3) // Minimum items for column
            .OrderBy(g => g.Key)
            .ToList();
        
        var columns = new List<ColumnInfo>();
        foreach (var group in columnGroups)
        {
            columns.Add(new ColumnInfo
            {
                X = (float)group.Key,
                Width = group.Max(tb => tb.BoundingBox.Right) - (float)group.Key,
                TextBlocks = group.ToList(),
                Confidence = CalculateColumnConfidence(group.ToList())
            });
        }
        
        return columns;
    }
}
```

### Table Structure Validation

```csharp
public class TableValidator
{
    public async Task<bool> ValidateTableStructureAsync(TableCandidate candidate)
    {
        var validationRules = new List<ITableValidationRule>
        {
            new MinimumRowsRule(2),
            new MinimumColumnsRule(2),
            new ConsistentColumnWidthRule(),
            new HeaderDetectionRule(),
            new DataConsistencyRule()
        };
        
        foreach (var rule in validationRules)
        {
            if (!await rule.ValidateAsync(candidate))
            {
                return false;
            }
        }
        
        return true;
    }
}

public class ConsistentColumnWidthRule : ITableValidationRule
{
    public async Task<bool> ValidateAsync(TableCandidate candidate)
    {
        var columnWidthVariance = CalculateColumnWidthVariance(candidate.Columns);
        var threshold = 0.2f; // 20% variance allowed
        
        return columnWidthVariance < threshold;
    }
}
```

## Field Identification and Extraction

### Business Field Recognition

The PDF engine uses multiple strategies to identify and extract business-relevant fields from documents:

```mermaid
graph TB
    subgraph "Field Identification Strategies"
        TB[Text Blocks] --> KL[Keyword Location]
        TB --> PS[Positional Scanning]
        TB --> PM[Pattern Matching]
        TB --> TS[Template Matching]
    end
    
    subgraph "Field Extraction"
        KL --> FE[Field Extraction]
        PS --> FE
        PM --> FE
        TS --> FE
        
        FE --> VV[Value Validation]
        VV --> FT[Field Typing]
        FT --> BC[Business Context]
    end
    
    subgraph "Output"
        BC --> SF[Structured Fields]
        SF --> BM[Business Model]
    end
    
    style TB fill:#e3f2fd
    style BM fill:#e8f5e8
```

#### Coordinate-Based Field Extraction

```csharp
public class FieldExtractor
{
    public async Task<List<ExtractedField>> ExtractFieldsAsync(
        List<TextBlock> textBlocks,
        FieldDefinition[] fieldDefinitions)
    {
        var extractedFields = new List<ExtractedField>();
        
        foreach (var fieldDef in fieldDefinitions)
        {
            var field = await ExtractFieldAsync(textBlocks, fieldDef);
            if (field != null)
            {
                extractedFields.Add(field);
            }
        }
        
        return extractedFields;
    }
    
    private async Task<ExtractedField> ExtractFieldAsync(
        List<TextBlock> textBlocks,
        FieldDefinition fieldDef)
    {
        return fieldDef.ExtractionMethod switch
        {
            ExtractionMethod.Coordinate => await ExtractByCoordinateAsync(textBlocks, fieldDef),
            ExtractionMethod.Keyword => await ExtractByKeywordAsync(textBlocks, fieldDef),
            ExtractionMethod.Pattern => await ExtractByPatternAsync(textBlocks, fieldDef),
            ExtractionMethod.Relative => await ExtractByRelativePositionAsync(textBlocks, fieldDef),
            _ => null
        };
    }
    
    private async Task<ExtractedField> ExtractByCoordinateAsync(
        List<TextBlock> textBlocks,
        FieldDefinition fieldDef)
    {
        var searchArea = new Rectangle(
            fieldDef.X,
            fieldDef.Y,
            fieldDef.Width,
            fieldDef.Height
        );
        
        var textInArea = textBlocks
            .Where(tb => searchArea.IntersectsWith(tb.BoundingBox))
            .OrderBy(tb => tb.BoundingBox.Y)
            .ThenBy(tb => tb.BoundingBox.X)
            .ToList();
        
        if (!textInArea.Any())
        {
            return null;
        }
        
        var extractedText = string.Join(" ", textInArea.Select(tb => tb.Text));
        var processedValue = await ProcessFieldValueAsync(extractedText, fieldDef);
        
        return new ExtractedField
        {
            Name = fieldDef.Name,
            RawValue = extractedText,
            ProcessedValue = processedValue,
            Confidence = CalculateExtractionConfidence(textInArea, fieldDef),
            BoundingBox = CalculateCombinedBoundingBox(textInArea),
            ExtractionMethod = ExtractionMethod.Coordinate
        };
    }
}
```

### Pattern-Based Field Recognition

```csharp
public class PatternFieldExtractor
{
    private static readonly Dictionary<FieldType, Regex> FieldPatterns = new()
    {
        [FieldType.InvoiceNumber] = new Regex(@"(?:INV|INVOICE)\s*#?\s*(\d+)", RegexOptions.IgnoreCase),
        [FieldType.Date] = new Regex(@"\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4}"),
        [FieldType.Currency] = new Regex(@"[\£\$\€]\s*\d+(?:\.\d{2})?|\d+\.\d{2}"),
        [FieldType.Percentage] = new Regex(@"\d+(?:\.\d+)?\s*%"),
        [FieldType.PostalCode] = new Regex(@"[A-Z]{1,2}\d[A-Z\d]?\s*\d[A-Z]{2}", RegexOptions.IgnoreCase),
        [FieldType.VATNumber] = new Regex(@"GB\s*\d{3}\s*\d{4}\s*\d{2}", RegexOptions.IgnoreCase)
    };
    
    public async Task<ExtractedField> ExtractByPatternAsync(
        List<TextBlock> textBlocks,
        FieldDefinition fieldDef)
    {
        var pattern = FieldPatterns[fieldDef.FieldType];
        var combinedText = string.Join(" ", textBlocks.Select(tb => tb.Text));
        
        var match = pattern.Match(combinedText);
        if (match.Success)
        {
            // Find the text block containing the match
            var matchingBlock = FindTextBlockContainingMatch(textBlocks, match);
            
            return new ExtractedField
            {
                Name = fieldDef.Name,
                RawValue = match.Value,
                ProcessedValue = await ProcessMatchedValueAsync(match, fieldDef.FieldType),
                Confidence = CalculatePatternConfidence(match),
                BoundingBox = matchingBlock?.BoundingBox,
                ExtractionMethod = ExtractionMethod.Pattern
            };
        }
        
        return null;
    }
}
```

## Performance Optimisation

### Stream-Based Processing

```csharp
public class OptimisedPDFProcessor
{
    public async Task<PDFProcessingResult> ProcessLargePDFAsync(
        Stream pdfStream,
        ProcessingOptions options)
    {
        // Use streaming approach for large documents
        using var streamingParser = new StreamingPDFParser(pdfStream);
        
        var result = new PDFProcessingResult();
        
        // Process pages incrementally
        await foreach (var page in streamingParser.GetPagesAsync())
        {
            // Process only required pages if specified
            if (options.PageRange?.Contains(page.Number) == false)
            {
                continue;
            }
            
            // Extract text with memory-efficient approach
            var textBlocks = await ExtractTextBlocksStreamingAsync(page);
            
            // Process fields incrementally
            var fields = await ExtractFieldsStreamingAsync(textBlocks, options.FieldDefinitions);
            
            result.AddPageResult(page.Number, textBlocks, fields);
            
            // Clear memory after processing each page
            GC.Collect();
        }
        
        return result;
    }
}
```

### Memory Management

```mermaid
sequenceDiagram
    participant C as Client
    participant OP as Optimised Processor
    participant SP as Streaming Parser
    participant ME as Memory Manager
    
    C->>OP: Process Large PDF
    OP->>SP: Start Streaming Parse
    
    loop For Each Page
        SP->>OP: Page Data
        OP->>OP: Extract Text Blocks
        OP->>OP: Extract Fields
        OP->>ME: Clear Page Memory
        ME->>ME: Garbage Collect
    end
    
    OP-->>C: Processing Result
```

### Caching Strategy

```csharp
public class PDFProcessingCache
{
    private readonly IMemoryCache _cache;
    private readonly TimeSpan _cacheExpiry = TimeSpan.FromHours(2);
    
    public async Task<FontMap> GetFontMapAsync(string fontId)
    {
        var cacheKey = $"fontmap_{fontId}";
        
        if (_cache.TryGetValue(cacheKey, out FontMap cachedMap))
        {
            return cachedMap;
        }
        
        var fontMap = await BuildFontMapAsync(fontId);
        _cache.Set(cacheKey, fontMap, _cacheExpiry);
        
        return fontMap;
    }
    
    public async Task<List<FieldDefinition>> GetFieldDefinitionsAsync(Guid customerId)
    {
        var cacheKey = $"fields_{customerId}";
        
        if (_cache.TryGetValue(cacheKey, out List<FieldDefinition> cachedFields))
        {
            return cachedFields;
        }
        
        var fields = await LoadFieldDefinitionsAsync(customerId);
        _cache.Set(cacheKey, fields, _cacheExpiry);
        
        return fields;
    }
}
```

## Error Handling and Recovery

### Robust PDF Processing

```csharp
public class ResilientPDFProcessor
{
    public async Task<PDFProcessingResult> ProcessWithRecoveryAsync(
        byte[] pdfData,
        ProcessingOptions options)
    {
        var result = new PDFProcessingResult();
        
        try
        {
            // Attempt primary processing
            return await ProcessPDFAsync(pdfData, options);
        }
        catch (CorruptedPDFException ex)
        {
            // Attempt PDF repair
            var repairedPDF = await AttemptPDFRepairAsync(pdfData);
            if (repairedPDF != null)
            {
                return await ProcessPDFAsync(repairedPDF, options);
            }
            
            result.AddError("PDF is corrupted and cannot be repaired", ex);
        }
        catch (UnsupportedEncodingException ex)
        {
            // Fallback to OCR processing
            result = await FallbackToOCRAsync(pdfData, options);
            result.AddWarning("Used OCR fallback due to encoding issues", ex);
        }
        catch (InsufficientMemoryException ex)
        {
            // Use streaming approach
            using var stream = new MemoryStream(pdfData);
            return await ProcessLargePDFAsync(stream, options);
        }
        
        return result;
    }
}
```

## Testing and Validation

### PDF Processing Tests

```csharp
[TestClass]
public class PDFProcessingEngineTests
{
    [TestMethod]
    public async Task ExtractInvoiceFields_StandardInvoice_ShouldExtractAllFields()
    {
        // Arrange
        var testPDF = LoadTestInvoicePDF("standard_invoice.pdf");
        var expectedFields = new[]
        {
            "InvoiceNumber", "InvoiceDate", "DueDate",
            "SupplierName", "CustomerName", "TotalAmount"
        };
        
        // Act
        var result = await _processor.ProcessPDFAsync(testPDF, ProcessingOptions.Default);
        
        // Assert
        Assert.IsTrue(result.IsSuccess);
        foreach (var expectedField in expectedFields)
        {
            Assert.IsTrue(result.Fields.Any(f => f.Name == expectedField),
                $"Field '{expectedField}' was not extracted");
        }
    }
    
    [TestMethod]
    public async Task ProcessPDF_CorruptedFile_ShouldAttemptRepair()
    {
        // Arrange
        var corruptedPDF = LoadCorruptedPDF("corrupted_invoice.pdf");
        
        // Act
        var result = await _processor.ProcessWithRecoveryAsync(corruptedPDF, ProcessingOptions.Default);
        
        // Assert
        Assert.IsTrue(result.HasWarnings || result.IsSuccess);
    }
}
```

## Common Development Patterns

### Adding Custom Field Extractors

```csharp
public class CustomFieldExtractor : IFieldExtractor
{
    public async Task<ExtractedField> ExtractAsync(
        List<TextBlock> textBlocks,
        FieldDefinition fieldDef)
    {
        // Custom extraction logic
        var extractedValue = await PerformCustomExtractionAsync(textBlocks, fieldDef);
        
        return new ExtractedField
        {
            Name = fieldDef.Name,
            ProcessedValue = extractedValue,
            Confidence = CalculateConfidence(extractedValue),
            ExtractionMethod = ExtractionMethod.Custom
        };
    }
}

// Register custom extractor
services.AddScoped<IFieldExtractor, CustomFieldExtractor>();
```

### Performance Monitoring

```csharp
public class PerformanceMonitoredProcessor : IPDFProcessor
{
    private readonly IPDFProcessor _innerProcessor;
    private readonly IPerformanceCounter _perfCounter;
    
    public async Task<PDFProcessingResult> ProcessPDFAsync(byte[] pdfData, ProcessingOptions options)
    {
        using var timer = _perfCounter.StartTimer("pdf_processing");
        
        var result = await _innerProcessor.ProcessPDFAsync(pdfData, options);
        
        _perfCounter.Increment("pdfs_processed");
        _perfCounter.RecordValue("pdf_size_kb", pdfData.Length / 1024);
        _perfCounter.RecordValue("fields_extracted", result.Fields?.Count ?? 0);
        
        return result;
    }
}
```

## Related Documents

- **[DocumentProcessingPipeline.md](./DocumentProcessingPipeline.md)** - How PDF processing fits into the overall workflow
- **[BusinessLogicArchitecture.md](./BusinessLogicArchitecture.md)** - Business logic that uses PDF processing
- **[FileSystemAndStorage.md](./FileSystemAndStorage.md)** - Document storage and retrieval
- **[PerformanceAndScaling.md](./PerformanceAndScaling.md)** - Optimising PDF processing performance

---

*This PDF processing engine documentation provides comprehensive guidance for understanding and working with the sophisticated native PDF processing capabilities in eHub. The coordinate-based extraction, advanced font handling, and table detection make this a powerful platform for automated document processing.*