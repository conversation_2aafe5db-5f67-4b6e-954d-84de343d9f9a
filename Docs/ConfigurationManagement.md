# eHub Configuration Management

## Purpose & Scope

This document provides comprehensive documentation of the eHub configuration management system, including system settings, environment configurations, configuration patterns, and security practices. It serves as the definitive guide for understanding and managing the eHub configuration architecture.

## Prerequisites

- Understanding of .NET configuration patterns and appsettings.json
- Knowledge of environment-specific deployment strategies
- Familiarity with Azure configuration and connection strings
- Basic understanding of configuration security and secrets management

## Core Concepts

### Configuration Architecture Philosophy

The eHub configuration management is built on several key principles:

1. **Environment Separation**: Clear separation between development, UAT, and production configurations
2. **Security First**: Sensitive configuration data protected through secure storage
3. **Centralised Management**: Consistent configuration patterns across all components
4. **Runtime Flexibility**: Dynamic configuration updates without application restarts
5. **Audit Trail**: Configuration changes tracked for compliance and troubleshooting

### Configuration Overview

```
eHub Configuration Architecture
├── Application Settings
│   ├── Connection Strings - Database and service connections
│   ├── Azure Storage - Blob storage and file system configuration
│   ├── API Endpoints - Service-to-service communication
│   └── Feature Flags - Runtime behaviour control
├── Environment Configuration
│   ├── Development - Local development settings
│   ├── UAT - User acceptance testing environment
│   ├── PreProduction - Staging environment
│   └── Production - Live production settings
├── Security Configuration
│   ├── Certificates - SSL/TLS and AS2 certificates
│   ├── Authentication - OIDC and JWT configuration
│   ├── Encryption Keys - Data protection keys
│   └── Access Tokens - Service authentication tokens
└── Runtime Configuration
    ├── Background Jobs - Hangfire and scheduling
    ├── Email Settings - SMTP and notification configuration
    ├── Integration Settings - FTP, AS2, and external systems
    └── Business Rules - Customer-specific configuration
```

## Configuration Patterns and Architecture

### Centralised Configuration Model

```mermaid
graph TB
    subgraph "Configuration Sources"
        AS[appsettings.json]
        ENV[Environment Variables]
        AKV[Azure Key Vault]
        CMD[Command Line Args]
    end
    
    subgraph "Configuration Providers"
        CB[Configuration Builder]
        AS --> CB
        ENV --> CB
        AKV --> CB
        CMD --> CB
    end
    
    subgraph "Configuration Models"
        CB --> CM[ConfigurationModel]
        CB --> AC[ApplicationConfigs]
        CB --> CS[ConnectionStrings]
    end
    
    subgraph "Application Components"
        CM --> API[APIs]
        CM --> BG[Background Services]
        CM --> WEB[Web Applications]
        CM --> INT[Integration Services]
    end
```

### Configuration Model Implementation

**Core Configuration Model**:
```csharp
public class ConfigurationModel
{
    public ConfigurationModel(IConfiguration configuration)
    {
        // Database connections
        this.MainConnectionString = configuration.GetConnectionString(ConnectionStrings.ETrading);
        this.ReportsConnectionString = configuration.GetConnectionString(ConnectionStrings.Reports);
        this.Portal = configuration.GetConnectionString(ConnectionStrings.Portal);
        this.FTPHangfire = configuration.GetConnectionString(ConnectionStrings.FTPHangfire);

        // Azure Storage
        this.AzureStorageConnectionString = configuration[ApplicationConfigs.AzureStorageConnectionString];
        this.FTPAzureStorageConnectionString = configuration[ApplicationConfigs.FTPAzureStorageConnectionString];

        // Security certificates
        this.FTPCertificate = configuration[ApplicationConfigs.FTPCertificate];
        this.AS2CertificateContainer = configuration[ApplicationConfigs.AS2CertificateContainer];
        this.OurAS2Certificate = configuration[ApplicationConfigs.OurAS2Certificate];
        this.OurAS2CertificatePassword = configuration[ApplicationConfigs.OurAS2CertificatePassword];

        // API endpoints
        this.ETradingApiServer = configuration["Server:ETradingEndpoint"];
        this.ETradingApiJwt = configuration["Server:ETradingApiJwt"];
        this.SchedulingApiServer = configuration["Server:SchedulingApi"];
        this.ReportingApiServer = configuration["Server:ReportingApiServer"];

        // Environment-specific settings
        this.Production = new EnvironmentDetails(
            configuration["ProductionServer:ETradingEndpoint"],
            configuration["ProductionServer:ETradingApiJwt"],
            configuration["ProductionStorageConnectionString"]);

        this.UAT = new EnvironmentDetails(
            configuration["UATServer:ETradingEndpoint"],
            configuration["UATServer:ETradingApiJwt"],
            configuration["UATStorageConnectionString"]);
    }
}
```

### Application Configuration Constants

```csharp
public static class ApplicationConfigs
{
    public const string AzureStorageConnectionString = "AzureStorageConnectionString";
    public const string FTPAzureStorageConnectionString = "FTPAzureStorageConnectionString";
    public const string FTPCertificate = "FTPCertificate";
    public const string AS2CertificateContainer = "AS2CertificateContainer";
    public const string OurAS2Certificate = "OurAS2Certificate";
    public const string OurAS2CertificatePassword = "OurAS2CertificatePassword";
    public const string RuleFolder = "RuleFolder";
    public const string OverrideEmailAddress = "OverrideEmailAddress";
    public const string HangfireEndPoint = "HangfireEndPoint";
    public const string HangfireServerGroup = "HangfireServerGroup";
    public const string UsePoller = "UsePoller";
    public const string UseSeperateFTPServer = "UseSeperateFTPServer";
}

public static class ConnectionStrings
{
    public const string Portal = "Portal";
    public const string HangfirePoller = "HangfirePoller";
    public const string ETrading = "ETrading";
    public const string FTPHangfire = "FTPHangfire";
    public const string Reports = "Reports";
}
```

## Environment-Specific Configuration

### Development Environment Configuration

**Development appsettings.json**:
```json
{
  "ConnectionStrings": {
    "ETrading": "Server=(localdb)\\mssqllocaldb;Database=eHubDev;Trusted_Connection=true;MultipleActiveResultSets=true;",
    "BackgroundProcessor": "Server=(localdb)\\mssqllocaldb;Database=eHubDev;Trusted_Connection=true;",
    "Reports": "Server=(localdb)\\mssqllocaldb;Database=eHubReportsDev;Trusted_Connection=true;"
  },
  "AzureStorageConnectionString": "UseDevelopmentStorage=true",
  "FTPAzureStorageConnectionString": "UseDevelopmentStorage=true",
  "Server": {
    "ETradingEndpoint": "https://localhost:5001",
    "ETradingApiJwt": "dev-jwt-token",
    "SchedulingApi": "https://localhost:5002",
    "ReportingApiServer": "https://localhost:5003"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "Microsoft": "Warning",
      "Hangfire": "Information"
    }
  },
  "UsePoller": false,
  "UseSeperateFTPServer": false
}
```

### UAT Environment Configuration

**UAT appsettings.json**:
```json
{
  "ConnectionStrings": {
    "ETrading": "Server=tcp:openecx.database.windows.net,1433;Initial Catalog=eTradingUAT;Persist Security Info=False;User ID=openecx;Password=***;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;Max Pool Size=100;",
    "BackgroundProcessor": "Server=tcp:openecx.database.windows.net,1433;Initial Catalog=eTradingUAT;Persist Security Info=False;User ID=openecx;Password=***;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;",
    "Reports": "Server=tcp:openecx.database.windows.net,1433;Initial Catalog=eTradingReportsUAT;Persist Security Info=False;User ID=openecx;Password=***;"
  },
  "AzureStorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=etradinguat;AccountKey=***;EndpointSuffix=core.windows.net",
  "FTPAzureStorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=etradingftp;AccountKey=***;EndpointSuffix=core.windows.net",
  "Server": {
    "ETradingEndpoint": "https://uat-api.ehub.com",
    "ETradingApiJwt": "uat-jwt-token",
    "SchedulingApi": "https://uat-scheduling.ehub.com",
    "ReportingApiServer": "https://uat-reporting.ehub.com"
  },
  "UsePoller": true,
  "UseSeperateFTPServer": true
}
```

### Production Environment Configuration

**Production Configuration Strategy**:
```json
{
  "ConnectionStrings": {
    "ETrading": "Server=tcp:prod-sql.database.windows.net,1433;Initial Catalog=eHubProd;Persist Security Info=False;User ID=***;Password=***;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;Max Pool Size=200;",
    "BackgroundProcessor": "Server=tcp:prod-sql.database.windows.net,1433;Initial Catalog=eHubProd;Persist Security Info=False;User ID=***;Password=***;Connection Timeout=60;Max Pool Size=50;",
    "Reports": "Server=tcp:prod-sql-reports.database.windows.net,1433;Initial Catalog=eHubReportsProd;Persist Security Info=False;User ID=***;Password=***;"
  },
  "AzureStorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=ehubprod;AccountKey=***;EndpointSuffix=core.windows.net",
  "FTPAzureStorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=ehubftpprod;AccountKey=***;EndpointSuffix=core.windows.net",
  "Server": {
    "ETradingEndpoint": "https://api.ehub.com",
    "ETradingApiJwt": "prod-jwt-token",
    "SchedulingApi": "https://scheduling.ehub.com",
    "ReportingApiServer": "https://reporting.ehub.com"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Warning",
      "Microsoft": "Error",
      "Hangfire": "Information"
    }
  },
  "UsePoller": true,
  "UseSeperateFTPServer": true
}
```

## Component-Specific Configuration

### API Configuration Pattern

```csharp
public class Configuration
{
    private readonly dynamic configuration;

    public Configuration()
    {
        this.configuration = this.GetConfiguration();
    }

    public string ConnectionStringIO()
    {
        return this.configuration["ConnectionStrings"]["IO"].ToString();
    }

    public string AS2CertificateContainer()
    {
        return this.configuration.CertificateContainer;
    }

    public string AS2ReceiverCertificate()
    {
        return this.configuration.AS2ReceiverCertificate;
    }

    public string AS2ReceiverCertificatePassword()
    {
        return this.configuration.AS2ReceiverCertificatePassword;
    }

    private dynamic GetConfiguration()
    {
        var configpath = $"{Path.GetDirectoryName(this.Assembly.Location)}\\appsettings.json";
        var jsonString = File.ReadAllText(configpath);
        return JsonConvert.DeserializeObject(jsonString);
    }
}
```

### Background Service Configuration

```csharp
public class Configuration
{
    public string ConnectionString()
    {
        return this.configuration.ConnectionStrings.BackgroundProcessor;
    }

    public bool UseJsonFileForJobs()
    {
        return this.configuration.UseJsonFileForJobs;
    }

    public string FailedJobsEmail()
    {
        return this.configuration.FailedJobsEmail;
    }

    public string SMTPUser()
    {
        return this.configuration.SMTPUser;
    }

    public string SMTPPassword()
    {
        return this.configuration.SMPTPassword;
    }

    public string SMTPServer()
    {
        return this.configuration.SMPTServer;
    }

    public string SMTPPort()
    {
        return this.configuration.SMPTPort;
    }
}
```

### Settings-Based Configuration

```csharp
public sealed class JsonSettings : BaseSettings, ISettingList
{
    public JsonSettings()
    {
        this.SettingsList = this.GetSettings();
    }

    public List<SettingModel> GetSettings()
    {
        var settingspath = $"{System.IO.Path.GetDirectoryName(this.Assembly.Location)}\\Settings.json";
        var jsonString = System.IO.File.ReadAllText(settingspath);
        var settingsList = Newtonsoft.Json.JsonConvert.DeserializeObject<List<SettingModel>>(jsonString);
        return settingsList;
    }

    public PDFNative.Models.Settings.Settings Build()
    {
        return new PDFNative.Models.Settings.Settings
        {
            ApplicationOutputDirectory = this.GetSettingValue(PDFNative.Constants.Settings.ApplicationOutputDirectory, string.Empty),
            ComparisonOutputDirectory = this.GetSettingValue(PDFNative.Constants.Settings.ComparisonOutputDirectory, string.Empty),
            DefaultRulesDirectory = this.GetSettingValue(PDFNative.Constants.Settings.DefaultRulesDirectory, string.Empty),
            EnablePrologExtraction = this.GetSettingValue(PDFNative.Constants.Settings.EnablePrologExtraction, false),
            EnableProgramLogging = this.GetSettingValue(PDFNative.Constants.Settings.EnableProgramLogging, false),
            TestSystem = this.GetSettingValue(PDFNative.Constants.Settings.TestSystem, true)
        };
    }
}
```

## Security Configuration Management

### Certificate Management

```mermaid
graph TB
    subgraph "Certificate Storage"
        AKV[Azure Key Vault]
        BS[Blob Storage]
        LS[Local Storage]
    end
    
    subgraph "Certificate Types"
        SSL[SSL/TLS Certificates]
        AS2[AS2 Certificates]
        JWT[JWT Signing Keys]
        ENC[Encryption Keys]
    end
    
    subgraph "Configuration Access"
        AKV --> SSL
        BS --> AS2
        AKV --> JWT
        AKV --> ENC
    end
    
    subgraph "Application Usage"
        SSL --> API[API Endpoints]
        AS2 --> INT[Integration Services]
        JWT --> AUTH[Authentication]
        ENC --> DATA[Data Protection]
    end
```

**Certificate Configuration Pattern**:
```json
{
  "CertificateContainer": "certificates",
  "AS2Certificate": "certificates/AS2Sender.pfx",
  "AS2CertificatePassword": "SecurePassword123",
  "AS2ReceiverCertificate": "certificates/AS2Receiver.pfx",
  "AS2ReceiverCertificatePassword": "SecurePassword456",
  "FTPCertificate": "certificates/FTPClient.pfx"
}
```

### Connection String Security

**Secure Connection String Management**:
```csharp
public class SecureConnectionStringProvider
{
    private readonly IConfiguration configuration;
    private readonly IKeyVaultService keyVaultService;

    public async Task<string> GetConnectionStringAsync(string name)
    {
        // Try Azure Key Vault first
        var keyVaultKey = $"ConnectionStrings--{name}";
        var connectionString = await keyVaultService.GetSecretAsync(keyVaultKey);
        
        if (!string.IsNullOrEmpty(connectionString))
            return connectionString;
        
        // Fallback to configuration
        return configuration.GetConnectionString(name);
    }
}
```

## Configuration Validation and Health Checks

### Configuration Validation

```csharp
public class ConfigurationValidator
{
    public ValidationResult ValidateConfiguration(IConfiguration configuration)
    {
        var errors = new List<string>();
        
        // Validate required connection strings
        ValidateConnectionString(configuration, ConnectionStrings.ETrading, errors);
        ValidateConnectionString(configuration, ConnectionStrings.Reports, errors);
        
        // Validate Azure Storage
        ValidateAzureStorage(configuration, errors);
        
        // Validate API endpoints
        ValidateApiEndpoints(configuration, errors);
        
        // Validate certificates
        ValidateCertificates(configuration, errors);
        
        return new ValidationResult
        {
            IsValid = !errors.Any(),
            Errors = errors
        };
    }
    
    private void ValidateConnectionString(IConfiguration config, string name, List<string> errors)
    {
        var connectionString = config.GetConnectionString(name);
        if (string.IsNullOrEmpty(connectionString))
        {
            errors.Add($"Missing required connection string: {name}");
            return;
        }
        
        try
        {
            using var connection = new SqlConnection(connectionString);
            connection.Open();
        }
        catch (Exception ex)
        {
            errors.Add($"Invalid connection string {name}: {ex.Message}");
        }
    }
}
```

### Health Check Integration

```csharp
public class ConfigurationHealthCheck : IHealthCheck
{
    private readonly IConfiguration configuration;
    private readonly ConfigurationValidator validator;

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        var validationResult = validator.ValidateConfiguration(configuration);
        
        if (validationResult.IsValid)
        {
            return HealthCheckResult.Healthy("Configuration is valid");
        }
        
        var errorMessage = string.Join("; ", validationResult.Errors);
        return HealthCheckResult.Unhealthy($"Configuration validation failed: {errorMessage}");
    }
}
```

## Configuration Best Practices

### Environment Variable Override Pattern

```csharp
public static IHostBuilder CreateHostBuilder(string[] args) =>
    Host.CreateDefaultBuilder(args)
        .ConfigureAppConfiguration((context, config) =>
        {
            var env = context.HostingEnvironment;
            
            // Base configuration
            config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
            
            // Environment-specific configuration
            config.AddJsonFile($"appsettings.{env.EnvironmentName}.json", optional: true, reloadOnChange: true);
            
            // Environment variables (highest priority)
            config.AddEnvironmentVariables();
            
            // Command line arguments (override everything)
            config.AddCommandLine(args);
            
            // Azure Key Vault (production only)
            if (env.IsProduction())
            {
                var keyVaultEndpoint = config.Build()["KeyVaultEndpoint"];
                if (!string.IsNullOrEmpty(keyVaultEndpoint))
                {
                    config.AddAzureKeyVault(keyVaultEndpoint);
                }
            }
        });
```

### Configuration Change Management

```csharp
public class ConfigurationChangeTracker
{
    private readonly IOptionsMonitor<ConfigurationModel> optionsMonitor;
    private readonly ILogger<ConfigurationChangeTracker> logger;

    public ConfigurationChangeTracker(IOptionsMonitor<ConfigurationModel> optionsMonitor, ILogger<ConfigurationChangeTracker> logger)
    {
        this.optionsMonitor = optionsMonitor;
        this.logger = logger;
        
        // Monitor configuration changes
        optionsMonitor.OnChange(OnConfigurationChanged);
    }

    private void OnConfigurationChanged(ConfigurationModel newConfig, string name)
    {
        logger.LogInformation("Configuration changed for {ConfigurationName}", name);
        
        // Validate new configuration
        var validator = new ConfigurationValidator();
        var result = validator.ValidateConfiguration(newConfig);
        
        if (!result.IsValid)
        {
            logger.LogError("Invalid configuration detected: {Errors}", string.Join(", ", result.Errors));
        }
        
        // Notify dependent services of configuration change
        NotifyConfigurationChange(newConfig);
    }
}
```

## Related Documents

- **[SystemOverview.md](./SystemOverview.md)** - High-level system architecture
- **[SecurityArchitecture.md](./SecurityArchitecture.md)** - Security implementation details
- **[BuildAndDeployment.md](./BuildAndDeployment.md)** - Build and deployment procedures
- **[FileSystemAndStorage.md](./FileSystemAndStorage.md)** - Storage configuration
- **[AuditingAndLogging.md](./AuditingAndLogging.md)** - Audit and logging configuration

---

*This document provides comprehensive coverage of the eHub configuration management system. Refer to the related documentation for detailed implementation guidance on specific components.*
