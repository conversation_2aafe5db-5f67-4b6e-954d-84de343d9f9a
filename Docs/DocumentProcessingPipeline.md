# eHub Document Processing Pipeline

## Purpose & Scope

This document provides comprehensive documentation of the eHub document processing pipeline, covering the complete workflow from document ingestion through to export and integration. It details each stage of processing, state transitions, business rule application, and the orchestration of complex document workflows across multiple systems.

## Prerequisites

- Understanding of state machine concepts and workflow orchestration
- Knowledge of document processing and electronic trading workflows
- Familiarity with the eHub business logic and PDF processing capabilities
- Basic understanding of asynchronous processing and job scheduling

## Core Concepts

### Document Processing Philosophy

The eHub document processing pipeline is built on several key principles:

1. **State-Driven Workflow**: All document processing follows a clear state machine pattern
2. **Asynchronous Processing**: Long-running operations are handled via background jobs
3. **Resilient Architecture**: Comprehensive error handling and retry mechanisms
4. **Business Rule Integration**: Configurable business rules at every stage
5. **Audit Trail**: Complete audit logging throughout the pipeline

### Processing Pipeline Overview

```mermaid
graph TB
    subgraph "Document Ingestion"
        DI[Document Input] --> DC[Document Classification]
        DC --> DS[Document Storage]
        DS --> QP[Queue Processing]
    end
    
    subgraph "Extraction Stage"
        QP --> DE[Document Extraction]
        DE --> PDF[PDF Processing]
        DE --> XML[XML/JSON Processing]
        DE --> EDI[EDI Processing]
        PDF --> FE[Field Extraction]
        XML --> FE
        EDI --> FE
    end
    
    subgraph "Validation Stage"
        FE --> BV[Business Validation]
        BV --> RV[Rule Validation]
        RV --> DQ[Data Quality Check]
        DQ --> VS{Validation Success?}
        VS -->|Yes| MS[Matching Stage]
        VS -->|No| SP[Suspense Processing]
    end
    
    subgraph "Matching & Approval"
        MS --> TM[Transaction Matching]
        TM --> WF[Workflow Initiation]
        WF --> AP[Approval Process]
        AP --> AS{Approval Success?}
        AS -->|Yes| ES[Export Stage]
        AS -->|No| RP[Rejection Processing]
    end
    
    subgraph "Export & Integration"
        ES --> EX[Export Processing]
        EX --> TF[Transport Formation]
        TF --> DL[Delivery]
        DL --> CF[Confirmation]
    end
    
    SP --> MS
    RP --> MS
    
    style DI fill:#e3f2fd
    style CF fill:#e8f5e8
```

## Document Ingestion and Classification

### Multiple Ingestion Channels

The eHub platform supports various document ingestion methods, each with specific processing characteristics:

```mermaid
graph LR
    subgraph "Ingestion Channels"
        EM[Email Polling] --> DQ[Document Queue]
        FTP[FTP/SFTP Polling] --> DQ
        AS2[AS2 Protocol] --> DQ
        API[REST API Upload] --> DQ
        WEB[Web Portal Upload] --> DQ
        MU[Manual Upload] --> DQ
    end
    
    subgraph "Classification"
        DQ --> CL[Document Classifier]
        CL --> INV[Invoice]
        CL --> PO[Purchase Order]
        CL --> CN[Credit Note]
        CL --> ST[Statement]
        CL --> GRN[Goods Receipt Note]
        CL --> QT[Quote]
    end
    
    subgraph "Initial Processing"
        INV --> IP[Initial Processing]
        PO --> IP
        CN --> IP
        ST --> IP
        GRN --> IP
        QT --> IP
    end
    
    style EM fill:#e3f2fd
    style IP fill:#e8f5e8
```

#### Document Classification Engine

```csharp
public class DocumentClassificationEngine
{
    public async Task<ClassificationResult> ClassifyDocumentAsync(
        Document document,
        ClassificationOptions options)
    {
        var strategies = GetClassificationStrategies(options);
        var results = new List<ClassificationResult>();
        
        foreach (var strategy in strategies)
        {
            var result = await strategy.ClassifyAsync(document);
            results.Add(result);
            
            // Short-circuit if high confidence result found
            if (result.Confidence > 0.9)
            {
                return result;
            }
        }
        
        // Combine results using weighted scoring
        return CombineClassificationResults(results);
    }
    
    private List<IClassificationStrategy> GetClassificationStrategies(ClassificationOptions options)
    {
        return new List<IClassificationStrategy>
        {
            new FileExtensionClassifier(weight: 0.1),
            new MimeTypeClassifier(weight: 0.2),
            new ContentAnalysisClassifier(weight: 0.3),
            new SenderBasedClassifier(weight: 0.2),
            new TemplateMatchingClassifier(weight: 0.2)
        };
    }
}
```

### Document Storage and Metadata

```csharp
public class DocumentStorageService
{
    public async Task<StorageResult> StoreDocumentAsync(
        IncomingDocument document,
        StorageOptions options)
    {
        // Generate secure document identifier
        var documentId = Guid.NewGuid();
        var secureFilename = GenerateSecureFilename(document.OriginalFilename);
        
        // Store document content
        var storageResult = await _fileStorage.StoreAsync(
            containerName: GetContainerName(document.Source),
            filename: secureFilename,
            content: document.Content,
            metadata: CreateDocumentMetadata(document)
        );
        
        // Create database record
        var documentRecord = new DocumentRecord
        {
            Id = documentId,
            OriginalFilename = document.OriginalFilename,
            SecureFilename = secureFilename,
            DocumentType = document.ClassifiedType,
            Source = document.Source,
            CustomerId = document.CustomerId,
            DateReceived = DateTime.UtcNow,
            ProcessingStatus = ProcessingStatus.Pending,
            StorageLocation = storageResult.Location
        };
        
        await _documentRepository.SaveAsync(documentRecord);
        
        return new StorageResult
        {
            DocumentId = documentId,
            StorageLocation = storageResult.Location,
            Success = true
        };
    }
}
```

## Document Extraction Pipeline

### Multi-Format Processing Architecture

The extraction stage handles different document formats through specialised processors:

```mermaid
sequenceDiagram
    participant DM as Document Manager
    participant EP as Extraction Processor
    participant PP as PDF Processor
    participant XP as XML Processor
    participant JP as JSON Processor
    participant EP2 as EDI Processor
    
    DM->>EP: Process Document
    EP->>EP: Determine Document Type
    
    alt PDF Document
        EP->>PP: Extract PDF Content
        PP->>PP: Parse PDF Structure
        PP->>PP: Extract Text & Fields
        PP-->>EP: Extraction Result
    else XML Document
        EP->>XP: Parse XML Content
        XP->>XP: Validate Schema
        XP->>XP: Extract Business Data
        XP-->>EP: Extraction Result
    else JSON Document
        EP->>JP: Parse JSON Content
        JP->>JP: Validate Structure
        JP->>JP: Map to Business Model
        JP-->>EP: Extraction Result
    else EDI Document
        EP->>EP2: Parse EDI Content
        EP2->>EP2: Validate EDI Structure
        EP2->>EP2: Map Segments to Fields
        EP2-->>EP: Extraction Result
    end
    
    EP-->>DM: Processed Document
```

#### Extraction Orchestrator

```csharp
public class DocumentExtractionOrchestrator
{
    public async Task<ExtractionResult> ProcessDocumentAsync(
        Guid documentId,
        ExtractionOptions options)
    {
        var document = await _documentRepository.GetAsync(documentId);
        var processor = _processorFactory.CreateProcessor(document.DocumentType);
        
        // Update status to processing
        await UpdateDocumentStatusAsync(documentId, ProcessingStatus.Extracting);
        
        try
        {
            // Perform extraction
            var extractionResult = await processor.ExtractAsync(document, options);
            
            // Apply field mapping rules
            var mappedFields = await ApplyFieldMappingAsync(extractionResult, document.CustomerId);
            
            // Validate extracted data
            var validationResult = await ValidateExtractedDataAsync(mappedFields);
            
            var result = new ExtractionResult
            {
                DocumentId = documentId,
                Fields = mappedFields,
                ValidationResult = validationResult,
                Success = validationResult.IsValid,
                ProcessingDuration = extractionResult.ProcessingDuration
            };
            
            // Update document status
            var newStatus = result.Success 
                ? ProcessingStatus.ExtractedSuccessfully 
                : ProcessingStatus.ExtractionFailed;
            await UpdateDocumentStatusAsync(documentId, newStatus);
            
            return result;
        }
        catch (Exception ex)
        {
            await UpdateDocumentStatusAsync(documentId, ProcessingStatus.ExtractionFailed);
            throw new ExtractionException($"Failed to extract document {documentId}", ex);
        }
    }
}
```

### Field Mapping and Transformation

```csharp
public class FieldMappingEngine
{
    public async Task<List<MappedField>> ApplyFieldMappingAsync(
        ExtractionResult extraction,
        Guid customerId)
    {
        var mappingRules = await GetMappingRulesAsync(customerId, extraction.DocumentType);
        var mappedFields = new List<MappedField>();
        
        foreach (var rule in mappingRules)
        {
            var sourceValue = GetSourceValue(extraction.Fields, rule.SourcePath);
            if (sourceValue != null)
            {
                var transformedValue = await ApplyTransformationAsync(sourceValue, rule);
                
                mappedFields.Add(new MappedField
                {
                    Name = rule.TargetFieldName,
                    Value = transformedValue,
                    SourceField = rule.SourcePath,
                    Transformation = rule.TransformationType,
                    Confidence = CalculateMappingConfidence(sourceValue, rule)
                });
            }
        }
        
        return mappedFields;
    }
    
    private async Task<object> ApplyTransformationAsync(object sourceValue, MappingRule rule)
    {
        return rule.TransformationType switch
        {
            TransformationType.DirectMapping => sourceValue,
            TransformationType.DateFormat => ParseAndFormatDate(sourceValue, rule.Parameters),
            TransformationType.CurrencyFormat => ParseAndFormatCurrency(sourceValue, rule.Parameters),
            TransformationType.StringTrim => sourceValue.ToString()?.Trim(),
            TransformationType.RegexExtract => ExtractWithRegex(sourceValue, rule.Parameters),
            TransformationType.LookupTable => await LookupValueAsync(sourceValue, rule.Parameters),
            _ => sourceValue
        };
    }
}
```

## Validation and Business Rules

### Multi-Layer Validation Architecture

The validation stage implements comprehensive checks across multiple layers:

```mermaid
graph TD
    subgraph "Validation Layers"
        FV[Field Validation] --> BV[Business Rules]
        BV --> CV[Cross-Field Validation]
        CV --> RV[Regulatory Validation]
        RV --> DV[Data Quality Validation]
    end
    
    subgraph "Validation Types"
        FV --> MF[Mandatory Fields]
        FV --> DT[Data Types]
        FV --> FR[Format Rules]
        
        BV --> AR[Arithmetic Rules]
        BV --> LR[Logic Rules]
        BV --> CR[Customer Rules]
        
        CV --> TC[Total Calculations]
        CV --> DR[Date Relationships]
        CV --> CR2[Code Relationships]
    end
    
    subgraph "Validation Results"
        DV --> VS{All Valid?}
        VS -->|Yes| VAL[Validation Success]
        VS -->|No| VF[Validation Failure]
        VF --> ER[Error Reporting]
        VF --> SP[Suspense Processing]
    end
    
    style FV fill:#e3f2fd
    style VAL fill:#e8f5e8
    style SP fill:#ffebee
```

#### Business Rules Engine

```csharp
public class BusinessRulesEngine
{
    public async Task<ValidationResult> ValidateDocumentAsync(
        ExtractedDocument document,
        Guid customerId)
    {
        var rules = await GetBusinessRulesAsync(customerId, document.Type);
        var validationResults = new List<RuleValidationResult>();
        
        foreach (var rule in rules.OrderBy(r => r.Priority))
        {
            var ruleResult = await ExecuteRuleAsync(rule, document);
            validationResults.Add(ruleResult);
            
            // Stop on critical failures
            if (ruleResult.Severity == RuleSeverity.Critical && !ruleResult.Passed)
            {
                break;
            }
        }
        
        return new ValidationResult
        {
            DocumentId = document.Id,
            IsValid = validationResults.All(r => r.Passed || r.Severity != RuleSeverity.Critical),
            Results = validationResults,
            ErrorCount = validationResults.Count(r => !r.Passed && r.Severity == RuleSeverity.Error),
            WarningCount = validationResults.Count(r => !r.Passed && r.Severity == RuleSeverity.Warning)
        };
    }
}
```

### Arithmetic Validation Rules

```csharp
public class ArithmeticValidationRule : IBusinessRule
{
    public async Task<RuleValidationResult> ExecuteAsync(ExtractedDocument document)
    {
        var headerTotal = document.GetDecimalField("Total");
        var lineTotal = document.Lines.Sum(l => l.GetDecimalField("LineTotal"));
        var vatTotal = document.Lines.Sum(l => l.GetDecimalField("VATAmount"));
        var netTotal = document.GetDecimalField("NetTotal");
        
        var expectedTotal = netTotal + vatTotal;
        var tolerance = await GetToleranceAsync(document.CustomerId);
        
        var results = new List<string>();
        
        // Validate line total consistency
        if (Math.Abs(lineTotal - netTotal) > tolerance)
        {
            results.Add($"Line total ({lineTotal:C}) does not match net total ({netTotal:C})");
        }
        
        // Validate header total consistency
        if (Math.Abs(headerTotal - expectedTotal) > tolerance)
        {
            results.Add($"Header total ({headerTotal:C}) does not match calculated total ({expectedTotal:C})");
        }
        
        return new RuleValidationResult
        {
            RuleId = "ArithmeticValidation",
            Passed = !results.Any(),
            Messages = results,
            Severity = RuleSeverity.Error
        };
    }
}
```

## Transaction State Management

### Document Processing State Machine

The document processing pipeline follows a sophisticated state machine that manages the lifecycle of each document:

```mermaid
stateDiagram-v2
    [*] --> Created
    Created --> ReadyForProcessing
    Created --> FailedClassification
    
    ReadyForProcessing --> Extracting
    Extracting --> ExtractionComplete
    Extracting --> ExtractionFailed
    
    ExtractionComplete --> Validating
    ExtractionFailed --> Suspense
    
    Validating --> ValidationSuccess
    Validating --> ValidationFailed
    ValidationFailed --> Suspense
    
    ValidationSuccess --> Matching
    Matching --> MatchingComplete
    Matching --> MatchingFailed
    MatchingFailed --> ManualReview
    
    MatchingComplete --> ApprovalPending
    ApprovalPending --> Approved
    ApprovalPending --> Rejected
    ApprovalPending --> OnHold
    
    Approved --> ExportPending
    ExportPending --> ExportComplete
    ExportPending --> ExportFailed
    
    ExportComplete --> Completed
    ExportFailed --> RetryExport
    RetryExport --> ExportPending
    
    Suspense --> ManualIntervention
    ManualIntervention --> ReadyForProcessing
    
    OnHold --> ApprovalPending
    Rejected --> [*]
    Completed --> [*]
```

#### State Machine Implementation

```csharp
public class DocumentStateMachine
{
    private readonly Dictionary<DocumentState, List<DocumentState>> _validTransitions;
    
    public DocumentStateMachine()
    {
        _validTransitions = new Dictionary<DocumentState, List<DocumentState>>
        {
            [DocumentState.Created] = new() { DocumentState.ReadyForProcessing, DocumentState.FailedClassification },
            [DocumentState.ReadyForProcessing] = new() { DocumentState.Extracting },
            [DocumentState.Extracting] = new() { DocumentState.ExtractionComplete, DocumentState.ExtractionFailed },
            [DocumentState.ExtractionComplete] = new() { DocumentState.Validating },
            [DocumentState.Validating] = new() { DocumentState.ValidationSuccess, DocumentState.ValidationFailed },
            [DocumentState.ValidationSuccess] = new() { DocumentState.Matching },
            [DocumentState.Matching] = new() { DocumentState.MatchingComplete, DocumentState.MatchingFailed },
            [DocumentState.MatchingComplete] = new() { DocumentState.ApprovalPending },
            [DocumentState.ApprovalPending] = new() { DocumentState.Approved, DocumentState.Rejected, DocumentState.OnHold },
            [DocumentState.Approved] = new() { DocumentState.ExportPending },
            [DocumentState.ExportPending] = new() { DocumentState.ExportComplete, DocumentState.ExportFailed },
            [DocumentState.ExportComplete] = new() { DocumentState.Completed },
            [DocumentState.ExportFailed] = new() { DocumentState.RetryExport },
            [DocumentState.RetryExport] = new() { DocumentState.ExportPending },
            [DocumentState.ValidationFailed] = new() { DocumentState.Suspense },
            [DocumentState.ExtractionFailed] = new() { DocumentState.Suspense },
            [DocumentState.MatchingFailed] = new() { DocumentState.ManualReview },
            [DocumentState.Suspense] = new() { DocumentState.ManualIntervention },
            [DocumentState.ManualIntervention] = new() { DocumentState.ReadyForProcessing },
            [DocumentState.OnHold] = new() { DocumentState.ApprovalPending }
        };
    }
    
    public async Task<StateTransitionResult> TransitionToAsync(
        Guid documentId,
        DocumentState newState,
        string reason = null)
    {
        var document = await _repository.GetDocumentAsync(documentId);
        
        if (!IsValidTransition(document.CurrentState, newState))
        {
            return StateTransitionResult.InvalidTransition(document.CurrentState, newState);
        }
        
        // Execute pre-transition logic
        await ExecutePreTransitionAsync(document, newState);
        
        // Update state
        document.CurrentState = newState;
        document.LastStateChange = DateTime.UtcNow;
        document.StateChangeReason = reason;
        
        await _repository.UpdateDocumentAsync(document);
        
        // Execute post-transition logic
        await ExecutePostTransitionAsync(document, newState);
        
        // Publish state change event
        await _eventBus.PublishAsync(new DocumentStateChangedEvent
        {
            DocumentId = documentId,
            PreviousState = document.CurrentState,
            NewState = newState,
            Timestamp = DateTime.UtcNow
        });
        
        return StateTransitionResult.Success(newState);
    }
}
```

## Matching and Linking

### Three-Way Matching Process

The matching stage implements sophisticated algorithms to link related documents:

```mermaid
sequenceDiagram
    participant ME as Matching Engine
    participant PO as Purchase Order
    participant GRN as Goods Receipt Note
    participant INV as Invoice
    participant MR as Match Repository
    
    ME->>PO: Retrieve PO Details
    ME->>GRN: Retrieve GRN Details
    ME->>INV: Retrieve Invoice Details
    
    ME->>ME: Validate Match Prerequisites
    ME->>ME: Perform Line-Level Matching
    ME->>ME: Apply Tolerance Rules
    ME->>ME: Calculate Match Confidence
    
    alt High Confidence Match
        ME->>MR: Create Automatic Match
        MR-->>ME: Match Created
    else Medium Confidence Match
        ME->>MR: Create Suggested Match
        MR-->>ME: Suggestion Created
    else Low Confidence Match
        ME->>MR: Flag for Manual Review
        MR-->>ME: Review Flagged
    end
    
    ME->>ME: Update Document States
```

#### Matching Engine Implementation

```csharp
public class ThreeWayMatchingEngine
{
    public async Task<MatchingResult> PerformMatchingAsync(
        Guid invoiceId,
        MatchingCriteria criteria)
    {
        var invoice = await _repository.GetInvoiceAsync(invoiceId);
        
        // Find potential purchase order matches
        var potentialPOs = await FindPotentialPurchaseOrdersAsync(invoice, criteria);
        
        // Find potential GRN matches
        var potentialGRNs = await FindPotentialGRNsAsync(invoice, criteria);
        
        var matchResults = new List<MatchResult>();
        
        foreach (var po in potentialPOs)
        {
            foreach (var grn in potentialGRNs.Where(g => g.PurchaseOrderId == po.Id))
            {
                var matchResult = await EvaluateThreeWayMatchAsync(po, grn, invoice);
                if (matchResult.Score > criteria.MinimumScore)
                {
                    matchResults.Add(matchResult);
                }
            }
        }
        
        // Sort by match score and select best matches
        var bestMatches = matchResults
            .OrderByDescending(m => m.Score)
            .Take(criteria.MaxResults)
            .ToList();
        
        return new MatchingResult
        {
            InvoiceId = invoiceId,
            Matches = bestMatches,
            AutoMatchCount = bestMatches.Count(m => m.Score > criteria.AutoMatchThreshold),
            SuggestedMatchCount = bestMatches.Count(m => m.Score > criteria.SuggestionThreshold && m.Score <= criteria.AutoMatchThreshold)
        };
    }
}
```

### Tolerance-Based Matching

```csharp
public class ToleranceMatchingService
{
    public async Task<MatchScore> CalculateMatchScoreAsync(
        TransactionLine poLine,
        TransactionLine invoiceLine,
        MatchingTolerance tolerance)
    {
        var score = 100.0; // Start with perfect score
        var penalties = new List<MatchPenalty>();
        
        // Quantity tolerance checking
        if (tolerance.QuantityTolerance.HasValue)
        {
            var qtyDifference = Math.Abs(poLine.Quantity - invoiceLine.Quantity);
            var qtyTolerance = poLine.Quantity * tolerance.QuantityTolerance.Value;
            
            if (qtyDifference > qtyTolerance)
            {
                var penalty = (qtyDifference / poLine.Quantity) * 30; // Max 30% penalty
                penalties.Add(new MatchPenalty("QuantityMismatch", penalty));
            }
        }
        
        // Price tolerance checking
        if (tolerance.PriceTolerance.HasValue)
        {
            var priceDifference = Math.Abs(poLine.UnitPrice - invoiceLine.UnitPrice);
            var priceTolerance = poLine.UnitPrice * tolerance.PriceTolerance.Value;
            
            if (priceDifference > priceTolerance)
            {
                var penalty = (priceDifference / poLine.UnitPrice) * 25; // Max 25% penalty
                penalties.Add(new MatchPenalty("PriceMismatch", penalty));
            }
        }
        
        // Product code matching
        if (!string.Equals(poLine.ProductCode, invoiceLine.ProductCode, StringComparison.OrdinalIgnoreCase))
        {
            penalties.Add(new MatchPenalty("ProductCodeMismatch", 20));
        }
        
        var finalScore = Math.Max(0, score - penalties.Sum(p => p.PenaltyValue));
        
        return new MatchScore
        {
            Score = finalScore,
            Penalties = penalties,
            IsAutoMatch = finalScore >= tolerance.AutoMatchThreshold,
            IsSuggestedMatch = finalScore >= tolerance.SuggestionThreshold
        };
    }
}
```

## Approval Workflows

### Configurable Approval Engine

The approval stage implements flexible, rule-based approval workflows:

```mermaid
graph TB
    subgraph "Approval Configuration"
        AR[Approval Rules] --> AT[Amount Thresholds]
        AR --> RT[Role-Based Rules]
        AR --> CT[Customer-Specific Rules]
        AR --> VR[Vendor Rules]
    end
    
    subgraph "Approval Process"
        AD[Approval Determination] --> S1[Stage 1 Approval]
        S1 --> S2[Stage 2 Approval]
        S2 --> S3[Stage 3 Approval]
        S3 --> FA[Final Approval]
        
        S1 --> E1[Escalation Timer 1]
        S2 --> E2[Escalation Timer 2]
        S3 --> E3[Escalation Timer 3]
        
        E1 --> S2
        E2 --> S3
        E3 --> FA
    end
    
    subgraph "Approval Actions"
        FA --> APP[Approved]
        S1 --> REJ1[Rejected]
        S2 --> REJ2[Rejected]
        S3 --> REJ3[Rejected]
        
        APP --> EX[Export Processing]
        REJ1 --> RH[Rejection Handling]
        REJ2 --> RH
        REJ3 --> RH
    end
    
    style AR fill:#e3f2fd
    style EX fill:#e8f5e8
    style RH fill:#ffebee
```

#### Approval Workflow Engine

```csharp
public class ApprovalWorkflowEngine
{
    public async Task<ApprovalWorkflow> InitiateApprovalAsync(
        Guid documentId,
        ApprovalContext context)
    {
        var document = await _repository.GetDocumentAsync(documentId);
        var approvalRules = await GetApprovalRulesAsync(document.CustomerId);
        
        // Determine approval requirements
        var requirements = await DetermineApprovalRequirementsAsync(document, approvalRules);
        
        if (requirements.RequiresApproval)
        {
            // Create approval workflow
            var workflow = new ApprovalWorkflow
            {
                DocumentId = documentId,
                WorkflowId = Guid.NewGuid(),
                CreatedDate = DateTime.UtcNow,
                Status = ApprovalStatus.Pending,
                Stages = requirements.ApprovalStages.ToList()
            };
            
            await _workflowRepository.SaveAsync(workflow);
            
            // Initiate first approval stage
            await InitiateApprovalStageAsync(workflow.Stages.First());
            
            return workflow;
        }
        else
        {
            // Auto-approve if no approval required
            await TransitionDocumentStateAsync(documentId, DocumentState.Approved, "Auto-approved");
            return null;
        }
    }
    
    private async Task<ApprovalRequirements> DetermineApprovalRequirementsAsync(
        Document document,
        List<ApprovalRule> rules)
    {
        var requirements = new ApprovalRequirements();
        
        foreach (var rule in rules.OrderBy(r => r.Priority))
        {
            if (await rule.AppliesAsync(document))
            {
                await rule.ApplyRequirementsAsync(requirements, document);
            }
        }
        
        return requirements;
    }
}
```

### External Approval Integration

```csharp
public class ExternalApprovalIntegration
{
    public async Task<ApprovalResponse> RequestExternalApprovalAsync(
        ApprovalRequest request,
        ExternalApprovalConfiguration config)
    {
        var httpClient = _httpClientFactory.CreateClient("ExternalApproval");
        
        // Prepare approval request
        var approvalPayload = new
        {
            DocumentId = request.DocumentId,
            Amount = request.Amount,
            Supplier = request.SupplierName,
            RequestedBy = request.RequestedBy,
            DueDate = request.DueDate,
            Priority = request.Priority,
            CustomFields = request.CustomFields
        };
        
        try
        {
            var response = await httpClient.PostAsJsonAsync(config.ApprovalEndpoint, approvalPayload);
            
            if (response.IsSuccessStatusCode)
            {
                var approvalResponse = await response.Content.ReadFromJsonAsync<ExternalApprovalResponse>();
                
                return new ApprovalResponse
                {
                    Success = true,
                    ApprovalId = approvalResponse.ApprovalId,
                    Status = MapExternalStatus(approvalResponse.Status),
                    ApproverComments = approvalResponse.Comments,
                    ProcessedDate = approvalResponse.ProcessedDate
                };
            }
            else
            {
                return new ApprovalResponse
                {
                    Success = false,
                    Error = $"External approval failed: {response.StatusCode}"
                };
            }
        }
        catch (Exception ex)
        {
            return new ApprovalResponse
            {
                Success = false,
                Error = $"External approval error: {ex.Message}"
            };
        }
    }
}
```

## Export and Integration

### Multi-Channel Export Processing

The final stage of the pipeline handles export to various external systems:

```mermaid
graph LR
    subgraph "Export Triggers"
        APR[Approved Document] --> EPQ[Export Queue]
        SC[Schedule Trigger] --> EPQ
        MAN[Manual Export] --> EPQ
    end
    
    subgraph "Export Processing"
        EPQ --> EE[Export Engine]
        EE --> DT[Data Transformation]
        DT --> FG[Format Generation]
        FG --> TC[Transport Selection]
    end
    
    subgraph "Transport Channels"
        TC --> HTTP[HTTP/REST]
        TC --> FTP[FTP/SFTP]
        TC --> EMAIL[Email Delivery]
        TC --> AS2[AS2 Protocol]
        TC --> FILE[File System]
    end
    
    subgraph "Delivery Confirmation"
        HTTP --> CF[Confirmation Processing]
        FTP --> CF
        EMAIL --> CF
        AS2 --> CF
        FILE --> CF
        
        CF --> ACK[Acknowledgement]
        CF --> ERR[Error Handling]
    end
    
    style APR fill:#e3f2fd
    style ACK fill:#e8f5e8
    style ERR fill:#ffebee
```

#### Export Engine Implementation

```csharp
public class DocumentExportEngine
{
    public async Task<ExportResult> ExportDocumentAsync(
        Guid documentId,
        ExportConfiguration config)
    {
        var document = await _repository.GetDocumentAsync(documentId);
        
        // Transform document data
        var transformedData = await TransformDocumentDataAsync(document, config.MappingRules);
        
        // Generate export format
        var exportData = await GenerateExportFormatAsync(transformedData, config.OutputFormat);
        
        // Select transport method
        var transport = _transportFactory.CreateTransport(config.TransportType);
        
        // Perform export
        var deliveryResult = await transport.DeliverAsync(exportData, config.TransportSettings);
        
        // Process confirmation
        await ProcessExportConfirmationAsync(document, deliveryResult);
        
        return new ExportResult
        {
            DocumentId = documentId,
            Success = deliveryResult.Success,
            TrackingId = deliveryResult.TrackingId,
            DeliveryTimestamp = deliveryResult.DeliveryTimestamp,
            Error = deliveryResult.Error
        };
    }
}
```

## Error Handling and Recovery

### Comprehensive Error Management

```csharp
public class PipelineErrorHandler
{
    public async Task<ErrorHandlingResult> HandleProcessingErrorAsync(
        Guid documentId,
        ProcessingStage stage,
        Exception error)
    {
        var document = await _repository.GetDocumentAsync(documentId);
        var errorPolicy = await GetErrorPolicyAsync(document.CustomerId, stage);
        
        // Log the error
        await _logger.LogErrorAsync(error, "Document processing error", new
        {
            DocumentId = documentId,
            Stage = stage,
            CustomerId = document.CustomerId
        });
        
        // Determine recovery action
        var recoveryAction = DetermineRecoveryAction(error, errorPolicy);
        
        switch (recoveryAction)
        {
            case RecoveryAction.Retry:
                return await ScheduleRetryAsync(documentId, stage, errorPolicy.RetryDelay);
                
            case RecoveryAction.Suspense:
                return await MoveTo SuspenseAsync(documentId, error.Message);
                
            case RecoveryAction.ManualIntervention:
                return await FlagForManualReviewAsync(documentId, error);
                
            case RecoveryAction.Fail:
                return await MarkAsFailedAsync(documentId, error);
                
            default:
                throw new InvalidOperationException($"Unknown recovery action: {recoveryAction}");
        }
    }
}
```

## Performance Monitoring

### Pipeline Metrics and Monitoring

```csharp
public class PipelinePerformanceMonitor
{
    public async Task TrackProcessingMetricsAsync(
        Guid documentId,
        ProcessingStage stage,
        TimeSpan duration,
        ProcessingResult result)
    {
        var metrics = new ProcessingMetrics
        {
            DocumentId = documentId,
            Stage = stage,
            Duration = duration,
            Success = result.Success,
            Timestamp = DateTime.UtcNow
        };
        
        // Record performance metrics
        _metricsCollector.RecordProcessingTime(stage.ToString(), duration);
        _metricsCollector.IncrementCounter($"documents_processed_{stage}");
        
        if (!result.Success)
        {
            _metricsCollector.IncrementCounter($"documents_failed_{stage}");
        }
        
        // Store detailed metrics
        await _metricsRepository.SaveAsync(metrics);
        
        // Check for performance thresholds
        await CheckPerformanceThresholdsAsync(stage, duration);
    }
}
```

## Related Documents

- **[PDFProcessingEngine.md](./PDFProcessingEngine.md)** - Detailed PDF processing capabilities
- **[BusinessLogicArchitecture.md](./BusinessLogicArchitecture.md)** - Business logic implementation
- **[BackgroundJobProcessing.md](./BackgroundJobProcessing.md)** - Asynchronous job processing
- **[IntegrationSystems.md](./IntegrationSystems.md)** - External system integration
- **[WorkflowEngine.md](./WorkflowEngine.md)** - Workflow orchestration details

---

*This document processing pipeline documentation provides comprehensive guidance for understanding the complete document lifecycle in eHub. The state-driven workflow, multi-layer validation, and sophisticated matching capabilities make this a robust platform for enterprise document processing operations.*