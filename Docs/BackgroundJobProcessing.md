# eHub Background Job Processing

## Purpose & Scope

This document provides comprehensive documentation of the eHub background job processing system, including Hangfire integration, job scheduling patterns, distributed processing architecture, error handling, and monitoring capabilities. It serves as the definitive guide for understanding and working with the asynchronous processing infrastructure that powers the eHub platform.

## Prerequisites

- Understanding of distributed systems and message queuing concepts
- Knowledge of Hangfire framework and background job processing
- Familiarity with the eHub business processes and document workflows
- Basic understanding of Azure Service Bus and distributed architectures

## Core Concepts

### Background Processing Philosophy

The eHub background job processing system is built on several key principles:

1. **Asynchronous Processing**: Long-running operations are decoupled from user interactions
2. **Scalable Architecture**: Distributed processing across multiple server instances
3. **Reliable Execution**: Comprehensive retry logic and error handling
4. **Priority-Based Scheduling**: Critical jobs processed before lower-priority tasks
5. **Blue/Green Deployment**: Zero-downtime deployments with job migration

### Background Processing Overview

```mermaid
graph TB
    subgraph "Job Sources"
        UI[User Interface] --> JQ[Job Queue]
        API[API Endpoints] --> JQ
        SCH[Scheduled Tasks] --> JQ
        EXT[External Systems] --> JQ
    end
    
    subgraph "Hangfire Infrastructure"
        JQ --> HF[Hangfire Server]
        HF --> JS[Job Storage<br/>SQL Server]
        HF --> QM[Queue Manager]
        QM --> WN[Worker Nodes]
    end
    
    subgraph "Job Processing"
        WN --> DP[Document Processing]
        WN --> EP[Email Processing]
        WN --> EX[Export Jobs]
        WN --> MJ[Maintenance Jobs]
        WN --> CJ[Cleanup Jobs]
    end
    
    subgraph "Monitoring & Management"
        HF --> HD[Hangfire Dashboard]
        HF --> JM[Job Monitoring]
        JM --> AL[Alerting]
        JM --> PM[Performance Metrics]
    end
    
    style UI fill:#e3f2fd
    style HD fill:#e8f5e8
    style AL fill:#ffebee
```

## Hangfire Architecture and Configuration

### Hangfire Server Setup

**Location**: `Hangfire/`

The Hangfire service provides the core background processing infrastructure:

```csharp
public class Startup
{
    public void ConfigureServices(IServiceCollection services)
    {
        // Hangfire configuration with SQL Server storage
        services.AddHangfire(configuration => configuration
            .SetDataCompatibilityLevel(CompatibilityLevel.Version_170)
            .UseSimpleAssemblyNameTypeSerializer()
            .UseRecommendedSerializerSettings()
            .UseSqlServerStorage(connectionString, new SqlServerStorageOptions
            {
                CommandBatchMaxTimeout = TimeSpan.FromMinutes(5),
                SlidingInvisibilityTimeout = TimeSpan.FromMinutes(5),
                QueuePollInterval = TimeSpan.Zero,
                UseRecommendedIsolationLevel = true,
                DisableGlobalLocks = true,
                EnableHeavyMigrations = true
            }));
        
        // Hangfire server configuration
        services.AddHangfireServer(options =>
        {
            options.WorkerCount = Environment.ProcessorCount * 2;
            options.Queues = new[] { "critical", "default", "background" };
            options.ServerName = Environment.MachineName;
            options.ServerTimeout = TimeSpan.FromMinutes(4);
            options.HeartbeatInterval = TimeSpan.FromSeconds(30);
            options.ServerCheckInterval = TimeSpan.FromMinutes(1);
        });
    }
    
    public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
    {
        // Hangfire dashboard with custom authorization
        app.UseHangfireDashboard("/hangfire", new DashboardOptions
        {
            Authorization = new[] { new HangfireAuthorizationFilter() },
            StatsPollingInterval = 2000,
            DisplayStorageConnectionString = false
        });
        
        // Start Hangfire server
        app.UseHangfireServer();
    }
}
```

### Queue Management and Prioritisation

```mermaid
graph LR
    subgraph "Queue Priority System"
        CQ[Critical Queue<br/>Priority: 1] --> HF[Hangfire Server]
        DQ[Default Queue<br/>Priority: 2] --> HF
        BQ[Background Queue<br/>Priority: 3] --> HF
        LQ[Low Priority Queue<br/>Priority: 4] --> HF
    end
    
    subgraph "Job Types by Queue"
        CQ --> CJ[Critical Jobs<br/>System Failures<br/>Data Corruption]
        DQ --> DJ[Default Jobs<br/>Document Processing<br/>User Requests]
        BQ --> BJ[Background Jobs<br/>Email Processing<br/>Scheduled Tasks]
        LQ --> LJ[Low Priority<br/>Cleanup<br/>Reporting]
    end
    
    style CQ fill:#ffebee
    style DQ fill:#fff3e0
    style BQ fill:#e8f5e8
    style LQ fill:#f3e5f5
```

#### Queue Configuration

```csharp
public static class QueueNames
{
    public const string Critical = "critical";
    public const string Default = "default";
    public const string Background = "background";
    public const string LowPriority = "low";
    public const string Maintenance = "maintenance";
}

public class QueueManager
{
    public static void EnqueueJob<T>(Expression<Func<T, Task>> methodCall, QueuePriority priority = QueuePriority.Default)
    {
        var queueName = GetQueueName(priority);
        
        BackgroundJob.Enqueue(methodCall, new EnqueuedState(queueName));
    }
    
    public static void ScheduleJob<T>(Expression<Func<T, Task>> methodCall, TimeSpan delay, QueuePriority priority = QueuePriority.Default)
    {
        var queueName = GetQueueName(priority);
        
        BackgroundJob.Schedule(methodCall, delay, new EnqueuedState(queueName));
    }
    
    private static string GetQueueName(QueuePriority priority)
    {
        return priority switch
        {
            QueuePriority.Critical => QueueNames.Critical,
            QueuePriority.Default => QueueNames.Default,
            QueuePriority.Background => QueueNames.Background,
            QueuePriority.Low => QueueNames.LowPriority,
            QueuePriority.Maintenance => QueueNames.Maintenance,
            _ => QueueNames.Default
        };
    }
}
```

## Job Types and Processing Patterns

### Document Processing Jobs

Document processing represents the core background workload in eHub:

```mermaid
sequenceDiagram
    participant UI as User Interface
    participant API as API Controller
    participant JM as Job Manager
    participant HF as Hangfire
    participant DP as Document Processor
    participant DB as Database
    
    UI->>API: Upload Document
    API->>JM: Queue Processing Job
    JM->>HF: Enqueue Document Job
    API-->>UI: Upload Confirmation
    
    HF->>DP: Start Processing
    DP->>DP: Extract Document
    DP->>DP: Validate Data
    DP->>DP: Apply Business Rules
    DP->>DB: Save Results
    
    DP->>HF: Job Complete
    HF->>JM: Trigger Next Job
    JM->>HF: Queue Matching Job
```

#### Document Processing Job Implementation

```csharp
public class DocumentProcessingJob
{
    private readonly IDocumentProcessor _processor;
    private readonly ILogger<DocumentProcessingJob> _logger;
    
    public DocumentProcessingJob(
        IDocumentProcessor processor,
        ILogger<DocumentProcessingJob> logger)
    {
        _processor = processor;
        _logger = logger;
    }
    
    [AutomaticRetry(Attempts = 3, DelaysInSeconds = new[] { 30, 300, 1800 })]
    public async Task ProcessDocumentAsync(Guid documentId, ProcessingOptions options)
    {
        try
        {
            _logger.LogInformation("Starting document processing for {DocumentId}", documentId);
            
            // Update job status
            await UpdateJobStatusAsync(documentId, JobStatus.Processing);
            
            // Process document through pipeline
            var result = await _processor.ProcessDocumentAsync(documentId, options);
            
            if (result.Success)
            {
                await UpdateJobStatusAsync(documentId, JobStatus.Completed);
                
                // Queue dependent jobs
                await QueueDependentJobsAsync(documentId, result);
                
                _logger.LogInformation("Document processing completed for {DocumentId}", documentId);
            }
            else
            {
                await HandleProcessingFailureAsync(documentId, result.Errors);
            }
        }
        catch (TemporaryException ex)
        {
            _logger.LogWarning(ex, "Temporary processing failure for {DocumentId}", documentId);
            throw; // Allow retry
        }
        catch (PermanentException ex)
        {
            _logger.LogError(ex, "Permanent processing failure for {DocumentId}", documentId);
            await MoveTo suspenseAsync(documentId, ex.Message);
        }
    }
    
    private async Task QueueDependentJobsAsync(Guid documentId, ProcessingResult result)
    {
        // Queue validation job
        QueueManager.EnqueueJob<ValidationJob>(
            job => job.ValidateDocumentAsync(documentId),
            QueuePriority.Default);
        
        // Queue matching job if applicable
        if (result.RequiresMatching)
        {
            QueueManager.EnqueueJob<MatchingJob>(
                job => job.PerformMatchingAsync(documentId),
                QueuePriority.Default);
        }
        
        // Queue notification job
        QueueManager.EnqueueJob<NotificationJob>(
            job => job.SendProcessingNotificationAsync(documentId),
            QueuePriority.Background);
    }
}
```

### Email Processing Jobs

Email processing handles automated document ingestion and notifications:

```csharp
public class EmailProcessingJob
{
    [DisableConcurrentExecution(timeoutInSeconds: 60)]
    [AutomaticRetry(Attempts = 2)]
    public async Task ProcessInboxAsync(Guid inboxConfigId)
    {
        var config = await _configRepository.GetEmailInboxConfigAsync(inboxConfigId);
        
        using var emailClient = _emailClientFactory.CreateClient(config);
        await emailClient.ConnectAsync();
        
        var messages = await emailClient.GetUnreadMessagesAsync();
        
        foreach (var message in messages)
        {
            try
            {
                await ProcessEmailMessageAsync(message, config);
                await emailClient.MarkAsReadAsync(message.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process email {MessageId}", message.Id);
                await emailClient.MarkAsErrorAsync(message.Id);
            }
        }
        
        await emailClient.DisconnectAsync();
    }
    
    private async Task ProcessEmailMessageAsync(EmailMessage message, EmailInboxConfig config)
    {
        // Extract attachments
        var attachments = await ExtractAttachmentsAsync(message);
        
        foreach (var attachment in attachments)
        {
            if (IsProcessableDocument(attachment))
            {
                // Store document
                var documentId = await StoreDocumentAsync(attachment, config.CustomerId);
                
                // Queue document processing
                QueueManager.EnqueueJob<DocumentProcessingJob>(
                    job => job.ProcessDocumentAsync(documentId, ProcessingOptions.EmailSource),
                    QueuePriority.Default);
            }
        }
    }
}
```

### Scheduled and Recurring Jobs

```csharp
public class ScheduledJobManager
{
    public static void ConfigureRecurringJobs()
    {
        // Email polling jobs - every 5 minutes
        RecurringJob.AddOrUpdate<EmailProcessingJob>(
            "email-polling",
            job => job.ProcessAllInboxesAsync(),
            "*/5 * * * *", // Every 5 minutes
            new RecurringJobOptions
            {
                Queue = QueueNames.Background,
                TimeZone = TimeZoneInfo.Utc
            });
        
        // FTP polling jobs - every 10 minutes
        RecurringJob.AddOrUpdate<FTPPollingJob>(
            "ftp-polling",
            job => job.PollAllFTPServersAsync(),
            "*/10 * * * *", // Every 10 minutes
            new RecurringJobOptions
            {
                Queue = QueueNames.Background,
                TimeZone = TimeZoneInfo.Utc
            });
        
        // Daily maintenance - 2 AM UTC
        RecurringJob.AddOrUpdate<MaintenanceJob>(
            "daily-maintenance",
            job => job.PerformDailyMaintenanceAsync(),
            "0 2 * * *", // Daily at 2 AM
            new RecurringJobOptions
            {
                Queue = QueueNames.Maintenance,
                TimeZone = TimeZoneInfo.Utc
            });
        
        // Weekly cleanup - Sunday 3 AM UTC
        RecurringJob.AddOrUpdate<CleanupJob>(
            "weekly-cleanup",
            job => job.PerformWeeklyCleanupAsync(),
            "0 3 * * 0", // Sunday at 3 AM
            new RecurringJobOptions
            {
                Queue = QueueNames.LowPriority,
                TimeZone = TimeZoneInfo.Utc
            });
    }
}
```

## Blue/Green Deployment Support

### Zero-Downtime Job Migration

The eHub platform supports blue/green deployments with seamless job migration:

```mermaid
graph TB
    subgraph "Blue Environment (Current)"
        BH[Blue Hangfire Server] --> BJS[Blue Job Storage]
        BH --> BQ[Blue Queue Processing]
    end
    
    subgraph "Green Environment (New)"
        GH[Green Hangfire Server] --> GJS[Green Job Storage]
        GH --> GQ[Green Queue Processing]
    end
    
    subgraph "Deployment Process"
        DP[Deployment Process] --> SP[Stop Blue Processing]
        SP --> MJ[Migrate Jobs]
        MJ --> SG[Start Green Processing]
        SG --> VG[Verify Green Health]
        VG --> DB[Decommission Blue]
    end
    
    BJS -.-> GJS
    BQ -.-> GQ
    
    style BH fill:#e3f2fd
    style GH fill:#e8f5e8
    style DP fill:#fff3e0
```

#### Deployment Coordination

```csharp
public class BlueGreenDeploymentCoordinator
{
    public async Task<DeploymentResult> CoordinateDeploymentAsync(DeploymentPlan plan)
    {
        var currentColor = await GetCurrentDeploymentColorAsync();
        var targetColor = currentColor == DeploymentColor.Blue ? DeploymentColor.Green : DeploymentColor.Blue;
        
        _logger.LogInformation("Starting blue/green deployment from {CurrentColor} to {TargetColor}", 
            currentColor, targetColor);
        
        try
        {
            // 1. Prepare target environment
            await PrepareTargetEnvironmentAsync(targetColor);
            
            // 2. Gracefully stop processing new jobs in current environment
            await StopAcceptingNewJobsAsync(currentColor);
            
            // 3. Wait for current jobs to complete (with timeout)
            await WaitForJobCompletionAsync(currentColor, TimeSpan.FromMinutes(10));
            
            // 4. Migrate pending jobs to target environment
            await MigrateJobsAsync(currentColor, targetColor);
            
            // 5. Start target environment
            await StartTargetEnvironmentAsync(targetColor);
            
            // 6. Verify target environment health
            await VerifyEnvironmentHealthAsync(targetColor);
            
            // 7. Update routing to target environment
            await UpdateRoutingAsync(targetColor);
            
            // 8. Decommission previous environment
            await DecommissionEnvironmentAsync(currentColor);
            
            return DeploymentResult.Success(targetColor);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Deployment failed, rolling back");
            await RollbackDeploymentAsync(currentColor);
            throw;
        }
    }
}
```

### Job Migration Strategy

```csharp
public class JobMigrationService
{
    public async Task MigrateJobsAsync(DeploymentColor source, DeploymentColor target)
    {
        var sourceStorage = GetHangfireStorage(source);
        var targetStorage = GetHangfireStorage(target);
        
        // Get all pending jobs from source
        var pendingJobs = await GetPendingJobsAsync(sourceStorage);
        var scheduledJobs = await GetScheduledJobsAsync(sourceStorage);
        var recurringJobs = await GetRecurringJobsAsync(sourceStorage);
        
        // Migrate pending jobs
        foreach (var job in pendingJobs)
        {
            await MigratePendingJobAsync(job, targetStorage);
        }
        
        // Migrate scheduled jobs
        foreach (var job in scheduledJobs)
        {
            await MigrateScheduledJobAsync(job, targetStorage);
        }
        
        // Recreate recurring jobs in target environment
        foreach (var job in recurringJobs)
        {
            await RecreateRecurringJobAsync(job, targetStorage);
        }
        
        _logger.LogInformation("Migrated {PendingCount} pending, {ScheduledCount} scheduled, and {RecurringCount} recurring jobs",
            pendingJobs.Count, scheduledJobs.Count, recurringJobs.Count);
    }
}
```

## Error Handling and Resilience

### Comprehensive Retry Logic

```mermaid
flowchart TD
    JS[Job Start] --> EX[Execute Job]
    EX --> S{Success?}
    S -->|Yes| JC[Job Complete]
    S -->|No| ET[Error Type?]
    
    ET -->|Temporary| RC[Retry Count < Max?]
    ET -->|Permanent| DLQ[Dead Letter Queue]
    ET -->|Business| SUS[Move to Suspense]
    
    RC -->|Yes| WD[Wait Delay]
    RC -->|No| DLQ
    
    WD --> BD[Backoff Delay]
    BD --> EX
    
    JC --> END[End]
    DLQ --> ALT[Alert & Log]
    SUS --> ALT
    ALT --> END
    
    style JS fill:#e3f2fd
    style JC fill:#e8f5e8
    style DLQ fill:#ffebee
    style SUS fill:#fff3e0
```

#### Advanced Retry Strategies

```csharp
public class RetryPolicyBuilder
{
    public static AutomaticRetryAttribute BuildRetryPolicy(JobType jobType)
    {
        return jobType switch
        {
            JobType.DocumentProcessing => new AutomaticRetryAttribute
            {
                Attempts = 3,
                DelaysInSeconds = new[] { 30, 300, 1800 }, // 30s, 5m, 30m
                LogEvents = true,
                OnAttemptsExceeded = AttemptsExceededAction.Delete
            },
            
            JobType.EmailProcessing => new AutomaticRetryAttribute
            {
                Attempts = 5,
                DelaysInSeconds = new[] { 60, 300, 900, 1800, 3600 }, // Progressive backoff
                LogEvents = true,
                OnAttemptsExceeded = AttemptsExceededAction.Delete
            },
            
            JobType.ExportProcessing => new AutomaticRetryAttribute
            {
                Attempts = 2,
                DelaysInSeconds = new[] { 120, 600 }, // 2m, 10m
                LogEvents = true,
                OnAttemptsExceeded = AttemptsExceededAction.Delete
            },
            
            JobType.MaintenanceJob => new AutomaticRetryAttribute
            {
                Attempts = 1, // Don't retry maintenance jobs
                LogEvents = true,
                OnAttemptsExceeded = AttemptsExceededAction.Delete
            },
            
            _ => new AutomaticRetryAttribute
            {
                Attempts = 3,
                DelaysInSeconds = new[] { 60, 300, 900 },
                LogEvents = true
            }
        };
    }
}
```

### Dead Letter Queue Management

```csharp
public class DeadLetterQueueProcessor
{
    [DisableConcurrentExecution(timeoutInSeconds: 300)]
    public async Task ProcessDeadLetterQueueAsync()
    {
        var deadLetterJobs = await GetDeadLetterJobsAsync();
        
        foreach (var deadJob in deadLetterJobs)
        {
            try
            {
                var analysis = await AnalyseJobFailureAsync(deadJob);
                
                switch (analysis.RecommendedAction)
                {
                    case DeadLetterAction.Retry:
                        await RetryDeadJobAsync(deadJob);
                        break;
                        
                    case DeadLetterAction.FixAndRetry:
                        await ApplyDataFixAsync(deadJob, analysis.FixInstructions);
                        await RetryDeadJobAsync(deadJob);
                        break;
                        
                    case DeadLetterAction.ManualIntervention:
                        await FlagForManualReviewAsync(deadJob, analysis.Reason);
                        break;
                        
                    case DeadLetterAction.Discard:
                        await DiscardDeadJobAsync(deadJob, analysis.Reason);
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process dead letter job {JobId}", deadJob.Id);
            }
        }
    }
}
```

## Performance Monitoring and Metrics

### Real-Time Job Monitoring

```csharp
public class JobPerformanceMonitor
{
    public async Task TrackJobPerformanceAsync(JobExecutionContext context)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            // Execute job with monitoring
            await ExecuteJobWithMonitoringAsync(context);
            
            stopwatch.Stop();
            
            // Record successful execution metrics
            await RecordJobMetricsAsync(new JobMetrics
            {
                JobId = context.JobId,
                JobType = context.JobType,
                Duration = stopwatch.Elapsed,
                Success = true,
                Queue = context.Queue,
                ServerName = Environment.MachineName,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            
            // Record failure metrics
            await RecordJobMetricsAsync(new JobMetrics
            {
                JobId = context.JobId,
                JobType = context.JobType,
                Duration = stopwatch.Elapsed,
                Success = false,
                Queue = context.Queue,
                ServerName = Environment.MachineName,
                ErrorMessage = ex.Message,
                Timestamp = DateTime.UtcNow
            });
            
            throw;
        }
    }
    
    public async Task<PerformanceReport> GeneratePerformanceReportAsync(TimeSpan period)
    {
        var endTime = DateTime.UtcNow;
        var startTime = endTime.Subtract(period);
        
        var metrics = await GetJobMetricsAsync(startTime, endTime);
        
        return new PerformanceReport
        {
            Period = period,
            TotalJobs = metrics.Count,
            SuccessfulJobs = metrics.Count(m => m.Success),
            FailedJobs = metrics.Count(m => !m.Success),
            AverageExecutionTime = TimeSpan.FromMilliseconds(metrics.Average(m => m.Duration.TotalMilliseconds)),
            P95ExecutionTime = CalculatePercentile(metrics.Select(m => m.Duration), 0.95),
            JobsByQueue = metrics.GroupBy(m => m.Queue).ToDictionary(g => g.Key, g => g.Count()),
            JobsByType = metrics.GroupBy(m => m.JobType).ToDictionary(g => g.Key, g => g.Count())
        };
    }
}
```

### Alerting and Notifications

```mermaid
graph TB
    subgraph "Monitoring Triggers"
        HFA[High Failure Rate] --> AS[Alert System]
        LPT[Long Processing Time] --> AS
        QBU[Queue Build-up] --> AS
        SER[Server Errors] --> AS
        MEM[Memory Issues] --> AS
    end
    
    subgraph "Alert Processing"
        AS --> AR[Alert Rules]
        AR --> SEV[Severity Assessment]
        SEV --> NOT[Notification Routing]
    end
    
    subgraph "Notification Channels"
        NOT --> EM[Email Alerts]
        NOT --> SMS[SMS Alerts]
        NOT --> SLK[Slack Integration]
        NOT --> PD[PagerDuty]
        NOT --> DASH[Dashboard Alerts]
    end
    
    style HFA fill:#ffebee
    style AS fill:#fff3e0
    style EM fill:#e8f5e8
```

#### Alert Configuration

```csharp
public class JobAlertingService
{
    public async Task CheckAlertConditionsAsync()
    {
        var checks = new List<IAlertCheck>
        {
            new HighFailureRateCheck(),
            new QueueBacklogCheck(),
            new LongRunningJobCheck(),
            new ServerHealthCheck(),
            new ResourceUtilisationCheck()
        };
        
        foreach (var check in checks)
        {
            var result = await check.EvaluateAsync();
            
            if (result.ShouldAlert)
            {
                await ProcessAlertAsync(result);
            }
        }
    }
    
    private async Task ProcessAlertAsync(AlertResult result)
    {
        var alert = new Alert
        {
            Id = Guid.NewGuid(),
            Type = result.AlertType,
            Severity = result.Severity,
            Message = result.Message,
            Source = result.Source,
            Timestamp = DateTime.UtcNow,
            Data = result.AdditionalData
        };
        
        // Store alert
        await _alertRepository.SaveAsync(alert);
        
        // Send notifications based on severity
        var notificationChannels = GetNotificationChannels(result.Severity);
        
        foreach (var channel in notificationChannels)
        {
            await channel.SendNotificationAsync(alert);
        }
    }
}
```

## Job Coordination and Dependencies

### Complex Job Workflows

```csharp
public class JobWorkflowCoordinator
{
    public async Task<string> StartDocumentProcessingWorkflowAsync(Guid documentId)
    {
        // Create job workflow
        var workflowId = Guid.NewGuid().ToString();
        
        // Step 1: Document extraction
        var extractionJobId = BackgroundJob.Enqueue<DocumentExtractionJob>(
            job => job.ExtractDocumentAsync(documentId, workflowId));
        
        // Step 2: Validation (depends on extraction)
        var validationJobId = BackgroundJob.ContinueJobWith<DocumentValidationJob>(
            extractionJobId,
            job => job.ValidateDocumentAsync(documentId, workflowId));
        
        // Step 3: Matching (depends on validation)
        var matchingJobId = BackgroundJob.ContinueJobWith<DocumentMatchingJob>(
            validationJobId,
            job => job.PerformMatchingAsync(documentId, workflowId));
        
        // Step 4: Approval workflow (depends on matching)
        var approvalJobId = BackgroundJob.ContinueJobWith<ApprovalWorkflowJob>(
            matchingJobId,
            job => job.InitiateApprovalAsync(documentId, workflowId));
        
        // Step 5: Export (depends on approval)
        var exportJobId = BackgroundJob.ContinueJobWith<DocumentExportJob>(
            approvalJobId,
            job => job.ExportDocumentAsync(documentId, workflowId));
        
        // Step 6: Cleanup (depends on export)
        BackgroundJob.ContinueJobWith<CleanupJob>(
            exportJobId,
            job => job.CleanupWorkflowAsync(documentId, workflowId));
        
        return workflowId;
    }
}
```

### Batch Processing

```csharp
public class BatchJobProcessor
{
    public async Task ProcessDocumentBatchAsync(List<Guid> documentIds, BatchProcessingOptions options)
    {
        var batchId = Guid.NewGuid();
        var batchSize = options.BatchSize ?? 10;
        
        // Split documents into batches
        var batches = documentIds
            .Select((id, index) => new { Id = id, Index = index })
            .GroupBy(x => x.Index / batchSize)
            .Select(g => g.Select(x => x.Id).ToList())
            .ToList();
        
        // Process batches in parallel with controlled concurrency
        var semaphore = new SemaphoreSlim(options.MaxConcurrency ?? 3);
        var tasks = batches.Select(async batch =>
        {
            await semaphore.WaitAsync();
            try
            {
                return await ProcessBatchAsync(batch, batchId);
            }
            finally
            {
                semaphore.Release();
            }
        });
        
        var results = await Task.WhenAll(tasks);
        
        // Queue batch completion job
        QueueManager.EnqueueJob<BatchCompletionJob>(
            job => job.CompleteBatchAsync(batchId, results),
            QueuePriority.Default);
    }
}
```

## Security and Access Control

### Job Security Considerations

```csharp
public class SecureJobExecutor
{
    public async Task ExecuteSecureJobAsync<T>(Expression<Func<T, Task>> jobExpression, SecurityContext context)
    {
        // Validate security context
        if (!await ValidateSecurityContextAsync(context))
        {
            throw new UnauthorisedAccessException("Invalid security context for job execution");
        }
        
        // Create secure job wrapper
        var secureJob = new SecureJobWrapper<T>
        {
            JobExpression = jobExpression,
            SecurityContext = context,
            Timestamp = DateTime.UtcNow
        };
        
        // Enqueue with security metadata
        BackgroundJob.Enqueue(() => ExecuteWithSecurityAsync(secureJob));
    }
    
    public async Task ExecuteWithSecurityAsync<T>(SecureJobWrapper<T> wrapper)
    {
        // Re-validate security context (in case of delayed execution)
        if (!await ValidateSecurityContextAsync(wrapper.SecurityContext))
        {
            throw new UnauthorisedAccessException("Security context expired or invalid");
        }
        
        // Set security context for job execution
        using var securityScope = CreateSecurityScope(wrapper.SecurityContext);
        
        // Execute the actual job
        var service = _serviceProvider.GetRequiredService<T>();
        await wrapper.JobExpression.Compile()(service);
    }
}
```

## Troubleshooting and Diagnostics

### Common Job Issues

```csharp
public class JobDiagnosticsService
{
    public async Task<DiagnosticsReport> GenerateDiagnosticsReportAsync()
    {
        var report = new DiagnosticsReport();
        
        // Check queue health
        report.QueueHealth = await CheckQueueHealthAsync();
        
        // Check server health
        report.ServerHealth = await CheckServerHealthAsync();
        
        // Check job failures
        report.RecentFailures = await GetRecentJobFailuresAsync();
        
        // Check performance metrics
        report.PerformanceMetrics = await GetPerformanceMetricsAsync();
        
        // Check resource utilisation
        report.ResourceUtilisation = await GetResourceUtilisationAsync();
        
        return report;
    }
    
    public async Task<List<JobIssue>> IdentifyJobIssuesAsync()
    {
        var issues = new List<JobIssue>();
        
        // Check for stuck jobs
        var stuckJobs = await FindStuckJobsAsync();
        if (stuckJobs.Any())
        {
            issues.Add(new JobIssue
            {
                Type = JobIssueType.StuckJobs,
                Severity = IssueSeverity.High,
                Description = $"{stuckJobs.Count} jobs appear to be stuck",
                AffectedJobs = stuckJobs
            });
        }
        
        // Check for high failure rates
        var failureRate = await CalculateRecentFailureRateAsync();
        if (failureRate > 0.1) // More than 10% failure rate
        {
            issues.Add(new JobIssue
            {
                Type = JobIssueType.HighFailureRate,
                Severity = IssueSeverity.Medium,
                Description = $"Job failure rate is {failureRate:P1}"
            });
        }
        
        return issues;
    }
}
```

## Related Documents

- **[DocumentProcessingPipeline.md](./DocumentProcessingPipeline.md)** - Document processing workflows
- **[BusinessLogicArchitecture.md](./BusinessLogicArchitecture.md)** - Business logic executed in jobs
- **[IntegrationSystems.md](./IntegrationSystems.md)** - External system integration jobs
- **[PerformanceAndScaling.md](./PerformanceAndScaling.md)** - Performance optimisation strategies
- **[MaintenanceAndOperations.md](./MaintenanceAndOperations.md)** - Operational procedures

---

*This background job processing documentation provides comprehensive guidance for understanding and working with the sophisticated asynchronous processing infrastructure in eHub. The Hangfire-based architecture, blue/green deployment support, and comprehensive monitoring make this a robust platform for enterprise-scale background processing operations.*