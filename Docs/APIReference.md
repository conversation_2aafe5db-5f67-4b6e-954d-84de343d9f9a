# eHub API Reference

## Purpose & Scope

This document provides comprehensive REST API documentation for the eHub electronic trading platform. It serves as the definitive reference for external integration developers who need to integrate with eHub's document processing, transaction management, and workflow systems. The API enables secure, multi-tenant access to eHub's core business functionality through well-defined REST endpoints.

## Prerequisites

- **HTTP/REST Knowledge**: Understanding of REST API principles and HTTP methods
- **Authentication**: Familiarity with JWT tokens and bearer authentication
- **JSON Processing**: Ability to work with JSON request/response payloads
- **Multi-tenancy**: Understanding of tenant-based data isolation
- **Business Context**: Basic knowledge of electronic document processing workflows

## Core Concepts

### API Architecture Overview

The eHub API follows a business-domain-focused design rather than strict RESTful conventions, optimised for complex document processing workflows and multi-tenant operations.

```mermaid
graph TB
    subgraph "eHub API Ecosystem"
        MAIN[ETradingAPI - Main REST API]
        IO[ECXIO.Api - Integration API]
        RPT[Reporting.Api - Analytics API]
        SCH[Schedule.Api - Job Management API]
    end
    
    subgraph "Core Domains"
        DOC[Document Management]
        TXN[Transaction Processing]
        CUST[Customer Management]
        WF[Workflow Management]
        RPT_DOM[Reporting & Analytics]
    end
    
    subgraph "Authentication & Security"
        JWT[JWT Bearer Tokens]
        TENANT[Multi-tenant Isolation]
        RBAC[Role-based Access Control]
    end
    
    MAIN --> DOC
    MAIN --> TXN
    MAIN --> CUST
    MAIN --> WF
    IO --> DOC
    RPT --> RPT_DOM
    
    JWT --> MAIN
    TENANT --> MAIN
    RBAC --> MAIN
```

### API Design Principles

#### POST-Heavy Design Pattern
eHub uses POST for most operations, including data retrieval, to support complex filtering and search parameters:

```http
# Traditional REST approach
GET /api/customers/123

# eHub approach - supports complex filtering
POST /api/customers/GetCustomer
Content-Type: application/json

{
  "customerId": "123e4567-e89b-12d3-a456-************",
  "includeDetails": true,
  "filterOptions": {
    "includeInactive": false
  }
}
```

#### Multi-tenant Architecture
All API endpoints operate within a tenant context, ensuring data isolation:

```http
# Tenant-scoped endpoint pattern
POST /api/members/{customerId}/transactions/GetTransactionList
```

#### Consistent Response Patterns
All endpoints follow standardised response structures for predictable integration:

```json
{
  "isSuccess": true,
  "data": { /* response payload */ },
  "error": null,
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## Authentication & Security

### JWT Bearer Authentication

All API endpoints (except health checks and test endpoints) require JWT bearer token authentication.

#### Authentication Headers

```http
Authorization: Bearer <jwt-token>
EHubBearer: <jwt-token>
```

#### Required JWT Claims

```json
{
  "UserId": "123e4567-e89b-12d3-a456-************",
  "fullName": "John Smith",
  "EHubCustomerId": "987fcdeb-51a2-43d1-9f12-123456789abc",
  "memberName": "Acme Corporation",
  "iss": "eHub-Auth",
  "exp": **********,
  "iat": **********
}
```

#### User Context Headers

Many endpoints require additional user context headers for audit and security purposes:

```http
ehub-user-id: 123e4567-e89b-12d3-a456-************
ehub-user-full-name: John Smith
ehub-user-member-id: 987fcdeb-51a2-43d1-9f12-123456789abc
ehub-user-member-name: Acme Corporation
```

### Security Features

- **Multi-tenant Isolation**: Automatic tenant-based data filtering
- **Role-based Access Control**: Endpoint-level permission validation
- **Audit Logging**: Comprehensive audit trails for all operations
- **Request Validation**: Input validation and sanitisation
- **Rate Limiting**: Protection against abuse and DoS attacks

## Base URL and Versioning

### Production Environment
```
Base URL: https://api.ehub.com
Version: v1 (current)
```

### Development Environment
```
Base URL: https://dev-api.ehub.com
Version: v1
```

### API Versioning Strategy
- **URL Versioning**: Version specified in URL path (`/api/v1/`)
- **Header Versioning**: Optional version header (`API-Version: v1`)
- **Backward Compatibility**: Maintained for at least 12 months

## Common Request/Response Models

### Grid Parameters Model

Used for paginated data retrieval across multiple endpoints:

```json
{
  "selectedPage": 1,
  "itemsPerPage": 25,
  "searchQuery": "invoice",
  "searchQueries": [
    {
      "field": "documentType",
      "value": "Invoice",
      "operator": "equals"
    }
  ],
  "sortBy": "createdDate",
  "sortAsc": false,
  "filteredItems": [
    {
      "field": "status",
      "values": ["Processed", "Approved"]
    }
  ],
  "exporting": false
}
```

### Grid Result Model

Standard response format for paginated data:

```json
{
  "results": [
    { /* data items */ }
  ],
  "totalCount": 150,
  "hasNextPage": true,
  "hasPreviousPage": false
}
```

### Standard Error Response

```json
{
  "isSuccess": false,
  "error": "Validation failed: Customer ID is required",
  "errorCode": "VALIDATION_ERROR",
  "details": {
    "field": "customerId",
    "message": "Customer ID must be a valid GUID"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## Core API Endpoints

### Health Check Endpoints

#### System Health Check
```http
GET /api/health
Authorization: Not required
```

**Response:**
```json
{
  "status": "Healthy",
  "database": true,
  "azureStorage": true,
  "backgroundJobs": true,
  "timestamp": "2024-01-15T10:30:00Z"
}
```

#### Component Health Checks
```http
GET /api/health/database
GET /api/health/storage
GET /api/health/integrations
```

### Document Management API

#### Upload Document
```http
POST /api/Document/AddAndProcess
Authorization: Bearer <token>
Content-Type: application/json
```

**Request:**
```json
{
  "fileName": "invoice_001.pdf",
  "fileContent": "<base64-encoded-content>",
  "customerId": "123e4567-e89b-12d3-a456-************",
  "documentType": "Invoice",
  "metadata": {
    "source": "Email",
    "priority": "Normal"
  }
}
```

**Response:**
```json
{
  "isSuccess": true,
  "data": {
    "documentId": "456e7890-e89b-12d3-a456-************",
    "transactionId": "789abcde-e89b-12d3-a456-************",
    "status": "Processing",
    "estimatedCompletionTime": "2024-01-15T10:35:00Z"
  }
}
```

#### Get Document Status
```http
POST /api/Document/GetDocumentStatus
Authorization: Bearer <token>
Content-Type: application/json
```

**Request:**
```json
{
  "documentId": "456e7890-e89b-12d3-a456-************"
}
```

### Transaction Management API

#### Get Transaction List
```http
POST /api/members/{customerId}/transactions/GetTransactionList
Authorization: Bearer <token>
Content-Type: application/json
```

**Request:**
```json
{
  "selectedPage": 1,
  "itemsPerPage": 25,
  "searchQuery": "",
  "filteredItems": [
    {
      "field": "status",
      "values": ["Processed", "Approved"]
    },
    {
      "field": "documentType",
      "values": ["Invoice"]
    }
  ],
  "sortBy": "createdDate",
  "sortAsc": false
}
```

**Response:**
```json
{
  "isSuccess": true,
  "data": {
    "results": [
      {
        "transactionId": "123e4567-e89b-12d3-a456-************",
        "documentType": "Invoice",
        "supplierName": "ABC Supplies Ltd",
        "invoiceNumber": "INV-2024-001",
        "totalAmount": 1250.00,
        "currency": "GBP",
        "status": "Approved",
        "createdDate": "2024-01-15T09:30:00Z",
        "approvedDate": "2024-01-15T10:15:00Z"
      }
    ],
    "totalCount": 150
  }
}
```

#### Get Transaction Details
```http
POST /api/members/{customerId}/transactions/GetTransaction
Authorization: Bearer <token>
Content-Type: application/json
```

**Request:**
```json
{
  "transactionId": "123e4567-e89b-12d3-a456-************",
  "includeLineItems": true,
  "includeAuditTrail": true
}
```

#### Update Transaction
```http
POST /api/members/{customerId}/transactions/UpdateTransaction
Authorization: Bearer <token>
ehub-user-id: <user-guid>
ehub-user-full-name: <user-name>
ehub-user-member-id: <member-guid>
ehub-user-member-name: <member-name>
Content-Type: application/json
```

**Request:**
```json
{
  "transactionId": "123e4567-e89b-12d3-a456-************",
  "updates": [
    {
      "field": "supplierReference",
      "value": "SUP-REF-001"
    },
    {
      "field": "purchaseOrderNumber",
      "value": "PO-2024-001"
    }
  ],
  "comment": "Updated supplier reference and PO number"
}
```

### Customer Management API

#### List Customers
```http
POST /api/Customer/ListCustomers
Authorization: Bearer <token>
Content-Type: application/json
```

**Request:**
```json
{
  "selectedPage": 1,
  "itemsPerPage": 50,
  "searchQuery": "Acme",
  "sortBy": "name",
  "sortAsc": true
}
```

#### Create Customer
```http
POST /api/Customer/CreateCustomer
Authorization: Bearer <token>
ehub-user-id: <user-guid>
ehub-user-full-name: <user-name>
ehub-user-member-id: <member-guid>
ehub-user-member-name: <member-name>
Content-Type: application/json
```

**Request:**
```json
{
  "name": "New Customer Ltd",
  "registrationNumber": "12345678",
  "vatNumber": "GB123456789",
  "address": {
    "line1": "123 Business Street",
    "line2": "Business Park",
    "city": "London",
    "postcode": "SW1A 1AA",
    "country": "United Kingdom"
  },
  "contactDetails": {
    "primaryEmail": "<EMAIL>",
    "primaryPhone": "+44 20 1234 5678"
  },
  "settings": {
    "currency": "GBP",
    "timeZone": "Europe/London",
    "documentRetentionDays": 2555
  }
}
```

### Workflow Management API

#### List Workflows
```http
POST /api/Workflow/ListWorkflows
Authorization: Bearer <token>
Content-Type: application/json
```

**Request:**
```json
{
  "customerId": "123e4567-e89b-12d3-a456-************",
  "selectedPage": 1,
  "itemsPerPage": 25,
  "filteredItems": [
    {
      "field": "isActive",
      "values": ["true"]
    }
  ]
}
```

#### Create Workflow
```http
POST /api/Workflow/CreateWorkflow
Authorization: Bearer <token>
ehub-user-id: <user-guid>
ehub-user-full-name: <user-name>
ehub-user-member-id: <member-guid>
ehub-user-member-name: <member-name>
Content-Type: application/json
```

**Request:**
```json
{
  "name": "Invoice Approval Workflow",
  "description": "Standard invoice approval process",
  "documentTypes": ["Invoice"],
  "steps": [
    {
      "stepOrder": 1,
      "stepType": "Validation",
      "name": "Data Validation",
      "rules": [
        {
          "field": "totalAmount",
          "operator": "required"
        },
        {
          "field": "supplierName",
          "operator": "required"
        }
      ]
    },
    {
      "stepOrder": 2,
      "stepType": "Approval",
      "name": "Manager Approval",
      "approvers": [
        {
          "userId": "456e7890-e89b-12d3-a456-************",
          "role": "Manager"
        }
      ],
      "conditions": [
        {
          "field": "totalAmount",
          "operator": "greaterThan",
          "value": 1000
        }
      ]
    }
  ],
  "isActive": true
}
```

## Error Handling

### HTTP Status Codes

| Status Code | Description | Usage |
|-------------|-------------|-------|
| 200 | OK | Successful request |
| 201 | Created | Resource created successfully |
| 400 | Bad Request | Invalid request parameters |
| 401 | Unauthorised | Missing or invalid authentication |
| 403 | Forbidden | Insufficient permissions |
| 404 | Not Found | Resource not found |
| 409 | Conflict | Resource conflict (e.g., duplicate) |
| 422 | Unprocessable Entity | Validation errors |
| 429 | Too Many Requests | Rate limit exceeded |
| 500 | Internal Server Error | Server error |
| 503 | Service Unavailable | Service temporarily unavailable |

### Error Response Format

```json
{
  "isSuccess": false,
  "error": "Validation failed",
  "errorCode": "VALIDATION_ERROR",
  "details": {
    "field": "customerId",
    "message": "Customer ID must be a valid GUID",
    "value": "invalid-guid"
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "traceId": "abc123-def456-ghi789"
}
```

### Common Error Codes

| Error Code | Description | Resolution |
|------------|-------------|------------|
| `AUTHENTICATION_REQUIRED` | Missing authentication token | Include valid JWT token |
| `INVALID_TOKEN` | Invalid or expired JWT token | Refresh authentication token |
| `INSUFFICIENT_PERMISSIONS` | User lacks required permissions | Contact administrator |
| `VALIDATION_ERROR` | Request validation failed | Check request parameters |
| `RESOURCE_NOT_FOUND` | Requested resource not found | Verify resource ID |
| `DUPLICATE_RESOURCE` | Resource already exists | Use update operation instead |
| `RATE_LIMIT_EXCEEDED` | Too many requests | Implement request throttling |
| `SERVICE_UNAVAILABLE` | Service temporarily unavailable | Retry with exponential backoff |

## Rate Limiting

### Rate Limit Headers

```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: **********
X-RateLimit-Window: 3600
```

### Rate Limit Tiers

| Tier | Requests per Hour | Burst Limit |
|------|-------------------|-------------|
| Standard | 1,000 | 100 |
| Premium | 5,000 | 500 |
| Enterprise | 10,000 | 1,000 |

## Pagination

### Request Parameters

```json
{
  "selectedPage": 1,
  "itemsPerPage": 25
}
```

### Response Format

```json
{
  "results": [ /* data items */ ],
  "totalCount": 150,
  "currentPage": 1,
  "pageSize": 25,
  "totalPages": 6,
  "hasNextPage": true,
  "hasPreviousPage": false
}
```

### Pagination Best Practices

- **Default Page Size**: 25 items
- **Maximum Page Size**: 100 items
- **Large Datasets**: Use filtering and search to reduce result sets
- **Performance**: Consider using cursor-based pagination for large datasets

## Filtering and Searching

### Search Parameters

```json
{
  "searchQuery": "invoice",
  "searchQueries": [
    {
      "field": "documentType",
      "value": "Invoice",
      "operator": "equals"
    },
    {
      "field": "totalAmount",
      "value": "1000",
      "operator": "greaterThan"
    }
  ]
}
```

### Filter Parameters

```json
{
  "filteredItems": [
    {
      "field": "status",
      "values": ["Processed", "Approved"]
    },
    {
      "field": "createdDate",
      "values": ["2024-01-01", "2024-01-31"],
      "operator": "between"
    }
  ]
}
```

### Supported Operators

| Operator | Description | Example |
|----------|-------------|---------|
| `equals` | Exact match | `"status": "Approved"` |
| `contains` | Partial match | `"supplierName": "Acme"` |
| `startsWith` | Prefix match | `"invoiceNumber": "INV"` |
| `greaterThan` | Numeric comparison | `"totalAmount": 1000` |
| `lessThan` | Numeric comparison | `"totalAmount": 5000` |
| `between` | Range comparison | `"createdDate": ["2024-01-01", "2024-01-31"]` |
| `in` | Multiple values | `"status": ["Approved", "Processed"]` |

## Sorting

### Sort Parameters

```json
{
  "sortBy": "createdDate",
  "sortAsc": false
}
```

### Multiple Sort Fields

```json
{
  "sortFields": [
    {
      "field": "status",
      "ascending": true
    },
    {
      "field": "createdDate",
      "ascending": false
    }
  ]
}
```

## Best Practices

### Request Optimisation

1. **Use Specific Fields**: Request only required data fields
2. **Implement Caching**: Cache frequently accessed data
3. **Batch Operations**: Group multiple operations when possible
4. **Pagination**: Always use pagination for large datasets
5. **Filtering**: Apply filters to reduce data transfer

### Error Handling

1. **Retry Logic**: Implement exponential backoff for retries
2. **Timeout Handling**: Set appropriate request timeouts
3. **Error Logging**: Log errors for debugging and monitoring
4. **Graceful Degradation**: Handle service unavailability gracefully

### Security

1. **Token Management**: Securely store and refresh JWT tokens
2. **HTTPS Only**: Always use HTTPS for API communications
3. **Input Validation**: Validate all input data client-side
4. **Sensitive Data**: Never log sensitive information

### Performance

1. **Connection Pooling**: Reuse HTTP connections
2. **Compression**: Enable gzip compression
3. **Monitoring**: Monitor API response times and error rates
4. **Load Balancing**: Distribute requests across multiple instances

## Advanced API Operations

### Bulk Operations

#### Bulk Document Upload
```http
POST /api/Document/BulkUpload
Authorization: Bearer <token>
Content-Type: application/json
```

**Request:**
```json
{
  "documents": [
    {
      "fileName": "invoice_001.pdf",
      "fileContent": "<base64-encoded-content>",
      "documentType": "Invoice",
      "metadata": {
        "source": "Email",
        "priority": "High"
      }
    },
    {
      "fileName": "receipt_002.pdf",
      "fileContent": "<base64-encoded-content>",
      "documentType": "Receipt",
      "metadata": {
        "source": "FTP",
        "priority": "Normal"
      }
    }
  ],
  "customerId": "123e4567-e89b-12d3-a456-************",
  "batchId": "batch-2024-001"
}
```

**Response:**
```json
{
  "isSuccess": true,
  "data": {
    "batchId": "batch-2024-001",
    "totalDocuments": 2,
    "successfulUploads": 2,
    "failedUploads": 0,
    "results": [
      {
        "fileName": "invoice_001.pdf",
        "documentId": "456e7890-e89b-12d3-a456-************",
        "status": "Processing"
      },
      {
        "fileName": "receipt_002.pdf",
        "documentId": "789abcde-e89b-12d3-a456-************",
        "status": "Processing"
      }
    ]
  }
}
```

### Reporting and Analytics API

#### Get Dashboard Metrics
```http
POST /api/Reporting/GetDashboardMetrics
Authorization: Bearer <token>
Content-Type: application/json
```

**Request:**
```json
{
  "customerId": "123e4567-e89b-12d3-a456-************",
  "dateRange": {
    "startDate": "2024-01-01T00:00:00Z",
    "endDate": "2024-01-31T23:59:59Z"
  },
  "metrics": [
    "documentsProcessed",
    "averageProcessingTime",
    "errorRate",
    "approvalRate"
  ]
}
```

**Response:**
```json
{
  "isSuccess": true,
  "data": {
    "period": {
      "startDate": "2024-01-01T00:00:00Z",
      "endDate": "2024-01-31T23:59:59Z"
    },
    "metrics": {
      "documentsProcessed": 1250,
      "averageProcessingTime": "00:02:45",
      "errorRate": 0.02,
      "approvalRate": 0.95
    },
    "trends": {
      "documentsProcessed": {
        "change": 15.5,
        "direction": "up"
      },
      "averageProcessingTime": {
        "change": -8.2,
        "direction": "down"
      }
    }
  }
}
```

### Integration Management API

#### List Integration Endpoints
```http
POST /api/Integration/ListEndpoints
Authorization: Bearer <token>
Content-Type: application/json
```

**Request:**
```json
{
  "customerId": "123e4567-e89b-12d3-a456-************",
  "integrationTypes": ["AS2", "FTP", "Email", "API"]
}
```

**Response:**
```json
{
  "isSuccess": true,
  "data": [
    {
      "endpointId": "endpoint-001",
      "name": "Primary AS2 Connection",
      "type": "AS2",
      "status": "Active",
      "lastActivity": "2024-01-15T10:25:00Z",
      "configuration": {
        "partnerName": "Trading Partner A",
        "certificateExpiry": "2024-12-31T23:59:59Z"
      }
    },
    {
      "endpointId": "endpoint-002",
      "name": "FTP Document Pickup",
      "type": "FTP",
      "status": "Active",
      "lastActivity": "2024-01-15T10:20:00Z",
      "configuration": {
        "serverAddress": "ftp.partner.com",
        "pollingInterval": "00:15:00"
      }
    }
  ]
}
```

### Audit and Compliance API

#### Get Audit Trail
```http
POST /api/Audit/GetAuditTrail
Authorization: Bearer <token>
Content-Type: application/json
```

**Request:**
```json
{
  "entityType": "Transaction",
  "entityId": "123e4567-e89b-12d3-a456-************",
  "dateRange": {
    "startDate": "2024-01-01T00:00:00Z",
    "endDate": "2024-01-15T23:59:59Z"
  },
  "includeSystemEvents": true
}
```

**Response:**
```json
{
  "isSuccess": true,
  "data": {
    "entityType": "Transaction",
    "entityId": "123e4567-e89b-12d3-a456-************",
    "auditEvents": [
      {
        "eventId": "audit-001",
        "timestamp": "2024-01-15T09:30:00Z",
        "eventType": "Created",
        "userId": "user-123",
        "userName": "John Smith",
        "details": {
          "action": "Transaction created from document upload",
          "documentId": "doc-456"
        }
      },
      {
        "eventId": "audit-002",
        "timestamp": "2024-01-15T10:15:00Z",
        "eventType": "Updated",
        "userId": "user-456",
        "userName": "Jane Doe",
        "details": {
          "action": "Transaction approved",
          "previousStatus": "Pending",
          "newStatus": "Approved"
        }
      }
    ],
    "totalEvents": 2
  }
}
```

## Webhook Integration

### Webhook Configuration

eHub supports webhook notifications for real-time event updates:

```http
POST /api/Webhooks/Configure
Authorization: Bearer <token>
Content-Type: application/json
```

**Request:**
```json
{
  "customerId": "123e4567-e89b-12d3-a456-************",
  "webhookUrl": "https://your-system.com/webhooks/ehub",
  "events": [
    "document.processed",
    "transaction.approved",
    "transaction.rejected",
    "workflow.completed"
  ],
  "secretKey": "your-webhook-secret",
  "isActive": true
}
```

### Webhook Payload Format

```json
{
  "eventId": "event-123",
  "eventType": "document.processed",
  "timestamp": "2024-01-15T10:30:00Z",
  "customerId": "123e4567-e89b-12d3-a456-************",
  "data": {
    "documentId": "456e7890-e89b-12d3-a456-************",
    "transactionId": "789abcde-e89b-12d3-a456-************",
    "status": "Processed",
    "processingTime": "00:02:30"
  },
  "signature": "sha256=abc123def456..."
}
```

## SDK and Code Examples

### C# SDK Example

```csharp
using eHub.SDK;

// Initialize client
var client = new EHubApiClient("https://api.ehub.com", "your-jwt-token");

// Upload document
var uploadRequest = new DocumentUploadRequest
{
    FileName = "invoice.pdf",
    FileContent = Convert.ToBase64String(fileBytes),
    CustomerId = Guid.Parse("123e4567-e89b-12d3-a456-************"),
    DocumentType = "Invoice"
};

var result = await client.Documents.UploadAsync(uploadRequest);
if (result.IsSuccess)
{
    Console.WriteLine($"Document uploaded: {result.Data.DocumentId}");
}
```

### JavaScript SDK Example

```javascript
import { EHubApiClient } from '@ehub/api-client';

// Initialize client
const client = new EHubApiClient({
  baseUrl: 'https://api.ehub.com',
  token: 'your-jwt-token'
});

// Get transaction list
const transactionList = await client.transactions.getList({
  customerId: '123e4567-e89b-12d3-a456-************',
  selectedPage: 1,
  itemsPerPage: 25,
  filteredItems: [
    {
      field: 'status',
      values: ['Processed', 'Approved']
    }
  ]
});

console.log(`Found ${transactionList.data.totalCount} transactions`);
```

### Python SDK Example

```python
from ehub_sdk import EHubApiClient

# Initialize client
client = EHubApiClient(
    base_url='https://api.ehub.com',
    token='your-jwt-token'
)

# Create customer
customer_data = {
    'name': 'New Customer Ltd',
    'registrationNumber': '12345678',
    'vatNumber': 'GB123456789',
    'address': {
        'line1': '123 Business Street',
        'city': 'London',
        'postcode': 'SW1A 1AA',
        'country': 'United Kingdom'
    }
}

result = client.customers.create(customer_data)
if result.is_success:
    print(f"Customer created: {result.data.customer_id}")
```

## Testing and Development

### Swagger/OpenAPI Documentation

Interactive API documentation is available at:
- **Production**: `https://api.ehub.com/swagger`
- **Development**: `https://dev-api.ehub.com/swagger`

### Test Endpoints

All controllers include test endpoints for connectivity verification:

```http
GET /api/{controller}/Test
Authorization: Not required
```

### Postman Collection

A comprehensive Postman collection is available for API testing:
- Download: `https://api.ehub.com/postman/eHub-API-Collection.json`
- Environment: `https://api.ehub.com/postman/eHub-Environment.json`

### Sample Data

Test data sets are available for development and testing:

```http
GET /api/Parameter/SampleGuidParameters
GET /api/Parameter/SampleStringParameters
Authorization: Bearer <token>
```

## Related Documents

- **[AuthenticationGuide.md](./AuthenticationGuide.md)** - Detailed authentication and authorisation flows
- **[IntegrationGuide.md](./IntegrationGuide.md)** - External system integration patterns and examples
- **[ErrorCodesAndHandling.md](./ErrorCodesAndHandling.md)** - Comprehensive error handling guide
- **[APIArchitecture.md](./APIArchitecture.md)** - Internal API architecture and design patterns
- **[SecurityArchitecture.md](./SecurityArchitecture.md)** - Security implementation details
- **[SystemOverview.md](./SystemOverview.md)** - High-level system architecture

---

*This API reference provides comprehensive documentation for integrating with the eHub platform. For additional support or questions, please contact the eHub development team or refer to the related documentation.*
