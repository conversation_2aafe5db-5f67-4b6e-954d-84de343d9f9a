# Error Codes and Handling Guide

## Purpose & Scope

This document provides comprehensive guidance for understanding, handling, and troubleshooting errors in the eHub electronic trading platform. It covers HTTP status codes, error response formats, common error scenarios, retry strategies, and best practices for robust error handling in client applications integrating with eHub APIs.

## Prerequisites

- **HTTP Protocol Knowledge**: Understanding of HTTP status codes and error handling
- **API Integration Experience**: Familiarity with REST API error patterns
- **JSON Processing**: Ability to parse and handle JSON error responses
- **Logging and Monitoring**: Understanding of application logging and error tracking
- **Business Process Context**: Knowledge of eHub document processing workflows

## Core Concepts

### Error Handling Philosophy

eHub implements a comprehensive error handling strategy designed to provide clear, actionable error information while maintaining system security and stability:

```mermaid
graph TB
    subgraph "Error Classification"
        CLIENT[Client Errors<br/>4xx Status Codes]
        SERVER[Server Errors<br/>5xx Status Codes]
        BUSINESS[Business Logic Errors<br/>Custom Error Codes]
        VALIDATION[Validation Errors<br/>Field-specific Messages]
    end
    
    subgraph "Error Response Structure"
        STANDARD[Standard HTTP Response]
        DETAILED[Detailed Error Information]
        CONTEXT[Contextual Error Data]
        TRACE[Trace Information]
    end
    
    subgraph "Error Handling Strategies"
        RETRY[Retry Logic]
        FALLBACK[Fallback Mechanisms]
        CIRCUIT[Circuit Breaker]
        LOGGING[Error Logging]
    end
    
    CLIENT --> STANDARD
    SERVER --> STANDARD
    BUSINESS --> DETAILED
    VALIDATION --> CONTEXT
    
    STANDARD --> RETRY
    DETAILED --> FALLBACK
    CONTEXT --> CIRCUIT
    TRACE --> LOGGING
```

### Error Response Structure

All eHub API errors follow a consistent response format:

```json
{
  "isSuccess": false,
  "error": "Primary error message",
  "errorCode": "SPECIFIC_ERROR_CODE",
  "details": {
    "field": "fieldName",
    "message": "Field-specific error message",
    "value": "invalid-value",
    "additionalInfo": {}
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "traceId": "abc123-def456-ghi789",
  "correlationId": "request-correlation-id"
}
```

## HTTP Status Codes

### 2xx Success Codes

| Code | Status | Description | Usage |
|------|--------|-------------|-------|
| 200 | OK | Request successful | Standard successful response |
| 201 | Created | Resource created | Document upload, customer creation |
| 202 | Accepted | Request accepted for processing | Async document processing |
| 204 | No Content | Successful with no response body | Delete operations |

### 4xx Client Error Codes

#### 400 Bad Request
**Description**: The request is malformed or contains invalid parameters.

**Common Causes**:
- Invalid JSON format
- Missing required fields
- Invalid data types
- Malformed request structure

**Example Response**:
```json
{
  "isSuccess": false,
  "error": "Invalid request format",
  "errorCode": "BAD_REQUEST",
  "details": {
    "field": "customerId",
    "message": "Customer ID must be a valid GUID",
    "value": "invalid-guid-format"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

**Resolution**:
- Validate request format before sending
- Ensure all required fields are present
- Check data type compatibility
- Review API documentation for correct format

#### 401 Unauthorised
**Description**: Authentication is required or has failed.

**Common Causes**:
- Missing authentication token
- Expired JWT token
- Invalid token format
- Token signature verification failure

**Example Response**:
```json
{
  "isSuccess": false,
  "error": "Authentication required",
  "errorCode": "AUTHENTICATION_REQUIRED",
  "details": {
    "message": "Valid JWT token required in Authorization header",
    "tokenStatus": "missing"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

**Resolution**:
- Include valid JWT token in Authorization header
- Refresh expired tokens
- Verify token format and signature
- Check authentication endpoint configuration

#### 403 Forbidden
**Description**: The authenticated user lacks sufficient permissions.

**Common Causes**:
- Insufficient role permissions
- Tenant access restrictions
- Resource-specific permissions
- API endpoint restrictions

**Example Response**:
```json
{
  "isSuccess": false,
  "error": "Insufficient permissions",
  "errorCode": "INSUFFICIENT_PERMISSIONS",
  "details": {
    "requiredPermission": "transaction.update",
    "userRole": "viewer",
    "resource": "transaction-123"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

**Resolution**:
- Contact administrator for permission elevation
- Verify user role assignments
- Check tenant-specific permissions
- Review API access policies

#### 404 Not Found
**Description**: The requested resource does not exist.

**Common Causes**:
- Invalid resource ID
- Resource deleted or archived
- Incorrect API endpoint
- Tenant isolation restrictions

**Example Response**:
```json
{
  "isSuccess": false,
  "error": "Resource not found",
  "errorCode": "RESOURCE_NOT_FOUND",
  "details": {
    "resourceType": "Transaction",
    "resourceId": "123e4567-e89b-12d3-a456-426614174000",
    "customerId": "987fcdeb-51a2-43d1-9f12-123456789abc"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

**Resolution**:
- Verify resource ID accuracy
- Check resource existence in target tenant
- Confirm API endpoint URL
- Review resource lifecycle status

#### 409 Conflict
**Description**: The request conflicts with the current state of the resource.

**Common Causes**:
- Duplicate resource creation
- Concurrent modification conflicts
- Business rule violations
- State transition restrictions

**Example Response**:
```json
{
  "isSuccess": false,
  "error": "Resource conflict",
  "errorCode": "RESOURCE_CONFLICT",
  "details": {
    "conflictType": "duplicate",
    "existingResource": "customer-456",
    "conflictField": "registrationNumber",
    "conflictValue": "12345678"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

**Resolution**:
- Check for existing resources before creation
- Implement optimistic concurrency control
- Review business rule constraints
- Use update operations for existing resources

#### 422 Unprocessable Entity
**Description**: The request is well-formed but contains semantic errors.

**Common Causes**:
- Business validation failures
- Data integrity violations
- Workflow state restrictions
- Cross-field validation errors

**Example Response**:
```json
{
  "isSuccess": false,
  "error": "Validation failed",
  "errorCode": "VALIDATION_ERROR",
  "details": {
    "validationErrors": [
      {
        "field": "totalAmount",
        "message": "Total amount must be greater than zero",
        "value": "-100.00"
      },
      {
        "field": "dueDate",
        "message": "Due date must be in the future",
        "value": "2023-12-01"
      }
    ]
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

**Resolution**:
- Validate data before submission
- Check business rule compliance
- Review field-specific requirements
- Implement client-side validation

#### 429 Too Many Requests
**Description**: Rate limit exceeded for the current user or API key.

**Common Causes**:
- Exceeding API rate limits
- Burst request patterns
- Inefficient polling strategies
- Missing request throttling

**Example Response**:
```json
{
  "isSuccess": false,
  "error": "Rate limit exceeded",
  "errorCode": "RATE_LIMIT_EXCEEDED",
  "details": {
    "limit": 1000,
    "remaining": 0,
    "resetTime": "2024-01-15T11:00:00Z",
    "retryAfter": 1800
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

**Resolution**:
- Implement request throttling
- Use exponential backoff for retries
- Consider webhook alternatives to polling
- Review rate limit tiers and upgrade if needed

### 5xx Server Error Codes

#### 500 Internal Server Error
**Description**: An unexpected error occurred on the server.

**Common Causes**:
- Unhandled exceptions
- Database connectivity issues
- External service failures
- Configuration problems

**Example Response**:
```json
{
  "isSuccess": false,
  "error": "Internal server error",
  "errorCode": "INTERNAL_SERVER_ERROR",
  "details": {
    "message": "An unexpected error occurred",
    "traceId": "abc123-def456-ghi789"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

**Resolution**:
- Retry the request after a delay
- Contact support with trace ID
- Check system status page
- Implement circuit breaker patterns

#### 502 Bad Gateway
**Description**: Invalid response from upstream server.

**Common Causes**:
- External service unavailability
- Network connectivity issues
- Proxy configuration problems
- Upstream service errors

**Resolution**:
- Retry with exponential backoff
- Check external service status
- Verify network connectivity
- Contact support if persistent

#### 503 Service Unavailable
**Description**: Service temporarily unavailable.

**Common Causes**:
- Planned maintenance
- System overload
- Database maintenance
- Deployment in progress

**Example Response**:
```json
{
  "isSuccess": false,
  "error": "Service temporarily unavailable",
  "errorCode": "SERVICE_UNAVAILABLE",
  "details": {
    "reason": "scheduled_maintenance",
    "estimatedRecovery": "2024-01-15T12:00:00Z",
    "retryAfter": 3600
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

**Resolution**:
- Wait for the specified retry period
- Check maintenance schedules
- Implement graceful degradation
- Monitor service status updates

#### 504 Gateway Timeout
**Description**: Timeout waiting for upstream response.

**Common Causes**:
- Long-running operations
- Database query timeouts
- External service delays
- Network latency issues

**Resolution**:
- Increase client timeout settings
- Retry with exponential backoff
- Consider async processing patterns
- Optimise request complexity

## Business Logic Error Codes

### Document Processing Errors

#### DOCUMENT_PROCESSING_FAILED
**Description**: Document processing encountered an error.

**Common Scenarios**:
- PDF parsing failures
- Corrupted file uploads
- Unsupported file formats
- OCR processing errors

**Example Response**:
```json
{
  "isSuccess": false,
  "error": "Document processing failed",
  "errorCode": "DOCUMENT_PROCESSING_FAILED",
  "details": {
    "documentId": "doc-123",
    "fileName": "invoice.pdf",
    "processingStage": "pdf_extraction",
    "errorDetails": "Unable to extract text from PDF",
    "supportedFormats": [".pdf", ".xml", ".csv"]
  }
}
```

**Resolution**:
- Verify file format compatibility
- Check file integrity
- Ensure file size within limits
- Contact support for processing issues

#### DOCUMENT_VALIDATION_FAILED
**Description**: Document content validation failed.

**Common Scenarios**:
- Missing required fields
- Invalid data formats
- Business rule violations
- Schema validation errors

**Example Response**:
```json
{
  "isSuccess": false,
  "error": "Document validation failed",
  "errorCode": "DOCUMENT_VALIDATION_FAILED",
  "details": {
    "documentId": "doc-123",
    "validationErrors": [
      {
        "field": "invoiceNumber",
        "message": "Invoice number is required",
        "location": "line 1"
      },
      {
        "field": "totalAmount",
        "message": "Total amount format invalid",
        "value": "invalid-amount",
        "location": "line 15"
      }
    ]
  }
}
```

### Transaction Processing Errors

#### TRANSACTION_STATE_INVALID
**Description**: Transaction is in an invalid state for the requested operation.

**Common Scenarios**:
- Attempting to modify approved transactions
- Invalid workflow transitions
- Concurrent modification conflicts
- Status inconsistencies

**Example Response**:
```json
{
  "isSuccess": false,
  "error": "Invalid transaction state",
  "errorCode": "TRANSACTION_STATE_INVALID",
  "details": {
    "transactionId": "txn-123",
    "currentStatus": "Approved",
    "requestedOperation": "update",
    "allowedOperations": ["view", "export"],
    "statusHistory": [
      {
        "status": "Processing",
        "timestamp": "2024-01-15T09:00:00Z"
      },
      {
        "status": "Approved",
        "timestamp": "2024-01-15T10:00:00Z"
      }
    ]
  }
}
```

#### WORKFLOW_EXECUTION_FAILED
**Description**: Workflow execution encountered an error.

**Common Scenarios**:
- Missing workflow configuration
- Approval timeout
- Rule evaluation failures
- External system integration errors

**Example Response**:
```json
{
  "isSuccess": false,
  "error": "Workflow execution failed",
  "errorCode": "WORKFLOW_EXECUTION_FAILED",
  "details": {
    "workflowId": "wf-123",
    "workflowName": "Invoice Approval",
    "currentStep": "manager_approval",
    "failureReason": "approver_not_available",
    "retryable": true,
    "nextRetry": "2024-01-15T11:00:00Z"
  }
}
```

### Integration Errors

#### EXTERNAL_SYSTEM_ERROR
**Description**: Error communicating with external system.

**Common Scenarios**:
- ERP system unavailability
- Authentication failures
- Data format mismatches
- Network connectivity issues

**Example Response**:
```json
{
  "isSuccess": false,
  "error": "External system error",
  "errorCode": "EXTERNAL_SYSTEM_ERROR",
  "details": {
    "systemName": "SAP_ERP",
    "operation": "create_invoice",
    "errorMessage": "Connection timeout",
    "systemStatus": "unavailable",
    "retryable": true,
    "lastSuccessful": "2024-01-15T09:30:00Z"
  }
}
```

#### AS2_TRANSMISSION_FAILED
**Description**: AS2 message transmission failed.

**Common Scenarios**:
- Certificate validation failures
- Partner system unavailability
- Message encryption errors
- MDN receipt failures

**Example Response**:
```json
{
  "isSuccess": false,
  "error": "AS2 transmission failed",
  "errorCode": "AS2_TRANSMISSION_FAILED",
  "details": {
    "partnerId": "PARTNER_001",
    "messageId": "msg-123",
    "failureStage": "encryption",
    "certificateStatus": "expired",
    "certificateExpiry": "2024-01-01T00:00:00Z",
    "retryScheduled": "2024-01-15T11:00:00Z"
  }
}
```

## Error Handling Best Practices

### Client-Side Error Handling

#### Retry Strategy Implementation
```javascript
class APIClient {
    async makeRequestWithRetry(url, options, maxRetries = 3) {
        let lastError;
        
        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                const response = await fetch(url, options);
                
                if (response.ok) {
                    return await response.json();
                }
                
                const errorData = await response.json();
                
                // Don't retry client errors (4xx) except 429
                if (response.status >= 400 && response.status < 500 && response.status !== 429) {
                    throw new APIError(errorData, response.status);
                }
                
                // Calculate delay for retry
                const delay = this.calculateRetryDelay(attempt, errorData);
                await this.sleep(delay);
                
            } catch (error) {
                lastError = error;
                
                if (attempt === maxRetries) {
                    throw lastError;
                }
                
                // Don't retry non-retryable errors
                if (!this.isRetryableError(error)) {
                    throw error;
                }
            }
        }
        
        throw lastError;
    }
    
    calculateRetryDelay(attempt, errorData) {
        // Use server-provided retry-after if available
        if (errorData.details?.retryAfter) {
            return errorData.details.retryAfter * 1000;
        }
        
        // Exponential backoff with jitter
        const baseDelay = 1000; // 1 second
        const maxDelay = 30000; // 30 seconds
        const delay = Math.min(baseDelay * Math.pow(2, attempt), maxDelay);
        const jitter = Math.random() * 0.1 * delay;
        
        return delay + jitter;
    }
    
    isRetryableError(error) {
        if (error instanceof APIError) {
            return error.status >= 500 || error.status === 429;
        }
        
        // Network errors are generally retryable
        return error instanceof TypeError || error.name === 'NetworkError';
    }
    
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
```

#### Error Classification and Handling
```javascript
class APIError extends Error {
    constructor(errorData, status) {
        super(errorData.error);
        this.name = 'APIError';
        this.status = status;
        this.errorCode = errorData.errorCode;
        this.details = errorData.details;
        this.timestamp = errorData.timestamp;
        this.traceId = errorData.traceId;
    }
    
    isClientError() {
        return this.status >= 400 && this.status < 500;
    }
    
    isServerError() {
        return this.status >= 500;
    }
    
    isRetryable() {
        return this.isServerError() || this.status === 429;
    }
    
    getRetryDelay() {
        return this.details?.retryAfter || null;
    }
}

// Usage example
try {
    const result = await apiClient.makeRequestWithRetry('/api/documents/upload', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(documentData)
    });
    
    console.log('Document uploaded successfully:', result);
    
} catch (error) {
    if (error instanceof APIError) {
        switch (error.errorCode) {
            case 'AUTHENTICATION_REQUIRED':
                // Refresh token and retry
                await refreshAuthToken();
                break;
                
            case 'VALIDATION_ERROR':
                // Handle validation errors
                displayValidationErrors(error.details.validationErrors);
                break;
                
            case 'RATE_LIMIT_EXCEEDED':
                // Wait and retry
                const retryDelay = error.getRetryDelay() || 60000;
                setTimeout(() => retryUpload(), retryDelay);
                break;
                
            default:
                // Log error and show user-friendly message
                console.error('API Error:', error);
                showErrorMessage('An error occurred. Please try again.');
        }
    } else {
        // Handle network or other errors
        console.error('Network Error:', error);
        showErrorMessage('Network error. Please check your connection.');
    }
}
```

### Circuit Breaker Pattern
```javascript
class CircuitBreaker {
    constructor(threshold = 5, timeout = 60000, monitor = 30000) {
        this.threshold = threshold;
        this.timeout = timeout;
        this.monitor = monitor;
        this.failureCount = 0;
        this.lastFailureTime = null;
        this.state = 'CLOSED'; // CLOSED, OPEN, HALF_OPEN
    }
    
    async execute(operation) {
        if (this.state === 'OPEN') {
            if (Date.now() - this.lastFailureTime >= this.timeout) {
                this.state = 'HALF_OPEN';
            } else {
                throw new Error('Circuit breaker is OPEN');
            }
        }
        
        try {
            const result = await operation();
            this.onSuccess();
            return result;
        } catch (error) {
            this.onFailure();
            throw error;
        }
    }
    
    onSuccess() {
        this.failureCount = 0;
        this.state = 'CLOSED';
    }
    
    onFailure() {
        this.failureCount++;
        this.lastFailureTime = Date.now();
        
        if (this.failureCount >= this.threshold) {
            this.state = 'OPEN';
        }
    }
}
```

### Logging and Monitoring

#### Structured Error Logging
```javascript
class ErrorLogger {
    static logError(error, context = {}) {
        const logEntry = {
            timestamp: new Date().toISOString(),
            level: 'ERROR',
            message: error.message,
            errorCode: error.errorCode,
            status: error.status,
            traceId: error.traceId,
            context: {
                url: context.url,
                method: context.method,
                userId: context.userId,
                customerId: context.customerId,
                ...context
            },
            stack: error.stack
        };
        
        // Send to logging service
        this.sendToLoggingService(logEntry);
        
        // Console log for development
        if (process.env.NODE_ENV === 'development') {
            console.error('API Error:', logEntry);
        }
    }
    
    static async sendToLoggingService(logEntry) {
        try {
            await fetch('/api/logging/errors', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(logEntry)
            });
        } catch (loggingError) {
            console.error('Failed to send error to logging service:', loggingError);
        }
    }
}
```

## Troubleshooting Guide

### Common Error Scenarios

#### Authentication Issues
**Symptoms**: 401 Unauthorised responses
**Diagnosis Steps**:
1. Verify JWT token format and expiry
2. Check token claims and permissions
3. Validate authentication endpoint configuration
4. Review token refresh logic

**Resolution**:
```javascript
// Token validation example
function validateToken(token) {
    try {
        const decoded = jwt.decode(token);
        
        if (!decoded) {
            throw new Error('Invalid token format');
        }
        
        if (decoded.exp < Date.now() / 1000) {
            throw new Error('Token expired');
        }
        
        return true;
    } catch (error) {
        console.error('Token validation failed:', error);
        return false;
    }
}
```

#### Rate Limiting Issues
**Symptoms**: 429 Too Many Requests responses
**Diagnosis Steps**:
1. Check current rate limit usage
2. Review request patterns and frequency
3. Identify inefficient polling or batch operations
4. Monitor rate limit headers

**Resolution**:
```javascript
// Rate limit aware client
class RateLimitAwareClient {
    constructor() {
        this.requestQueue = [];
        this.isProcessing = false;
    }
    
    async makeRequest(url, options) {
        return new Promise((resolve, reject) => {
            this.requestQueue.push({ url, options, resolve, reject });
            this.processQueue();
        });
    }
    
    async processQueue() {
        if (this.isProcessing || this.requestQueue.length === 0) {
            return;
        }
        
        this.isProcessing = true;
        
        while (this.requestQueue.length > 0) {
            const { url, options, resolve, reject } = this.requestQueue.shift();
            
            try {
                const response = await fetch(url, options);
                
                if (response.status === 429) {
                    const retryAfter = response.headers.get('Retry-After') || 60;
                    await this.sleep(retryAfter * 1000);
                    
                    // Re-queue the request
                    this.requestQueue.unshift({ url, options, resolve, reject });
                    continue;
                }
                
                resolve(await response.json());
                
                // Respect rate limits
                await this.sleep(100); // Small delay between requests
                
            } catch (error) {
                reject(error);
            }
        }
        
        this.isProcessing = false;
    }
}
```

#### Document Processing Failures
**Symptoms**: DOCUMENT_PROCESSING_FAILED errors
**Diagnosis Steps**:
1. Verify file format and size
2. Check document content and structure
3. Review processing logs and trace IDs
4. Test with known good documents

**Resolution**:
- Ensure documents meet format requirements
- Validate file integrity before upload
- Implement pre-processing validation
- Contact support with trace IDs for investigation

## Related Documents

- **[APIReference.md](./APIReference.md)** - Complete REST API documentation
- **[AuthenticationGuide.md](./AuthenticationGuide.md)** - Authentication and authorisation flows
- **[IntegrationGuide.md](./IntegrationGuide.md)** - External system integration patterns
- **[TroubleshootingGuide.md](./TroubleshootingGuide.md)** - System troubleshooting procedures
- **[MaintenanceAndOperations.md](./MaintenanceAndOperations.md)** - Operational monitoring and maintenance
- **[SystemOverview.md](./SystemOverview.md)** - High-level system architecture

---

*This error handling guide provides comprehensive guidance for understanding and managing errors in the eHub platform. Proper error handling implementation ensures robust, reliable integrations and improved user experience.*
