# eHub Auditing and Logging Architecture

## Purpose & Scope

This document provides comprehensive documentation of the eHub auditing and logging architecture, including audit trails, change tracking mechanisms, compliance logging patterns, and logging infrastructure. It serves as the definitive guide for understanding and implementing audit and logging requirements in the eHub system.

## Prerequisites

- Understanding of regulatory compliance requirements (SOX, GDPR)
- Knowledge of audit trail design patterns
- Familiarity with .NET logging frameworks
- Basic understanding of eHub business processes and data models

## Core Concepts

### Auditing Architecture Philosophy

The eHub auditing and logging system is built on several key principles:

1. **Audit Everything**: Comprehensive tracking of all business-critical changes
2. **Immutable Records**: Audit logs cannot be modified or deleted
3. **Regulatory Compliance**: Built-in support for SOX, GDPR, and industry standards
4. **Performance Optimised**: Asynchronous logging with minimal impact on business operations
5. **Contextual Information**: Rich audit context including user, timestamp, and business context

### Auditing and Logging Overview

```
eHub Auditing and Logging Architecture
├── Business Process Auditing
│   ├── Entity Change Tracking - Field-level change auditing
│   ├── Workflow State Changes - Process state transitions
│   ├── Financial Reconciliation - Matching and approval auditing
│   └── Document Lifecycle - Document processing audit trails
├── System Auditing
│   ├── Authentication Events - Login/logout tracking
│   ├── Authorisation Events - Permission checks and access control
│   ├── System Events - Application lifecycle and errors
│   └── Integration Events - External system interactions
├── Compliance Logging
│   ├── Data Access Logs - GDPR compliance tracking
│   ├── Data Modification Logs - SOX compliance requirements
│   ├── Retention Policies - Automated log retention management
│   └── Export Capabilities - Audit report generation
└── Technical Logging
    ├── Application Logs - Debug, info, warning, error logging
    ├── Performance Logs - System performance monitoring
    ├── Integration Logs - External system communication
    └── Background Job Logs - Scheduled task execution
```

## Comprehensive Audit Architecture

### Business Process Audit System

```csharp
public enum AuditType
{
    // CRUD Operations
    Create, Read, Update, Delete,
    
    // Business Process States
    Issue, Pay, Send, Deliver, Acknowledge,
    
    // Workflow Actions
    Revalidate, Reprocess, PushThrough,
    OnHold, ReleaseFromOnHold,
    Accept, Reject, Approve,
    
    // Financial Reconciliation
    StatementMatchReconciled,
    StatementMatchDisregarded,
    
    // System Events
    Purge, ArchiveUpdate, Clone, Reload,
    
    // Error Handling
    Failed, Retry, ManualIntervention
}
```

### Audit Data Model

```mermaid
erDiagram
    AuditLog {
        int Id PK
        DateTime Timestamp
        AuditType AuditType
        AuditDataType DataType
        string OldValue
        string NewValue
        Guid UserId
        string UserFullname
        Guid MemberId
        string MemberName
        string PrimaryObjectId
        string PrimaryObjectType
        string SecondaryObjectId
        string SecondaryObjectType
        string PrimaryFieldName
        string PrimaryFieldDisplayName
        string Comments
        string AdditionalInfo
    }
    
    Actor {
        Guid UserId PK
        string UserFullName
        Guid MemberId
        string MemberName
    }
    
    Entity {
        string Id PK
        string Type
    }
    
    Field {
        string FieldName PK
        string FieldDisplayName
    }
    
    Change {
        string OldValue
        string NewValue
    }
    
    AuditLog ||--|| Actor : "performed by"
    AuditLog ||--|| Entity : "primary entity"
    AuditLog ||--o| Entity : "secondary entity"
    AuditLog ||--o| Field : "field changed"
    AuditLog ||--o| Change : "value change"
```

**Core Audit Log Entity**:
```csharp
[Table("AuditLog", Schema = "Audit")]
public class AuditLog
{
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }

    [Required]
    public DateTime Timestamp { get; set; }

    [Required]
    public AuditType AuditType { get; set; }

    [Required]
    public AuditDataType DataType { get; set; }

    public string OldValue { get; set; }
    public string NewValue { get; set; }

    // User context
    [Required]
    public Guid UserId { get; set; }
    [Required]
    public string UserFullname { get; set; }
    [Required]
    public Guid MemberId { get; set; }
    [Required]
    public string MemberName { get; set; }

    // Object relationships
    [Required]
    public string PrimaryObjectId { get; set; }
    [Required]
    public string PrimaryObjectType { get; set; }
    public string SecondaryObjectId { get; set; }
    public string SecondaryObjectType { get; set; }

    // Field-level tracking
    public string PrimaryFieldName { get; set; }
    public string PrimaryFieldDisplayName { get; set; }

    // Additional context
    public string Comments { get; set; }
    public string AdditionalInfo { get; set; }
}
```

## Audit Repository Implementation

### Comprehensive Audit Operations

```csharp
public class AuditLogRepository : IAuditLogRepository
{
    private readonly DatabaseContext databaseContext;

    public async Task<IReadOnlyCollection<AuditEntry>> GetAsync(Entity entity)
    {
        return await databaseContext.AuditLogs
            .AsNoTracking()
            .Where(l => l.PrimaryObjectId == entity.Id)
            .Where(l => l.PrimaryObjectType == entity.Type)
            .Select(ModelTransformExpressions.ToAuditEntry)
            .ToArrayAsync();
    }

    public async Task LogAuditAsync(Entity entity, Actor by, AuditType auditType)
    {
        Validate(entity, by);
        await LogAsync(entity, by, auditType);
    }

    public async Task LogAuditAsync(Entity entity, Actor by, Field field, Change change, AuditType auditType)
    {
        Validate(entity, by, field);
        await LogAsync(entity, by, field, change, auditType);
    }

    // Specialised audit methods
    public async Task LogAcceptAsync(Entity entity, Actor acceptedBy)
    {
        await LogAsync(entity, acceptedBy, AuditType.Accept);
    }

    public async Task LogRejectAsync(Entity entity, Actor rejectedBy)
    {
        await LogAsync(entity, rejectedBy, AuditType.Reject);
    }

    public async Task LogOnHoldAsync(Entity entity, Actor by)
    {
        await LogAsync(entity, by, AuditType.OnHold);
    }

    public async Task LogPushThroughAsync(Entity entity, Actor by)
    {
        await LogAsync(entity, by, AuditType.PushThrough);
    }

    // Bulk operations for performance
    public async Task LogCreatesAsync(IReadOnlyCollection<CreateFieldLog> createFieldLogs)
    {
        foreach (var createLog in createFieldLogs)
        {
            var log = ToAuditLog(
                createLog.PrimaryEntity,
                createLog.SecondaryEntity,
                createLog.By,
                createLog.Field,
                AuditType.Create,
                createLog.FieldDataType,
                createLog.Change);

            await databaseContext.AuditLogs.AddAsync(log);
        }

        await databaseContext.SaveChangesAsync();
    }

    public async Task LogUpdatesAsync(IReadOnlyCollection<UpdateFieldLog> updateFieldLogs)
    {
        foreach (var updateLog in updateFieldLogs)
        {
            var log = ToAuditLog(
                updateLog.PrimaryEntity,
                updateLog.SecondaryEntity,
                updateLog.By,
                updateLog.Field,
                AuditType.Update,
                updateLog.FieldDataType,
                updateLog.Change);

            await databaseContext.AuditLogs.AddAsync(log);
        }

        await databaseContext.SaveChangesAsync();
    }
}
```

### Generic Audit Integration

```csharp
public sealed class GenericAudit
{
    private readonly AuditLogRepository auditLogRepository;

    public async Task SaveAndAuditChanges(ETradingDbContext context, Entity parent, Actor updatedBy)
    {
        // Capture changes before saving
        var updateLogs = context.GetTrackedChangesForUpdate(updatedBy, parent);
        var deleteLogs = context.GetTrackedChangesForDelete(updatedBy, parent);
        var createLogs = context.GetTrackedChangesForCreate(updatedBy, parent);
        
        // Save business changes
        context.SaveChangesAndClearTracking();
        
        // Audit the changes asynchronously
        await this.UpdateAudit(updateLogs).ConfigureAwait(false);
        await this.DeleteAudit(deleteLogs).ConfigureAwait(false);
        await this.CreateAudit(createLogs).ConfigureAwait(false);
    }

    public async Task CreateAudit(List<CreateFieldLog> logs)
    {
        await this.auditLogRepository
            .LogCreatesAsync(logs)
            .ConfigureAwait(continueOnCapturedContext: false);
    }

    public async Task UpdateAudit(List<UpdateFieldLog> logs)
    {
        await this.auditLogRepository
            .LogUpdatesAsync(logs)
            .ConfigureAwait(continueOnCapturedContext: false);
    }

    public async Task DeleteAudit(List<DeleteFieldLog> logs)
    {
        await this.auditLogRepository
            .LogDeletesAsync(logs)
            .ConfigureAwait(continueOnCapturedContext: false);
    }
}
```

## Entity-Level Audit Trails

### Built-in Audit Fields on Business Entities

```csharp
public class TransactionHeader
{
    // Workflow state tracking
    public DateTime? OnHoldDate { get; set; }
    public DateTime? OnHoldReleaseDate { get; set; }
    public DateTime? SuspenseDate { get; set; }
    public DateTime? SuspenseReleaseDate { get; set; }
    public DateTime? MatchingDate { get; set; }
    public DateTime? MatchingReleaseDate { get; set; }
    public DateTime? ApprovalDate { get; set; }
    public DateTime? ApprovalReleaseDate { get; set; }

    // Document lifecycle
    public DateTime? DateDocumentAdded { get; set; }
    public DateTime? DocumentDate { get; set; }
    public DateTime? TransactionDate { get; set; }
    
    // Processing audit
    public DateTime? ExtractionDate { get; set; }
    public DateTime? ValidationDate { get; set; }
    public DateTime? ExportDate { get; set; }
}
```

### Change Tracking Models

```csharp
public class Change
{
    public Change(object oldValue, object newValue)
    : this(
      oldValue is DateTime oldDate ? oldDate.ToString("O") : oldValue?.ToString(),
      newValue is DateTime newDate ? newDate.ToString("O") : newValue?.ToString())
    {
    }

    public Change(string oldValue, string newValue)
    {
        OldValue = oldValue;
        NewValue = newValue;
    }

    public string OldValue { get; set; }
    public string NewValue { get; set; }
}

public class CreateFieldLog
{
    public Entity PrimaryEntity { get; set; }
    public Entity SecondaryEntity { get; set; }
    public Actor By { get; set; }
    public Field Field { get; set; }
    public Change Change { get; set; }
    public AuditDataType FieldDataType { get; set; }
}

public class UpdateFieldLog
{
    public Entity PrimaryEntity { get; set; }
    public Entity SecondaryEntity { get; set; }
    public Actor By { get; set; }
    public Field Field { get; set; }
    public Change Change { get; set; }
    public AuditDataType FieldDataType { get; set; }
}
```

## Technical Logging Infrastructure

### Custom Logger Implementation

```csharp
public static class Logger
{
    private static readonly string MessageFormat = "{0}|{1}|{2}|{3}";

    public enum EnumLogLevel
    {
        Off = 0,
        Trace = 1,
        Debug = 2,
        Info = 3,
        Warn = 4,
        Error = 5,
        Fatal = 6,
    }

    public static void Trace(string logfile, string message)
    {
        Log(logfile, message, EnumLogLevel.Trace);
    }

    public static void Debug(string logfile, string message)
    {
        Log(logfile, message, EnumLogLevel.Debug);
    }

    public static void Info(string logfile, string message)
    {
        Log(logfile, message, EnumLogLevel.Info);
    }

    public static void Warn(string logfile, string message)
    {
        Log(logfile, message, EnumLogLevel.Warn);
    }

    public static void Error(string logfile, string message)
    {
        Log(logfile, message, EnumLogLevel.Error);
    }

    public static void Fatal(string logfile, string message)
    {
        Log(logfile, message, EnumLogLevel.Fatal);
    }

    public static void Log(string logFile, string message, EnumLogLevel logLevel)
    {
        var fullMessage = string.Format(MessageFormat, DateTime.Now, logLevel, logFile, message);
        System.Diagnostics.Trace.WriteLine(fullMessage, logFile);
    }
}
```

### Specialised Export Logging

```csharp
public static class ExportLogging
{
    public static void Trace(string correlationId, string fileName, string message)
    {
        Logger.Trace("Exporting", $"{correlationId}|{fileName}|{message}");
    }

    public static void Info(string correlationId, string fileName, string message)
    {
        Logger.Info("Exporting", $"{correlationId}|{fileName}|{message}");
    }

    public static void Warn(string correlationId, string fileName, string message)
    {
        Logger.Warn("Exporting", $"{correlationId}|{fileName}|{message}");
    }

    public static void Error(string correlationId, string fileName, string message)
    {
        Logger.Error("Exporting", $"{correlationId}|{fileName}|{message}");
    }
}
```

## Audit Query and Reporting

### Audit Entry Model

```csharp
public class AuditEntry
{
    public int Id { get; set; }
    public AuditType AuditType { get; set; }
    public AuditDataType DataType { get; set; }
    public DateTime Timestamp { get; set; }
    public Actor By { get; set; }
    public Entity PrimaryEntity { get; set; }
    public Field PrimaryField { get; set; }
    public Change PrimaryFieldChange { get; set; }
    public Entity SecondaryEntity { get; set; }

    public string Title => $"{By.UserFullName} ({By.MemberName}) {AuditType.GetDisplayName()} {PrimaryEntity.Type}";

    public string Message => PrimaryField != null
        ? PrimaryFieldChange != null
            ? $"Changed {PrimaryField.FieldDisplayName} from {PrimaryFieldChange.OldValue} to {PrimaryFieldChange.NewValue}"
            : $"{PrimaryField.FieldDisplayName}"
        : string.Empty;
}
```

### Model Transform Expressions

```csharp
public class ModelTransformExpressions
{
    public static readonly Expression<Func<AuditLog, AuditEntry>> ToAuditEntry =
     (AuditLog l) => new AuditEntry
     {
         Id = l.Id,
         Timestamp = l.Timestamp,
         By = new Actor(l.UserId, l.UserFullname, l.MemberId, l.MemberName),
         AuditType = l.AuditType,
         DataType = l.DataType,
         PrimaryEntity = new Entity(l.PrimaryObjectId, l.PrimaryObjectType),
         PrimaryField = l.PrimaryFieldName != null
                    ? new Field(l.PrimaryFieldName, l.PrimaryFieldDisplayName)
                    : null,
         SecondaryEntity = l.SecondaryObjectId != null
                    ? new Entity(l.SecondaryObjectId, l.SecondaryObjectType)
                    : null,
         PrimaryFieldChange = l.OldValue != null || l.NewValue != null
                    ? new Change(l.OldValue, l.NewValue)
                    : null,
     };
}
```

## Compliance and Retention

### Audit Data Retention Policy

```mermaid
graph TB
    subgraph "Retention Tiers"
        HOT[Hot Storage<br/>0-1 Years<br/>Immediate Access]
        WARM[Warm Storage<br/>1-3 Years<br/>Quick Retrieval]
        COLD[Cold Storage<br/>3-7 Years<br/>Archive Access]
        FROZEN[Frozen Storage<br/>7+ Years<br/>Compliance Only]
    end
    
    subgraph "Data Movement"
        HOT --> WARM
        WARM --> COLD
        COLD --> FROZEN
    end
    
    subgraph "Access Patterns"
        HOT --> DAILY[Daily Operations]
        WARM --> MONTHLY[Monthly Reports]
        COLD --> ANNUAL[Annual Audits]
        FROZEN --> LEGAL[Legal Discovery]
    end
```

### Compliance Reporting

```csharp
public class ComplianceReportGenerator
{
    public async Task<ComplianceReport> GenerateSOXReport(DateTime fromDate, DateTime toDate, Guid? customerId = null)
    {
        var query = databaseContext.AuditLogs
            .Where(a => a.Timestamp >= fromDate && a.Timestamp <= toDate)
            .Where(a => a.AuditType == AuditType.Update || a.AuditType == AuditType.Delete);

        if (customerId.HasValue)
        {
            query = query.Where(a => a.PrimaryObjectType == "TransactionHeader" && 
                                   a.AdditionalInfo.Contains(customerId.ToString()));
        }

        var auditEntries = await query
            .Select(ModelTransformExpressions.ToAuditEntry)
            .ToListAsync();

        return new ComplianceReport
        {
            ReportType = "SOX Compliance",
            GeneratedDate = DateTime.UtcNow,
            FromDate = fromDate,
            ToDate = toDate,
            TotalEntries = auditEntries.Count,
            AuditEntries = auditEntries
        };
    }

    public async Task<ComplianceReport> GenerateGDPRReport(Guid userId, DateTime fromDate, DateTime toDate)
    {
        var auditEntries = await databaseContext.AuditLogs
            .Where(a => a.UserId == userId)
            .Where(a => a.Timestamp >= fromDate && a.Timestamp <= toDate)
            .Where(a => a.AuditType == AuditType.Read || a.AuditType == AuditType.Update)
            .Select(ModelTransformExpressions.ToAuditEntry)
            .ToListAsync();

        return new ComplianceReport
        {
            ReportType = "GDPR Data Access",
            GeneratedDate = DateTime.UtcNow,
            FromDate = fromDate,
            ToDate = toDate,
            UserId = userId,
            TotalEntries = auditEntries.Count,
            AuditEntries = auditEntries
        };
    }
}
```

## Performance Considerations

### Asynchronous Audit Processing

```csharp
public class AsyncAuditProcessor
{
    private readonly IBackgroundTaskQueue taskQueue;
    private readonly IServiceProvider serviceProvider;

    public async Task QueueAuditLog(AuditLog auditLog)
    {
        await taskQueue.QueueBackgroundWorkItemAsync(async token =>
        {
            using var scope = serviceProvider.CreateScope();
            var auditRepository = scope.ServiceProvider.GetRequiredService<AuditLogRepository>();
            
            await auditRepository.AddAsync(auditLog);
            await auditRepository.SaveChangesAsync();
        });
    }
}
```

### Audit Index Strategy

```sql
-- Primary audit query indexes
CREATE INDEX IX_AuditLog_PrimaryObject ON [Audit].[AuditLog] ([PrimaryObjectType], [PrimaryObjectId], [Timestamp])
CREATE INDEX IX_AuditLog_User ON [Audit].[AuditLog] ([UserId], [Timestamp])
CREATE INDEX IX_AuditLog_AuditType ON [Audit].[AuditLog] ([AuditType], [Timestamp])
CREATE INDEX IX_AuditLog_Timestamp ON [Audit].[AuditLog] ([Timestamp])

-- Compliance query indexes
CREATE INDEX IX_AuditLog_Compliance_SOX ON [Audit].[AuditLog] ([AuditType], [Timestamp]) WHERE [AuditType] IN (2, 3) -- Update, Delete
CREATE INDEX IX_AuditLog_Compliance_GDPR ON [Audit].[AuditLog] ([UserId], [AuditType], [Timestamp]) WHERE [AuditType] IN (1, 2) -- Read, Update
```

## Related Documents

- **[SecurityArchitecture.md](./SecurityArchitecture.md)** - Security implementation details
- **[DatabaseArchitecture.md](./DatabaseArchitecture.md)** - Database design and entities
- **[DataModelsAndEntities.md](./DataModelsAndEntities.md)** - Core business entities
- **[SystemOverview.md](./SystemOverview.md)** - High-level system architecture
- **[ConfigurationManagement.md](./ConfigurationManagement.md)** - Configuration and settings

---

*This document provides comprehensive coverage of the eHub auditing and logging architecture. Refer to the related documentation for detailed implementation guidance on specific components.*
