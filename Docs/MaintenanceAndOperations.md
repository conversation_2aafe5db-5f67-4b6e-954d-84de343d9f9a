# Maintenance and Operations Guide

## Purpose & Scope

This document provides comprehensive operational procedures for maintaining the eHub platform in production environments. It covers monitoring systems, health checks, routine maintenance tasks, performance optimisation, and operational best practices for system administrators and DevOps teams.

## Prerequisites

- **System Access**: Administrative access to production environments
- **Monitoring Tools**: Familiarity with Azure Monitor, Application Insights, and Hangfire Dashboard
- **Database Access**: SQL Server management capabilities
- **Azure Services**: Understanding of Azure Storage, App Services, and monitoring tools
- **Background Knowledge**: Review [SystemOverview.md](./SystemOverview.md) and [BuildAndDeployment.md](./BuildAndDeployment.md)

## Core Concepts

### Operational Architecture

```mermaid
graph TB
    subgraph "Monitoring Layer"
        MON[Azure Monitor] --> AI[Application Insights]
        MON --> LA[Log Analytics]
        MON --> AM[Alert Manager]
    end
    
    subgraph "Health Checks"
        HC[Health Controller] --> DB[Database Health]
        HC --> AS[Azure Storage Health]
        HC --> BJ[Background Jobs Health]
        HC --> INT[Integration Health]
    end
    
    subgraph "Operational Tasks"
        MT[Maintenance Tasks] --> DBM[Database Maintenance]
        MT --> FM[File Management]
        MT --> PM[Performance Monitoring]
        MT --> BU[Backup Operations]
    end
    
    subgraph "Alerting System"
        AL[Alert System] --> EMAIL[Email Alerts]
        AL --> SMS[SMS Notifications]
        AL --> DASH[Dashboard Alerts]
    end
    
    MON --> HC
    HC --> AL
    MT --> MON
```

### Key Operational Components

- **Health Monitoring**: Continuous system health assessment
- **Performance Metrics**: Real-time performance tracking
- **Automated Maintenance**: Scheduled maintenance tasks
- **Alert Management**: Proactive issue notification
- **Backup Systems**: Data protection and recovery

## Health Monitoring System

### Application Health Endpoints

The eHub platform provides comprehensive health check endpoints for monitoring system status:

```bash
# Primary API health check
curl https://api.ehub.com/health

# Database connectivity check
curl https://api.ehub.com/health/database

# Azure Storage health check
curl https://api.ehub.com/health/storage

# Background services health
curl https://hangfire.ehub.com/health

# Integration systems health
curl https://api.ehub.com/health/integrations
```

### Health Check Implementation

The system implements comprehensive health checks across all critical components:

```csharp
public class ComprehensiveHealthCheck : IHealthCheck
{
    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        var healthData = new Dictionary<string, object>();
        var overallHealthy = true;
        var issues = new List<string>();
        
        // Database health verification
        try
        {
            await CheckDatabaseHealthAsync();
            healthData["database"] = "healthy";
        }
        catch (Exception ex)
        {
            overallHealthy = false;
            issues.Add($"Database: {ex.Message}");
            healthData["database"] = "unhealthy";
        }
        
        // Azure Storage connectivity
        try
        {
            await CheckAzureStorageHealthAsync();
            healthData["storage"] = "healthy";
        }
        catch (Exception ex)
        {
            overallHealthy = false;
            issues.Add($"Storage: {ex.Message}");
            healthData["storage"] = "unhealthy";
        }
        
        // Background job processing
        try
        {
            await CheckBackgroundJobsHealthAsync();
            healthData["background_jobs"] = "healthy";
        }
        catch (Exception ex)
        {
            overallHealthy = false;
            issues.Add($"Background Jobs: {ex.Message}");
            healthData["background_jobs"] = "unhealthy";
        }
        
        // Memory usage monitoring
        var memoryUsage = GC.GetTotalMemory(false) / (1024 * 1024); // MB
        healthData["memory_usage_mb"] = memoryUsage;
        
        if (memoryUsage > 2048) // 2GB threshold
        {
            issues.Add($"High memory usage: {memoryUsage}MB");
        }
        
        return overallHealthy 
            ? HealthCheckResult.Healthy("All systems operational", healthData)
            : HealthCheckResult.Unhealthy(string.Join("; ", issues), data: healthData);
    }
}
```

### Integration Health Monitoring

```mermaid
graph LR
    subgraph "Integration Health Checks"
        IHC[Integration Health Controller] --> AS2[AS2 Health Check]
        IHC --> FTP[FTP Health Check]
        IHC --> EMAIL[Email Health Check]
        IHC --> API[API Health Check]
    end
    
    subgraph "Health Metrics"
        AS2 --> AS2M[Connection Status, Error Rates, Certificate Status]
        FTP --> FTPM[Connection Status, Transfer Rates, Authentication]
        EMAIL --> EM[SMTP Status, Queue Depth, Delivery Rates]
        API --> APIM[Response Times, Error Rates, Availability]
    end
    
    subgraph "Alert Conditions"
        AS2M --> AS2A[Certificate Expiry, High Error Rate]
        FTPM --> FTPA[Connection Failures, Transfer Failures]
        EM --> EA[Queue Backlog, Delivery Failures]
        APIM --> APIA[High Latency, Service Unavailable]
    end
```

## Performance Monitoring

### Key Performance Indicators (KPIs)

Monitor these critical metrics for optimal system performance:

#### Application Performance
- **API Response Times**: Target < 2 seconds for 95th percentile
- **Database Query Performance**: Average query time < 500ms
- **Memory Usage**: Keep below 80% of allocated memory
- **CPU Utilisation**: Maintain below 70% average usage
- **Disk I/O**: Monitor for bottlenecks in document processing

#### Background Job Performance
- **Job Processing Rate**: Monitor jobs per minute
- **Queue Depth**: Alert when queues exceed normal thresholds
- **Job Failure Rate**: Target < 1% failure rate
- **Processing Latency**: Time from job creation to completion

#### Integration Performance
- **AS2 Message Processing**: Success rate and processing time
- **FTP Transfer Rates**: Upload/download performance
- **Email Delivery**: Queue processing and delivery rates
- **API Integration**: External service response times

### Performance Monitoring Dashboard

```mermaid
graph TB
    subgraph "Performance Dashboard"
        PD[Performance Dashboard] --> RT[Real-time Metrics]
        PD --> HT[Historical Trends]
        PD --> AL[Active Alerts]
        PD --> PR[Performance Reports]
    end
    
    subgraph "Metric Sources"
        AI[Application Insights] --> PD
        HF[Hangfire Dashboard] --> PD
        AM[Azure Monitor] --> PD
        DB[Database Metrics] --> PD
    end
    
    subgraph "Alert Triggers"
        TH[Threshold Alerts] --> EMAIL[Email Notifications]
        TH --> SMS[SMS Alerts]
        TH --> SLACK[Slack Notifications]
    end
    
    PD --> TH
```

## Routine Maintenance Tasks

### Daily Operations Checklist

#### Morning Health Check (09:00 GMT)
- [ ] **System Health**: Verify all health endpoints return healthy status
- [ ] **Background Jobs**: Check Hangfire dashboard for failed jobs
- [ ] **Database Performance**: Review slow query reports
- [ ] **Storage Usage**: Monitor Azure Storage consumption
- [ ] **Integration Status**: Verify AS2, FTP, and Email integrations
- [ ] **Error Logs**: Review overnight error logs for issues

#### Evening Review (17:00 GMT)
- [ ] **Daily Processing**: Verify document processing volumes
- [ ] **Performance Metrics**: Review daily performance reports
- [ ] **Backup Status**: Confirm automated backups completed
- [ ] **Security Alerts**: Check for any security-related alerts
- [ ] **Capacity Planning**: Monitor resource utilisation trends

### Weekly Maintenance Tasks

#### Monday: System Review
- [ ] **Performance Analysis**: Weekly performance trend analysis
- [ ] **Capacity Planning**: Resource utilisation review
- [ ] **Security Updates**: Check for available security patches
- [ ] **Documentation Updates**: Update operational logs

#### Wednesday: Database Maintenance
- [ ] **Index Optimisation**: Rebuild fragmented indexes
- [ ] **Statistics Update**: Update database statistics
- [ ] **Backup Verification**: Test backup restore procedures
- [ ] **Query Performance**: Analyse slow query reports

#### Friday: Integration Testing
- [ ] **AS2 Connectivity**: Test AS2 partner connections
- [ ] **FTP Services**: Verify FTP server connectivity
- [ ] **Email Services**: Test email delivery systems
- [ ] **API Endpoints**: Validate external API integrations

### Monthly Maintenance Tasks

#### First Monday of Month: Comprehensive Review
- [ ] **System Audit**: Complete system health audit
- [ ] **Performance Baseline**: Update performance baselines
- [ ] **Security Review**: Comprehensive security assessment
- [ ] **Disaster Recovery**: Test disaster recovery procedures
- [ ] **Documentation Review**: Update operational documentation

#### Third Monday of Month: Optimisation
- [ ] **Database Optimisation**: Comprehensive database tuning
- [ ] **Storage Cleanup**: Archive old documents and logs
- [ ] **Performance Tuning**: Application performance optimisation
- [ ] **Capacity Planning**: Resource scaling assessment

## Monitoring and Alerting Configuration

### Alert Thresholds

Configure alerts for the following conditions:

#### Critical Alerts (Immediate Response Required)
- **System Down**: Any health endpoint returns unhealthy
- **Database Connectivity**: Database connection failures
- **High Error Rate**: Error rate > 5% over 5 minutes
- **Memory Usage**: Memory usage > 90%
- **Disk Space**: Available disk space < 10%

#### Warning Alerts (Response Within 1 Hour)
- **Performance Degradation**: Response time > 5 seconds
- **Background Job Failures**: Job failure rate > 2%
- **Queue Backlog**: Job queue depth > 1000 items
- **Integration Issues**: AS2/FTP/Email connectivity problems
- **Certificate Expiry**: SSL certificates expiring within 30 days

#### Information Alerts (Daily Review)
- **High Volume**: Document processing volume > 150% of baseline
- **Slow Queries**: Database queries > 2 seconds
- **Storage Growth**: Storage usage growth > 20% per week
- **User Activity**: Unusual user activity patterns

### Alert Configuration Example

```json
{
  "alertRules": [
    {
      "name": "API Response Time Alert",
      "condition": "avg(response_time) > 5000ms over 5 minutes",
      "severity": "Warning",
      "actions": ["email", "slack"],
      "recipients": ["<EMAIL>"]
    },
    {
      "name": "Database Connection Alert",
      "condition": "database_connection_failures > 0",
      "severity": "Critical",
      "actions": ["email", "sms", "slack"],
      "recipients": ["<EMAIL>", "+44XXXXXXXXX"]
    },
    {
      "name": "Background Job Failure Alert",
      "condition": "job_failure_rate > 2% over 10 minutes",
      "severity": "Warning",
      "actions": ["email", "slack"],
      "recipients": ["<EMAIL>"]
    }
  ]
}
```

## Backup and Recovery Operations

### Automated Backup Schedule

#### Database Backups
- **Full Backup**: Daily at 02:00 GMT
- **Differential Backup**: Every 6 hours
- **Transaction Log Backup**: Every 15 minutes
- **Retention**: 30 days for full backups, 7 days for differentials

#### File System Backups
- **Document Storage**: Daily incremental backup
- **Configuration Files**: Daily backup with version control
- **Application Logs**: Weekly archive to long-term storage
- **Retention**: 90 days for documents, 365 days for configurations

### Backup Verification Procedures

```bash
# Daily backup verification script
#!/bin/bash

# Check database backup completion
sqlcmd -S production-server -Q "
SELECT 
    database_name,
    backup_finish_date,
    type,
    backup_size
FROM msdb.dbo.backupset 
WHERE backup_finish_date >= DATEADD(day, -1, GETDATE())
ORDER BY backup_finish_date DESC"

# Verify Azure Storage backup
az storage blob list --container-name backups --account-name ehubstorage

# Test backup restore (to test environment)
sqlcmd -S test-server -Q "
RESTORE VERIFYONLY 
FROM DISK = 'C:\Backup\eHub_Production_latest.bak'"
```

### Recovery Procedures

#### Database Recovery
```sql
-- Point-in-time recovery example
RESTORE DATABASE eHub_Production 
FROM DISK = 'C:\Backup\eHub_Production_full.bak' 
WITH NORECOVERY, REPLACE;

RESTORE DATABASE eHub_Production 
FROM DISK = 'C:\Backup\eHub_Production_diff.bak' 
WITH NORECOVERY;

RESTORE LOG eHub_Production 
FROM DISK = 'C:\Backup\eHub_Production_log.trn' 
WITH STOPAT = '2024-01-15 14:30:00';
```

#### Application Recovery
```bash
# Application rollback procedure
# 1. Stop services
systemctl stop ehub-api
systemctl stop ehub-hangfire

# 2. Restore application files
cp -r /backup/ehub-app-v1.2.3/* /opt/ehub/

# 3. Restore configuration
cp /backup/config/appsettings.production.json /opt/ehub/

# 4. Start services
systemctl start ehub-api
systemctl start ehub-hangfire

# 5. Verify functionality
curl https://api.ehub.com/health
```

## Log Management

### Log Categories and Locations

#### Application Logs
- **API Logs**: `/var/log/ehub/api/`
- **Background Job Logs**: `/var/log/ehub/hangfire/`
- **Integration Logs**: `/var/log/ehub/integrations/`
- **Security Logs**: `/var/log/ehub/security/`

#### System Logs
- **IIS Logs**: `C:\inetpub\logs\LogFiles\`
- **Windows Event Logs**: Event Viewer → Applications and Services
- **Azure Diagnostics**: Azure Monitor → Log Analytics

### Log Monitoring Patterns

Monitor logs for these critical patterns:

```bash
# Critical error patterns
grep -E "(ERROR|EXCEPTION|FATAL|TIMEOUT|FAILED)" /var/log/ehub/api/*.log

# Security-related patterns
grep -E "(UNAUTHORIZED|FORBIDDEN|AUTHENTICATION|LOGIN)" /var/log/ehub/security/*.log

# Performance issues
grep -E "(SLOW|TIMEOUT|DEADLOCK|MEMORY)" /var/log/ehub/api/*.log

# Integration failures
grep -E "(AS2|FTP|EMAIL).*FAILED" /var/log/ehub/integrations/*.log
```

### Log Retention Policy

- **Application Logs**: 30 days local, 90 days archived
- **Security Logs**: 365 days (compliance requirement)
- **Performance Logs**: 90 days
- **Integration Logs**: 60 days
- **Debug Logs**: 7 days (development environments only)

## Troubleshooting Procedures

### Common Issues and Resolutions

#### High Memory Usage
```bash
# Identify memory-consuming processes
ps aux --sort=-%mem | head -20

# Check application memory usage
dotnet-counters monitor --process-id <pid> --counters System.Runtime

# Restart services if necessary
systemctl restart ehub-api
```

#### Database Performance Issues
```sql
-- Identify slow queries
SELECT TOP 10
    total_elapsed_time/execution_count AS avg_elapsed_time,
    total_logical_reads/execution_count AS avg_logical_reads,
    execution_count,
    SUBSTRING(st.text, (qs.statement_start_offset/2)+1,
        ((CASE qs.statement_end_offset
            WHEN -1 THEN DATALENGTH(st.text)
            ELSE qs.statement_end_offset
        END - qs.statement_start_offset)/2) + 1) AS statement_text
FROM sys.dm_exec_query_stats qs
CROSS APPLY sys.dm_exec_sql_text(qs.sql_handle) st
ORDER BY total_elapsed_time/execution_count DESC;

-- Check for blocking processes
SELECT 
    blocking_session_id,
    session_id,
    wait_type,
    wait_time,
    wait_resource
FROM sys.dm_exec_requests
WHERE blocking_session_id <> 0;
```

#### Background Job Issues
```csharp
// Check Hangfire job status
public class JobMonitoringService
{
    public async Task<JobHealthReport> GetJobHealthAsync()
    {
        var monitoring = JobStorage.Current.GetMonitoringApi();
        
        var failedJobs = monitoring.FailedJobs(0, 100);
        var processingJobs = monitoring.ProcessingJobs(0, 100);
        var enqueuedJobs = monitoring.EnqueuedJobs("default", 0, 100);
        
        return new JobHealthReport
        {
            FailedJobCount = failedJobs.Count,
            ProcessingJobCount = processingJobs.Count,
            EnqueuedJobCount = enqueuedJobs.Count,
            IsHealthy = failedJobs.Count < 10 && processingJobs.Count < 50
        };
    }
}
```

## Security Operations

### Security Monitoring

#### Daily Security Checks
- [ ] **Failed Login Attempts**: Review authentication failures
- [ ] **Unauthorised Access**: Check for access violations
- [ ] **Certificate Status**: Verify SSL certificate validity
- [ ] **Security Updates**: Check for available security patches
- [ ] **Audit Logs**: Review security audit trails

#### Weekly Security Tasks
- [ ] **Vulnerability Scanning**: Run automated security scans
- [ ] **Access Review**: Review user access permissions
- [ ] **Security Metrics**: Analyse security incident trends
- [ ] **Compliance Check**: Verify regulatory compliance status

### Incident Response Procedures

#### Security Incident Classification
- **Critical**: Data breach, system compromise, unauthorised access
- **High**: Failed security controls, suspicious activity
- **Medium**: Policy violations, configuration issues
- **Low**: Informational security events

#### Response Procedures
1. **Immediate Response** (0-15 minutes)
   - Isolate affected systems
   - Preserve evidence
   - Notify security team

2. **Assessment** (15-60 minutes)
   - Determine scope of incident
   - Assess potential impact
   - Document findings

3. **Containment** (1-4 hours)
   - Implement containment measures
   - Prevent further damage
   - Maintain business continuity

4. **Recovery** (4-24 hours)
   - Restore affected systems
   - Verify system integrity
   - Resume normal operations

5. **Post-Incident** (24-72 hours)
   - Conduct lessons learned review
   - Update security procedures
   - Implement preventive measures

## Best Practices

### Operational Excellence

#### Monitoring Best Practices
- **Proactive Monitoring**: Monitor trends, not just thresholds
- **Meaningful Alerts**: Reduce alert fatigue with intelligent filtering
- **Documentation**: Maintain up-to-date runbooks and procedures
- **Automation**: Automate routine tasks where possible
- **Testing**: Regularly test monitoring and alerting systems

#### Performance Optimisation
- **Baseline Establishment**: Maintain performance baselines
- **Continuous Monitoring**: Monitor performance trends continuously
- **Proactive Scaling**: Scale resources before reaching limits
- **Regular Tuning**: Perform regular performance tuning
- **Capacity Planning**: Plan for future growth and capacity needs

#### Change Management
- **Change Control**: Follow established change control procedures
- **Testing**: Test all changes in non-production environments
- **Rollback Plans**: Maintain rollback procedures for all changes
- **Documentation**: Document all changes and their impact
- **Communication**: Communicate changes to stakeholders

### Maintenance Scheduling

#### Planned Maintenance Windows
- **Weekly Maintenance**: Sundays 02:00-04:00 GMT
- **Monthly Maintenance**: First Sunday 01:00-05:00 GMT
- **Emergency Maintenance**: As required with stakeholder notification

#### Maintenance Procedures
1. **Pre-Maintenance**
   - Notify stakeholders
   - Create system backup
   - Prepare rollback procedures
   - Verify maintenance steps

2. **During Maintenance**
   - Follow documented procedures
   - Monitor system status
   - Document any issues
   - Test functionality

3. **Post-Maintenance**
   - Verify system functionality
   - Monitor for issues
   - Update documentation
   - Notify stakeholders of completion

## Common Issues

### Performance Issues

#### Slow API Response Times
**Symptoms**: API endpoints responding slowly (>5 seconds)
**Diagnosis**:
```bash
# Check application performance
curl -w "@curl-format.txt" -o /dev/null -s "https://api.ehub.com/health"

# Monitor database performance
sqlcmd -S server -Q "SELECT * FROM sys.dm_exec_requests WHERE status = 'running'"
```
**Resolution**:
- Check database query performance
- Review application logs for errors
- Monitor memory and CPU usage
- Consider scaling resources

#### High Memory Usage
**Symptoms**: Application consuming excessive memory
**Diagnosis**:
```bash
# Check memory usage
free -h
ps aux --sort=-%mem | head -10
```
**Resolution**:
- Restart application services
- Check for memory leaks in logs
- Review garbage collection metrics
- Consider increasing available memory

### Integration Issues

#### AS2 Connection Failures
**Symptoms**: AS2 messages failing to send/receive
**Diagnosis**:
- Check AS2 partner connectivity
- Verify certificate validity
- Review AS2 error logs
**Resolution**:
- Restart AS2 services
- Update certificates if expired
- Contact AS2 partners if needed

#### Background Job Failures
**Symptoms**: High number of failed background jobs
**Diagnosis**:
- Check Hangfire dashboard
- Review job error logs
- Verify database connectivity
**Resolution**:
- Restart Hangfire services
- Retry failed jobs manually
- Fix underlying issues causing failures

## Related Documents

- **[SystemOverview.md](./SystemOverview.md)** - High-level system architecture and components
- **[BuildAndDeployment.md](./BuildAndDeployment.md)** - Build processes and deployment procedures
- **[TroubleshootingGuide.md](./TroubleshootingGuide.md)** - Detailed troubleshooting procedures
- **[PerformanceAndScaling.md](./PerformanceAndScaling.md)** - Performance optimisation strategies
- **[SecurityArchitecture.md](./SecurityArchitecture.md)** - Security implementation and procedures
- **[BackgroundJobProcessing.md](./BackgroundJobProcessing.md)** - Background job management
- **[IntegrationSystems.md](./IntegrationSystems.md)** - Integration monitoring and management
- **[AuditingAndLogging.md](./AuditingAndLogging.md)** - Logging and audit procedures

---

*This document provides comprehensive operational guidance for maintaining the eHub platform. Regular review and updates ensure operational procedures remain current with system changes and best practices.*
